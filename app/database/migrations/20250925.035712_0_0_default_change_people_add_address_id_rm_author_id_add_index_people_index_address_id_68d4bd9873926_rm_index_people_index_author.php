<?php

declare(strict_types=1);

namespace Migration;

use Cycle\Migrations\Migration;

class OrmDefault046170b1daab1a92e8db4a9339fbe72a extends Migration
{
    protected const DATABASE = 'default';

    public function up(): void
    {
        $this->table('people')
        ->addColumn('address_id', 'uuid', ['nullable' => true, 'defaultValue' => null, 'comment' => ''])
        ->dropColumn('author_id')
        ->addIndex(['address_id'], ['name' => 'people_index_address_id_68d4bd9873926', 'unique' => false])
        ->dropIndex(['author_id'])
        ->addForeignKey(['address_id'], 'addresses', ['id'], [
            'name' => 'people_foreign_address_id_68d4bd9873958',
            'delete' => 'CASCADE',
            'update' => 'CASCADE',
            'indexCreate' => true,
        ])
        ->dropForeign<PERSON>ey(['author_id'])
        ->update();
    }

    public function down(): void
    {
        $this->table('people')
        ->addForeignKey(['author_id'], 'addresses', ['id'], [
            'name' => 'people_author_id_fk',
            'delete' => 'CASCADE',
            'update' => 'CASCADE',
            'indexCreate' => true,
        ])
        ->dropForeignKey(['address_id'])
        ->addIndex(['author_id'], ['name' => 'people_index_author_id_68d1aec1eab92', 'unique' => false])
        ->dropIndex(['address_id'])
        ->addColumn('author_id', 'unknown', ['nullable' => true, 'defaultValue' => null, 'comment' => ''])
        ->dropColumn('address_id')
        ->update();
    }
}
