<?php

declare(strict_types=1);

namespace Migration;

use Cycle\Migrations\Migration;

class OrmDefault864315a026ad685ed0cd04a1c6a6a448 extends Migration
{
    protected const DATABASE = 'default';

    public function up(): void
    {
        $this->table('conversations')
        ->addColumn('organization_id', 'uuid', ['nullable' => true, 'defaultValue' => null, 'comment' => ''])
        ->addIndex(['organization_id'], ['name' => 'conversations_index_organization_id_68d4b838cf3a3', 'unique' => false])
        ->addForeignKey(['organization_id'], 'organizations', ['id'], [
            'name' => 'conversations_foreign_organization_id_68d4b838cf3d5',
            'delete' => 'CASCADE',
            'update' => 'CASCADE',
            'indexCreate' => true,
        ])
        ->update();
    }

    public function down(): void
    {
        $this->table('conversations')
        ->dropForeignKey(['organization_id'])
        ->dropIndex(['organization_id'])
        ->dropColumn('organization_id')
        ->update();
    }
}
