<?php

declare(strict_types=1);

namespace Migration;

use Cycle\Migrations\Migration;

class OrmDefaultAee31c296649e3a74d7ed145bd81307d extends Migration
{
    protected const DATABASE = 'default';

    public function up(): void
    {
        $this->table('auto_responders')
        ->alterColumn('response_delay', 'enum', [
            'nullable' => false,
            'defaultValue' => '0',
            'values' => ['0', '1-30', '30-60', '60-300', '300-600', '600-1800', '1800-3600', 'random'],
            'size' => 9,
            'comment' => '',
        ])
        ->update();
        $this->table('conversations')
        ->alterColumn('organization_id', 'uuid', ['nullable' => true, 'defaultValue' => null, 'comment' => ''])
        ->update();
        $this->table('calendar_integrations')
        ->addColumn('id', 'uuid', ['nullable' => false, 'defaultValue' => null, 'comment' => ''])
        ->addColumn('created_at', 'datetime', [
            'nullable' => false,
            'defaultValue' => 'CURRENT_TIMESTAMP',
            'withTimezone' => false,
            'comment' => '',
        ])
        ->addColumn('updated_at', 'datetime', [
            'nullable' => true,
            'defaultValue' => null,
            'withTimezone' => false,
            'comment' => '',
        ])
        ->addColumn('deleted_at', 'datetime', [
            'nullable' => true,
            'defaultValue' => null,
            'withTimezone' => false,
            'comment' => '',
        ])
        ->addColumn('provider', 'string', ['nullable' => false, 'defaultValue' => null, 'size' => 255, 'comment' => ''])
        ->addColumn('name', 'string', ['nullable' => false, 'defaultValue' => null, 'size' => 255, 'comment' => ''])
        ->addColumn('credentials', 'json', ['nullable' => false, 'defaultValue' => null, 'comment' => ''])
        ->addColumn('settings', 'json', ['nullable' => true, 'defaultValue' => null, 'comment' => ''])
        ->addColumn('status', 'enum', [
            'nullable' => false,
            'defaultValue' => 'active',
            'values' => ['active', 'inactive', 'error'],
            'size' => 8,
            'comment' => '',
        ])
        ->addColumn('last_sync_at', 'datetime', [
            'nullable' => true,
            'defaultValue' => null,
            'withTimezone' => false,
            'comment' => '',
        ])
        ->addColumn('last_sync_error', 'string', ['nullable' => true, 'defaultValue' => null, 'size' => 255, 'comment' => ''])
        ->addColumn('sync_enabled', 'boolean', ['nullable' => false, 'defaultValue' => true, 'comment' => ''])
        ->addColumn('bidirectional_sync', 'boolean', ['nullable' => false, 'defaultValue' => true, 'comment' => ''])
        ->addColumn('organization_id', 'uuid', ['nullable' => false, 'defaultValue' => null, 'comment' => ''])
        ->addColumn('user_id', 'uuid', ['nullable' => true, 'defaultValue' => null, 'comment' => ''])
        ->addIndex(['organization_id'], [
            'name' => 'calendar_integrations_index_organization_id_68d54e8b91198',
            'unique' => false,
        ])
        ->addIndex(['user_id'], ['name' => 'calendar_integrations_index_user_id_68d54e8b912a7', 'unique' => false])
        ->addForeignKey(['organization_id'], 'organizations', ['id'], [
            'name' => '8ad04090fca766ccc80028512d99de39',
            'delete' => 'CASCADE',
            'update' => 'CASCADE',
            'indexCreate' => true,
        ])
        ->addForeignKey(['user_id'], 'users', ['id'], [
            'name' => 'calendar_integrations_foreign_user_id_68d54e8b912e9',
            'delete' => 'CASCADE',
            'update' => 'CASCADE',
            'indexCreate' => true,
        ])
        ->setPrimaryKeys(['id'])
        ->create();
    }

    public function down(): void
    {
        $this->table('calendar_integrations')->drop();
        $this->table('conversations')
        ->alterColumn('organization_id', 'unknown', ['nullable' => true, 'defaultValue' => null, 'comment' => ''])
        ->update();
        $this->table('auto_responders')
        ->alterColumn('response_delay', 'enum', [
            'nullable' => false,
            'defaultValue' => '0',
            'values' => [
                '0',
                ' \'1-30\'::character varying',
                ' \'30-60\'::character varying',
                ' \'60-300\'::character varying',
                ' \'300-600\'::character varying',
                ' \'600-1800\'::character varying',
                ' \'1800-3600\'::character varying',
                'random',
            ],
            'size' => 9,
            'comment' => '',
        ])
        ->update();
    }
}
