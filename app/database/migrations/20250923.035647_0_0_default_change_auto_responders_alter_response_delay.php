<?php

declare(strict_types=1);

namespace Migration;

use Cycle\Migrations\Migration;

class OrmDefaultFfadadbea7041e7fd318859b0e349355 extends Migration
{
    protected const DATABASE = 'default';

    public function up(): void
    {
        $this->table('auto_responders')
        ->alterColumn('response_delay', 'enum', [
            'nullable' => false,
            'defaultValue' => '0',
            'values' => ['0', '1-30', '30-60', '60-300', '300-600', '600-1800', '1800-3600', 'random'],
            'size' => 9,
            'comment' => '',
        ])
        ->update();
    }

    public function down(): void
    {
        $this->table('auto_responders')
        ->alterColumn('response_delay', 'enum', [
            'nullable' => false,
            'defaultValue' => '0',
            'values' => [
                '0',
                ' \'1-30\'::character varying',
                ' \'30-60\'::character varying',
                ' \'60-300\'::character varying',
                ' \'300-600\'::character varying',
                ' \'600-1800\'::character varying',
                ' \'1800-3600\'::character varying',
                'random',
            ],
            'size' => 9,
            'comment' => '',
        ])
        ->update();
    }
}
