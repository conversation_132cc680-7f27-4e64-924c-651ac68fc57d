<?php

namespace App\Endpoint\Web\DataGrid;

use App\Database\Entity\Call;
use App\Endpoint\Web\View\CallView;
use Spiral\DataGrid\GridSchema;
use Spiral\DataGrid\Specification\Filter\Equals;
use Spiral\DataGrid\Specification\Pagination\PagePaginator;
use Spiral\DataGrid\Specification\Sorter\DescSorter;
use Spiral\DataGrid\Specification\Sorter\SorterSet;
use Spiral\DataGrid\Specification\Value\BoolValue;
use Spiral\DataGrid\Specification\Value\StringValue;
use Spiral\DataGrid\Specification\Value\UuidValue;

class CallGridSchema extends GridSchema
{
    public function __construct(
        private readonly CallView $callView
    ) {
        $this->addFilter('lead', new Equals('lead.id', new UuidValue()));
        $this->addFilter('agent', new Equals('agent.id', new UuidValue()));
        $this->addFilter('conversation', new Equals('conversation.id', new UuidValue()));
        $this->addFilter('answered', new Equals('answered', new BoolValue()));
        $this->addFilter('status', new Equals('status', new StringValue()));

        $this->addSorter('default', new SorterSet(
            new DescSorter('createdAt'),
        ));

        $this->setPaginator(new PagePaginator(12, [24, 48]));
    }

    public function __invoke(Call $call)
    {
        return $this->callView->map($call);
    }
}
