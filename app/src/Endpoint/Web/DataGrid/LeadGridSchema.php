<?php

namespace App\Endpoint\Web\DataGrid;

use App\Database\Entity\Lead;
use App\Database\Enum\LeadStatus;
use App\Endpoint\Web\View\LeadView;
use Spiral\DataGrid\GridSchema;
use Spiral\DataGrid\Specification\Filter\Any;
use Spiral\DataGrid\Specification\Filter\Equals;
use Spiral\DataGrid\Specification\Filter\InArray;
use Spiral\DataGrid\Specification\Filter\Like;
use Spiral\DataGrid\Specification\Pagination\PagePaginator;
use Spiral\DataGrid\Specification\Sorter\AscSorter;
use Spiral\DataGrid\Specification\Sorter\SorterSet;
use Spiral\DataGrid\Specification\Value\EnumValue;
use Spiral\DataGrid\Specification\Value\NumericValue;
use Spiral\DataGrid\Specification\Value\StringValue;
use Spiral\DataGrid\Specification\Value\UuidValue;

class LeadGridSchema extends GridSchema
{
    public function __construct(
        private readonly LeadView $leadView
    ){
        $this->addFilter('status', new Equals('status', new StringValue()));
        $this->addFilter('organization', new Equals('organization.id', new UuidValue()));
        $this->addFilter('agent', new Equals('agent.id', new UuidValue()));
        $this->addFilter('search', new Any(
            new Like('person.firstName', new StringValue()),
            new Like('person.lastName', new StringValue()),
            new Like('person.phoneNumber.number', new NumericValue()),
            new Like('person.email', new StringValue()),
        ));

        $this->addSorter('default',new SorterSet(
            new AscSorter('status'),
            new AscSorter('name'),
        ));

        $this->setPaginator(new PagePaginator(12, [24, 48]));
    }

    public function __invoke(Lead $lead)
    {
        return $this->leadView->map($lead);
    }
}
