<?php

namespace App\Endpoint\Web\DataGrid;

use App\Database\Entity\Conversation;
use App\Endpoint\Web\Filter\ConversationSearchFilter;
use App\Endpoint\Web\View\ConversationView;
use Spiral\DataGrid\GridSchema;
use Spiral\DataGrid\Specification\Filter\Any;
use Spiral\DataGrid\Specification\Filter\Equals;
use Spiral\DataGrid\Specification\Filter\Like;
use Spiral\DataGrid\Specification\Pagination\PagePaginator;
use Spiral\DataGrid\Specification\Sorter\DescSorter;
use Spiral\DataGrid\Specification\Sorter\Sorter;
use Spiral\DataGrid\Specification\Value\StringValue;
use Spiral\DataGrid\Specification\Value\UuidValue;
use Spiral\Prototype\Annotation\Prototyped;

#[Prototyped(property: 'conversationGridSchema')]
class ConversationGridSchema extends GridSchema
{
    public function __construct(
        private readonly ConversationView $conversationView
    )
    {
        // Filters
        $this->addFilter('lead', new Equals('lead.id', new UuidValue()));
        $this->addFilter('agent', new Equals('agent.id', new UuidValue()));
        $this->addFilter('status', new Equals('status', new StringValue()));
        $this->addFilter('search', new Any(
            new Like('lead.person.firstName', new StringValue()),
            new Like('lead.person.lastName', new StringValue()),
            new Like('messages.content', new StringValue()),
        ));

        // Sorters
        $this->addSorter('createdAt', new Sorter('created_at'));
        $this->addSorter('lastMessageTime', new DescSorter('messages.created_at'));
        $this->addSorter('-lastMessageTime', new DescSorter('messages.created_at'));

        // Pagination
        $this->setPaginator(new PagePaginator(20, [50, 100]));
    }

    public function __invoke(Conversation $conversation)
    {
        return $this->conversationView->map($conversation);
    }
}
