<?php

namespace App\Endpoint\Web\DataGrid;

use App\Database\Entity\Appointment;
use App\Endpoint\Web\View\AppointmentView;
use Spiral\DataGrid\GridSchema;
use Spiral\DataGrid\Specification\Filter\Equals;
use Spiral\DataGrid\Specification\Pagination\PagePaginator;
use Spiral\DataGrid\Specification\Sorter\DescSorter;
use Spiral\DataGrid\Specification\Sorter\SorterSet;
use Spiral\DataGrid\Specification\Value\StringValue;
use Spiral\DataGrid\Specification\Value\UuidValue;

class AppointmentGridSchema extends GridSchema
{
    public function __construct(
        private readonly AppointmentView $appointmentView
    ) {
        $this->addFilter('lead', new Equals('lead.id', new UuidValue()));
        $this->addFilter('agent', new Equals('agent.id', new UuidValue()));
        $this->addFilter('conversation', new Equals('conversation.id', new UuidValue()));
        $this->addFilter('status', new Equals('status', new StringValue()));

        $this->addSorter('default', new SorterSet(
            new DescSorter('startTime'),
        ));

        $this->setPaginator(new PagePaginator(12, [24, 48]));
    }

    public function __invoke(Appointment $appointment)
    {
        return $this->appointmentView->map($appointment);
    }
}
