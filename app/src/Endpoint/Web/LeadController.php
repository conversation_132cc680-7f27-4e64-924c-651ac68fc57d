<?php

namespace App\Endpoint\Web;

use App\Database\Entity\Lead;
use App\Endpoint\Web\DataGrid\LeadGridSchema;
use Cycle\ORM\Select;
use Psr\Http\Message\ResponseInterface;
use Spiral\DataGrid\Annotation\DataGrid;
use Spiral\Prototype\Traits\PrototypeTrait;
use Spiral\Router\Annotation\Route;

final class LeadController
{
    use PrototypeTrait;

    #[Route(route: '/lead', name: 'lead.list', methods: 'GET', group: 'v1')]
    #[DataGrid(grid: LeadGridSchema::class)]
    public function list(): Select
    {
        return $this->leads->select()->load('notes')->load('agent');
    }

    #[Route(route: '/lead/<lead>', name: 'lead.get', methods: 'GET', group: 'v1')]
    public function get(Lead $lead)
    {
        // Load the lead with all necessary relationships
        $leadWithRelations = $this->leads->select()
            ->where('id', $lead->id)
            ->load('person')
            ->load('agent.leads')
            ->load('agent.phoneNumber')
            ->load('phoneNumber')
            ->load('organization')
            ->fetchOne();

        return $this->leadView->json($leadWithRelations);
    }

    #[Route(route: '/lead/<lead:uuid>/metrics', name: 'lead.metrics', methods: 'GET', group: 'v1')]
    public function metrics(Lead $lead): ResponseInterface
    {
        return $this->response->json([
            'status' => 200,
            'data' => [
                'messageCount' => $lead->calculateMessageCount(),
                'callCount' => $lead->calculateCallCount(),
                'voicemailCount' => $lead->calculateVoicemailCount(),
                'appointmentCount' => $lead->calculateAppointmentCount(),
                'leadScore' => $lead->calculateLeadScore(),
                'averageResponseTime' => $lead->calculateAverageResponseTime(),
                'totalCost' => $lead->calculateTotalCost(),
                'lastContact' => $lead->updatedAt?->format('Y-m-d H:i:s'),
            ]
        ]);
    }

    #[Route(route: '/lead/<lead:uuid>/analysis', name: 'lead.analysis', methods: 'GET', group: 'v1')]
    public function analysis(Lead $lead): ResponseInterface
    {
        return $this->response->json([
            'status' => 200,
            'data' => $lead->calculateAiAnalysis()
        ]);
    }

    #[Route(route: '/lead/<lead:uuid>/related', name: 'lead.related', methods: 'GET', group: 'v1')]
    public function related(Lead $lead): ResponseInterface
    {
        // Get other leads associated with the same person
        $relatedLeads = $this->leads->select()
            ->where('person_id', $lead->person->id)
            ->where('id', '!=', $lead->id)
            ->load('agent')
            ->load('person')
            ->orderBy('createdAt', 'DESC')
            ->fetchAll();

        $mappedLeads = [];
        foreach ($relatedLeads as $relatedLead) {
            $mappedLeads[] = $this->leadView->map($relatedLead);
        }

        return $this->response->json([
            'status' => 200,
            'data' => $mappedLeads
        ]);
    }
}
