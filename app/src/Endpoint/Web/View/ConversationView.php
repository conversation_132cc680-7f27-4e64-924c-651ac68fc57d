<?php

namespace App\Endpoint\Web\View;

use App\Database\Entity\Conversation;
use Carbon\Carbon;
use Psr\Http\Message\ResponseInterface;
use Spiral\Core\Attribute\Singleton;
use Spiral\Prototype\Annotation\Prototyped;
use Spiral\Prototype\Traits\PrototypeTrait;

#[Singleton]
#[Prototyped(property: 'conversationView')]
class ConversationView
{
    use PrototypeTrait;

    public function map(Conversation $conversation): array
    {
        $lastMessage = $conversation->messages->last();

        return [
            'id' => $conversation->id,
            'lead' => [
                'id' => $conversation->lead->id,
                'person' => [
                    'id' => $conversation->lead->person->id,
                    'name' => [
                        'full' => $conversation->lead->person->firstName.' '.$conversation->lead->person->lastName,
                        'first' => $conversation->lead->person->firstName,
                        'last' => $conversation->lead->person->lastName,
                    ]
                ],
            ],
            'agent' => [
                'id' => $conversation->agent->id,
                'name' => $conversation->agent->name,
            ],
            'leadPhone' => $conversation->lead->phoneNumber->number ?? '',
            'lastMessage' => $lastMessage ? $lastMessage->content : '',
            'lastMessageTime' => $lastMessage ? $lastMessage->createdAt->format('c') : $conversation->createdAt->format('c'),
            'messageCount' => $conversation->messages->count(),
            'status' => $conversation->status->value,
            'isAgentActive' => $conversation->isAgentActive,
            'priority' => $conversation->priority->value,
            'metadata' => [
                'createdAt' => [
                    'raw' => $conversation->createdAt ?? null,
                    'formatted' => Carbon::createFromImmutable($conversation->createdAt)->diffForHumans(),
                ],
                'createdBy' => 'Unknown', // @todo
                'updatedAt' => [
                    'raw' => $conversation->updatedAt ?? null,
                    'formatted' => $conversation->updatedAt ? Carbon::createFromImmutable($conversation->updatedAt)->diffForHumans()
                    : 'Never',
                ],
                'updatedBy' => 'Unknown', // @todo
            ]
        ];
    }

    public function json(Conversation $conversation): ResponseInterface
    {
        return $this->response->json($this->map($conversation), 200);
    }
}
