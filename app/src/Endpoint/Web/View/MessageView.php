<?php

namespace App\Endpoint\Web\View;

use App\Database\Entity\Message;
use Psr\Http\Message\ResponseInterface;
use Spiral\Core\Attribute\Singleton;
use Spiral\Prototype\Annotation\Prototyped;
use Spiral\Prototype\Traits\PrototypeTrait;

#[Singleton]
#[Prototyped(property: 'messageView')]
class MessageView
{
    use PrototypeTrait;
    public function map(Message $message): array
    {
        return [
            'id' => $message->id,
            'conversation' => $message->conversation->id,
            'content' => $message->content,
            'sender' => $message->sender->value,
            'createdAt' => $message->createdAt->format('c'), // ISO 8601 format
            'author' => $message->author ? [
                'id' => $message->author->id,
                'name' => $message->author->person->firstName . ' ' . $message->author->person->lastName,
            ] : null,
            'lead' => $message->lead ? [
                'id' => $message->lead->id,
                'name' => $message->lead->person->firstName . ' ' . $message->lead->person->lastName,
            ] : null,
            'agent' => $message->agent ? [
                'id' => $message->agent->id,
                'name' => $message->agent->name,
            ] : null,
        ];
    }

    public function json(Message $message): ResponseInterface
    {
        return $this->response->json($this->map($message), 200);
    }
}
