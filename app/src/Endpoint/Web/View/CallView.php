<?php

namespace App\Endpoint\Web\View;

use App\Database\Entity\Call;
use Psr\Http\Message\ResponseInterface;
use Spiral\Core\Attribute\Singleton;
use Spiral\Prototype\Annotation\Prototyped;
use Spiral\Prototype\Traits\PrototypeTrait;

#[Singleton]
#[Prototyped(property: 'callView')]
class CallView
{
    use PrototypeTrait;

    public function map(Call $call): array
    {
        return [
            'id' => $call->id,
            'lead' => $call->lead ? [
                'id' => $call->lead->id,
                'name' => $call->lead->person->firstName . ' ' . $call->lead->person->lastName,
            ] : null,
            'agent' => $call->agent ? [
                'id' => $call->agent->id,
                'name' => $call->agent->name,
            ] : null,
            'conversation' => $call->conversation ? [
                'id' => $call->conversation->id,
            ] : null,
            'answered' => $call->answered,
            'answeredBy' => $call->answeredBy ? [
                'id' => $call->answeredBy->id,
                'number' => $call->answeredBy->number,
            ] : null,
            'voicemail' => $call->voicemail ? [
                'id' => $call->voicemail->id,
                'transcription' => $call->voicemail->transcription,
                'audioUrl' => $call->voicemail->audioUrl,
                'duration' => $call->voicemail->duration,
            ] : null,
            'duration' => $call->duration,
            'cost' => $call->cost,
            'status' => $call->status->value,
            'startTime' => $call->startTime->format('c'),
            'answerTime' => $call->answerTime?->format('c'),
            'endTime' => $call->endTime?->format('c'),
            'createdAt' => $call->createdAt->format('c'),
            'updatedAt' => $call->updatedAt?->format('c'),
        ];
    }

    public function json(Call $call): ResponseInterface
    {
        return $this->response->json($this->map($call), 200);
    }
}
