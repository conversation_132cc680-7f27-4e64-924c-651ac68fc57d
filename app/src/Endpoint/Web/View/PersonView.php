<?php

namespace App\Endpoint\Web\View;

use App\Database\Entity\Person;
use Psr\Http\Message\ResponseInterface;
use Spiral\Core\Attribute\Singleton;
use Spiral\Prototype\Annotation\Prototyped;
use Spiral\Prototype\Traits\PrototypeTrait;

#[Singleton]
#[Prototyped(property: 'personView')]
class PersonView
{
    use PrototypeTrait;

    public function map(Person $person)
    {
        return [
            'id' => $person->id,
            'name' => [
                'full' => trim($person->firstName.' '.$person->lastName),
                'first' => $person->firstName,
                'last' => $person->lastName,
            ],
            'email' => $person->email,
            'metadata' => [
                'createdAt' => $person->createdAt->format('Y-m-d H:i:s'),
                'updatedAt' => $person->updatedAt ? $person->updatedAt->format('Y-m-d H:i:s') : null,
            ]
        ];
    }

    public function json(Person $person): ResponseInterface
    {
        return $this->response->json($this->map($person), 200);
    }
}
