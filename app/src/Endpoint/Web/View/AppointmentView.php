<?php

namespace App\Endpoint\Web\View;

use App\Database\Entity\Appointment;
use App\Database\Entity\Note;
use Psr\Http\Message\ResponseInterface;
use Spiral\Core\Attribute\Singleton;
use Spiral\Prototype\Annotation\Prototyped;
use Spiral\Prototype\Traits\PrototypeTrait;

#[Singleton]
#[Prototyped(property: 'appointmentView')]
class AppointmentView
{
    use PrototypeTrait;

    public function map(Appointment $appointment): array
    {
        $notes = $appointment->notes->map(fn(Note $note) => $note->content)->toArray();
        
        return [
            'id' => $appointment->id,
            'lead' => $appointment->lead ? [
                'id' => $appointment->lead->id,
                'name' => $appointment->lead->person->firstName . ' ' . $appointment->lead->person->lastName,
            ] : null,
            'agent' => $appointment->agent ? [
                'id' => $appointment->agent->id,
                'name' => $appointment->agent->name,
            ] : null,
            'conversation' => $appointment->conversation ? [
                'id' => $appointment->conversation->id,
            ] : null,
            'service' => $appointment->service,
            'startTime' => $appointment->startTime->format('Y-m-d H:i:s'),
            'endTime' => $appointment->endTime->format('Y-m-d H:i:s'),
            'value' => $appointment->value,
            'status' => $appointment->status->value,
            'notes' => $notes,
            'createdAt' => $appointment->createdAt->format('Y-m-d H:i:s'),
            'updatedAt' => $appointment->updatedAt?->format('Y-m-d H:i:s'),
        ];
    }

    public function json(Appointment $appointment): ResponseInterface
    {
        return $this->response->json($this->map($appointment), 200);
    }
}
