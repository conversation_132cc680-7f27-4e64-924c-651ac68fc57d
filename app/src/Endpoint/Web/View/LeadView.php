<?php

namespace App\Endpoint\Web\View;

use App\Database\Entity\Lead;
use App\Database\Entity\Note;
use Spiral\Core\Attribute\Singleton;
use Spiral\Prototype\Annotation\Prototyped;
use Spiral\Prototype\Traits\PrototypeTrait;

#[Singleton]
#[Prototyped(property: 'leadView')]
class LeadView
{
    use PrototypeTrait;

    public function map(Lead $lead)
    {
        $notes = null;
//        $agent = null;
//        $notes = $lead?->notes?->map(fn(Note $item) => $item->content)->toArray();
        $agent = $lead->agent ? [
            'id' => $lead->agent->id,
            'name' => $lead->agent->name,
            'phone' => $lead->agent->phoneNumber?->number,
            'metrics' => [
                'totalLeads' => $lead->agent->leads ? $lead->agent->leads->count() : 0,
                'totalConversations' => $lead->agent->calculateConversationCount(),
                'totalCalls' => $lead->agent->calculateCallsHandled(),
                'totalAppointments' => $lead->agent->calculateConversionCount(),
                'averageResponseTime' => $lead->agent->calculateAvgLeadResponseTime() . ' seconds',
                'conversionRate' => $lead->agent->calculateConversionRate(),
                'leadsThisMonth' => $this->calculateLeadsThisMonth($lead->agent),
                'interactionsWithThisLead' => $this->calculateAgentInteractionsWithLead($lead->agent, $lead),
            ]
        ] : null;

        return [
            'id' => $lead->id,
            'person' => $this->personView->map($lead->person),
            'phone' => $lead->phoneNumber->number,
            'organization' => $lead->organization->id,
            'service' => $lead->service,
            'status' => $lead->status->value,
            'priority' => $lead->priority->value,
            'estimatedValue' => $lead->estimatedValue,
            'actualValue' => $lead->actualValue,
            'estimatedCost' => $lead->estimatedCost,
            'lastContact' => $lead->lastContact,
            'score' => $lead->score,
            'notes' => $notes,
            'agent' => $agent,
            'metadata' => [
                'createdAt' => $lead->createdAt->format('Y-m-d H:i:s'),
                'createdBy' => 'Unknown', // @todo
                'updatedAt' => $lead->updatedAt ? $lead->updatedAt->format('Y-m-d H:i:s') : null,
                'updatedBy' => 'Unknown', // @todo
            ]
        ];
    }

    public function json(Lead $lead)
    {
        return $this->response->json($this->map($lead), 200);
    }

    private function calculateAgentInteractionsWithLead($agent, $lead): int
    {
        if (!$agent || !$lead) {
            return 0;
        }

        $interactions = 0;

        // Count conversations between this agent and lead
        if ($lead->conversations) {
            foreach ($lead->conversations as $conversation) {
                if ($conversation && $conversation->agent && $conversation->agent->id === $agent->id) {
                    $interactions += $conversation->messages ? $conversation->messages->count() : 0;
                }
            }
        }

        // Count calls between this agent and lead
        if ($agent->conversations) {
            foreach ($agent->conversations as $conversation) {
                if ($conversation && $conversation->lead && $conversation->lead->id === $lead->id) {
                    if ($conversation->calls) {
                        $interactions += $conversation->calls->count();
                    }
                }
            }
        }

        // Count appointments between this agent and lead
        if ($agent->appointments) {
            foreach ($agent->appointments as $appointment) {
                if ($appointment && $appointment->lead && $appointment->lead->id === $lead->id) {
                    $interactions++;
                }
            }
        }

        return $interactions;
    }

    private function calculateLeadsThisMonth($agent): int
    {
        if (!$agent || !$agent->leads) {
            return 0;
        }

        $currentMonth = new \DateTimeImmutable('first day of this month');
        $count = 0;

        foreach ($agent->leads as $lead) {
            if ($lead->createdAt >= $currentMonth) {
                $count++;
            }
        }

        return $count;
    }
}
