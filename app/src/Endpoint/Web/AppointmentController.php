<?php

namespace App\Endpoint\Web;

use App\Database\Entity\Appointment;
use App\Endpoint\Web\DataGrid\AppointmentGridSchema;
use Cycle\ORM\Select;
use Spiral\DataGrid\Annotation\DataGrid;
use Spiral\Prototype\Traits\PrototypeTrait;
use Spiral\Router\Annotation\Route;

final class AppointmentController
{
    use PrototypeTrait;

    #[Route(route: '/appointment', name: 'appointment.list', methods: 'GET', group: 'v1')]
    #[DataGrid(grid: AppointmentGridSchema::class)]
    public function list(): Select
    {
        return $this->appointments->select()
            ->load('lead.person')
            ->load('agent')
            ->load('conversation')
            ->load('notes');
    }

    #[Route(route: '/appointment/<appointment:uuid>', name: 'appointment.get', methods: 'GET', group: 'v1')]
    public function get(Appointment $appointment)
    {
        return $this->appointmentView->json($appointment);
    }
}
