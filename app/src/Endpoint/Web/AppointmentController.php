<?php

namespace App\Endpoint\Web;

use App\Database\Entity\Appointment;
use App\Endpoint\Web\DataGrid\AppointmentGridSchema;
use App\Endpoint\Web\Filter\CreateAppointmentFilter;
use App\Endpoint\Web\Filter\UpdateAppointmentFilter;
use App\Service\AppointmentService;
use Cycle\ORM\Select;
use Psr\Http\Message\ResponseInterface;
use Spiral\DataGrid\Annotation\DataGrid;
use Spiral\Prototype\Traits\PrototypeTrait;
use Spiral\Router\Annotation\Route;

final class AppointmentController
{
    use PrototypeTrait;

    public function __construct(
        private readonly AppointmentService $appointmentService
    ) {}

    #[Route(route: '/appointment', name: 'appointment.list', methods: 'GET', group: 'v1')]
    #[DataGrid(grid: AppointmentGridSchema::class)]
    public function list(): Select
    {
        return $this->appointments->select()
            ->load('lead.person')
            ->load('agent')
            ->load('conversation')
            ->load('notes');
    }

    #[Route(route: '/appointment/<appointment:uuid>', name: 'appointment.get', methods: 'GET', group: 'v1')]
    public function get(Appointment $appointment): ResponseInterface
    {
        return $this->appointmentView->json($appointment);
    }

    #[Route(route: '/appointment', name: 'appointment.create', methods: 'POST', group: 'v1')]
    public function create(CreateAppointmentFilter $filter): ResponseInterface
    {
        try {
            $data = $filter->getAppointmentData();
            $appointment = $this->appointmentService->create($data);

            return $this->response->json([
                'status' => 201,
                'message' => 'Appointment created successfully',
                'data' => $this->appointmentView->map($appointment)
            ], 201);

        } catch (\InvalidArgumentException $e) {
            return $this->response->json([
                'status' => 400,
                'error' => $e->getMessage()
            ], 400);
        } catch (\Exception $e) {
            $this->logger->error('Failed to create appointment', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return $this->response->json([
                'status' => 500,
                'error' => 'Internal server error'
            ], 500);
        }
    }

    #[Route(route: '/appointment/<appointment:uuid>', name: 'appointment.update', methods: 'PATCH', group: 'v1')]
    public function update(Appointment $appointment, UpdateAppointmentFilter $filter): ResponseInterface
    {
        try {
            if (!$filter->hasUpdateData()) {
                return $this->response->json([
                    'status' => 400,
                    'error' => 'No update data provided'
                ], 400);
            }

            $data = $filter->getAppointmentData();
            $updatedAppointment = $this->appointmentService->update($appointment, $data);

            return $this->response->json([
                'status' => 200,
                'message' => 'Appointment updated successfully',
                'data' => $this->appointmentView->map($updatedAppointment)
            ]);

        } catch (\InvalidArgumentException $e) {
            return $this->response->json([
                'status' => 400,
                'error' => $e->getMessage()
            ], 400);
        } catch (\Exception $e) {
            $this->logger->error('Failed to update appointment', [
                'appointment_id' => $appointment->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return $this->response->json([
                'status' => 500,
                'error' => 'Internal server error'
            ], 500);
        }
    }

    #[Route(route: '/appointment/<appointment:uuid>', name: 'appointment.delete', methods: 'DELETE', group: 'v1')]
    public function delete(Appointment $appointment): ResponseInterface
    {
        try {
            $this->appointmentService->delete($appointment);

            return $this->response->json([
                'status' => 200,
                'message' => 'Appointment deleted successfully'
            ]);

        } catch (\Exception $e) {
            $this->logger->error('Failed to delete appointment', [
                'appointment_id' => $appointment->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return $this->response->json([
                'status' => 500,
                'error' => 'Internal server error'
            ], 500);
        }
    }

    #[Route(route: '/appointment/<appointment:uuid>/confirm', name: 'appointment.confirm', methods: 'PATCH', group: 'v1')]
    public function confirm(Appointment $appointment): ResponseInterface
    {
        try {
            $confirmedAppointment = $this->appointmentService->confirm($appointment);

            return $this->response->json([
                'status' => 200,
                'message' => 'Appointment confirmed successfully',
                'data' => $this->appointmentView->map($confirmedAppointment)
            ]);

        } catch (\Exception $e) {
            $this->logger->error('Failed to confirm appointment', [
                'appointment_id' => $appointment->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return $this->response->json([
                'status' => 500,
                'error' => 'Internal server error'
            ], 500);
        }
    }

    #[Route(route: '/appointment/<appointment:uuid>/cancel', name: 'appointment.cancel', methods: 'PATCH', group: 'v1')]
    public function cancel(Appointment $appointment): ResponseInterface
    {
        try {
            $cancelledAppointment = $this->appointmentService->cancel($appointment);

            return $this->response->json([
                'status' => 200,
                'message' => 'Appointment cancelled successfully',
                'data' => $this->appointmentView->map($cancelledAppointment)
            ]);

        } catch (\Exception $e) {
            $this->logger->error('Failed to cancel appointment', [
                'appointment_id' => $appointment->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return $this->response->json([
                'status' => 500,
                'error' => 'Internal server error'
            ], 500);
        }
    }
}
