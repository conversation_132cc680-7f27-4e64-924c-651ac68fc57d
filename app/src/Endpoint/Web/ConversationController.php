<?php

namespace App\Endpoint\Web;

use App\Database\Entity\Conversation;
use App\Endpoint\Web\DataGrid\ConversationGridSchema;
use App\Endpoint\Web\Filter\UpdateConversationFilter;
use Cycle\ORM\Select;
use Psr\Http\Message\ResponseInterface;
use Spiral\DataGrid\Annotation\DataGrid;
use Spiral\Prototype\Traits\PrototypeTrait;
use Spiral\Router\Annotation\Route;

final class ConversationController
{
    use PrototypeTrait;

    #[Route(route: '/conversation', name: 'conversation.list', methods: 'GET', group: 'v1')]
    #[DataGrid(grid: ConversationGridSchema::class)]
    public function list(): Select
    {
        return $this->conversations->select()
            ->load('lead')
            ->load('lead.person')
            ->load('lead.phoneNumber')
            ->load('agent')
            ->load('messages', ['orderBy' => ['createdAt' => 'DESC']])
            ->load('calls')
            ;
    }

    #[Route(route: '/conversation/<conversation:uuid>', name: 'conversation.get', methods: 'GET', group: 'v1')]
    public function get(Conversation $conversation): ResponseInterface
    {
        return $this->conversationView->json($conversation);
    }

    #[Route(route: '/conversation/<conversation:uuid>', name: 'conversation.update', methods: 'PATCH', group: 'v1')]
    public function update(
        Conversation $conversation,
        UpdateConversationFilter $filter
    ): ResponseInterface {
        try {
            if (!$filter->hasUpdateData()) {
                return $this->response->json([
                    'status' => 400,
                    'error' => 'No update data provided'
                ], 400);
            }

            // Prepare data for service
            $data = [];
            if ($filter->status !== null) {
                $data['status'] = $filter->status;
            }
            if ($filter->isAgentActive !== null) {
                $data['isAgentActive'] = $filter->isAgentActive;
            }

            // Update conversation using service
            $updatedConversation = $this->conversationService->update($conversation, $data);

            return $this->response->json([
                'status' => 200,
                'message' => 'Conversation updated successfully',
                'data' => $this->conversationView->map($updatedConversation)
            ]);

        } catch (\InvalidArgumentException $e) {
            return $this->response->json([
                'status' => 400,
                'error' => $e->getMessage()
            ], 400);
        } catch (\Exception $e) {
            return $this->response->json([
                'status' => 500,
                'error' => 'Internal server error'
            ], 500);
        }
    }
}
