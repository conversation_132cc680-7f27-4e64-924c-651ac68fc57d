<?php

declare(strict_types=1);

namespace App\Endpoint\Web\Filter;

use App\Database\Enum\ConversationStatus;
use Spiral\Filters\Attribute\Input\Post;
use Spiral\Filters\Model\Filter;
use Spiral\Filters\Model\FilterDefinitionInterface;
use Spiral\Filters\Model\HasFilterDefinition;
use Spiral\Validator\FilterDefinition;

final class UpdateConversationFilter extends Filter implements HasFilterDefinition
{
    #[Post]
    public readonly ?string $status;

    #[Post]
    public readonly ?bool $isAgentActive;

    public function filterDefinition(): FilterDefinitionInterface
    {
        return new FilterDefinition([
            'status' => [
                'string',
                ['in_array', array_column(ConversationStatus::cases(), 'value'), true]
            ],
            'isAgentActive' => ['boolean'],
        ]);
    }

    /**
     * Get the status enum value if provided
     */
    public function getStatus(): ?ConversationStatus
    {
        return $this->status ? ConversationStatus::from($this->status) : null;
    }

    /**
     * Check if any update data is provided
     */
    public function hasUpdateData(): bool
    {
        return $this->status !== null || $this->isAgentActive !== null;
    }
}
