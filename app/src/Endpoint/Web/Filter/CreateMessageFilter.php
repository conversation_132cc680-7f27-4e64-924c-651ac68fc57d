<?php

declare(strict_types=1);

namespace App\Endpoint\Web\Filter;

use App\Database\Enum\MessageSender;
use Spiral\Filters\Attribute\Input\Post;
use Spiral\Filters\Model\Filter;
use Spiral\Filters\Model\FilterDefinitionInterface;
use Spiral\Filters\Model\HasFilterDefinition;
use Spiral\Validator\FilterDefinition;

final class CreateMessageFilter extends Filter implements HasFilterDefinition
{
    #[Post]
    public readonly string $conversation;

    #[Post]
    public readonly string $content;

    #[Post]
    public readonly ?string $sender;

    public function filterDefinition(): FilterDefinitionInterface
    {
        return new FilterDefinition([
            'conversation' => [
                'required',
                'string',
                ['regexp', '/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i']
            ],
            'content' => [
                'required',
                'string',
                ['string::longer', 0],
                ['string::shorter', 2001]
            ],
            'sender' => [
                'string',
                ['in_array', array_column(MessageSender::cases(), 'value'), true]
            ],
        ]);
    }

    /**
     * Get the sender enum value
     */
    public function getSender(): MessageSender
    {
        return MessageSender::from($this->sender ?? MessageSender::USER->value);
    }
}
