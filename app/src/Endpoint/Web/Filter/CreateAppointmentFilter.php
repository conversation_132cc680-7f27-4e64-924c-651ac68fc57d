<?php

declare(strict_types=1);

namespace App\Endpoint\Web\Filter;

use App\Database\Enum\AppointmentStatus;
use Spiral\Filters\Attribute\Input\Post;
use Spiral\Filters\Model\Filter;
use Spiral\Filters\Model\FilterDefinitionInterface;
use Spiral\Filters\Model\HasFilterDefinition;
use Spiral\Validator\FilterDefinition;

final class CreateAppointmentFilter extends Filter implements HasFilterDefinition
{
    #[Post]
    public readonly string $lead_id;

    #[Post]
    public readonly string $agent_id;

    #[Post]
    public readonly ?string $conversation_id;

    #[Post]
    public readonly string $service;

    #[Post]
    public readonly string $start_time;

    #[Post]
    public readonly string $end_time;

    #[Post]
    public readonly ?float $value;

    #[Post]
    public readonly ?string $status;

    #[Post]
    public readonly ?array $notes;

    public function filterDefinition(): FilterDefinitionInterface
    {
        return new FilterDefinition([
            'lead_id' => [
                'required',
                'string',
                ['regexp', '/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i']
            ],
            'agent_id' => [
                'required',
                'string',
                ['regexp', '/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i']
            ],
            'conversation_id' => [
                'string',
                ['regexp', '/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i']
            ],
            'service' => [
                'required',
                'string',
                ['string::longer', 1],
                ['string::shorter', 255]
            ],
            'start_time' => [
                'required',
                'string'
            ],
            'end_time' => [
                'required',
                'string'
            ],
            'value' => [
                'numeric',
                ['number::higher', 0]
            ],
            'status' => [
                'string',
                ['in_array', array_column(AppointmentStatus::cases(), 'value'), true]
            ],
            'notes' => [
                'array'
            ],
            'notes.*' => [
                'string',
                ['string::shorter', 1000]
            ]
        ]);
    }

    /**
     * Get validated appointment data for service
     */
    public function getAppointmentData(): array
    {
        $data = [
            'lead_id' => $this->lead_id,
            'agent_id' => $this->agent_id,
            'service' => $this->service,
            'start_time' => $this->start_time,
            'end_time' => $this->end_time,
        ];

        if ($this->conversation_id !== null) {
            $data['conversation_id'] = $this->conversation_id;
        }

        if ($this->value !== null) {
            $data['value'] = $this->value;
        }

        if ($this->status !== null) {
            $data['status'] = $this->status;
        }

        if ($this->notes !== null && !empty($this->notes)) {
            $data['notes'] = array_filter($this->notes, fn($note) => !empty(trim($note)));
        }

        return $data;
    }
}
