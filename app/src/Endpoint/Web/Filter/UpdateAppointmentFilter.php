<?php

declare(strict_types=1);

namespace App\Endpoint\Web\Filter;

use App\Database\Enum\AppointmentStatus;
use Spiral\Filters\Attribute\Input\Post;
use Spiral\Filters\Model\Filter;
use Spiral\Filters\Model\FilterDefinitionInterface;
use Spiral\Filters\Model\HasFilterDefinition;
use Spiral\Validator\FilterDefinition;

final class UpdateAppointmentFilter extends Filter implements HasFilterDefinition
{
    #[Post]
    public readonly ?string $lead_id;

    #[Post]
    public readonly ?string $agent_id;

    #[Post]
    public readonly ?string $conversation_id;

    #[Post]
    public readonly ?string $service;

    #[Post]
    public readonly ?string $start_time;

    #[Post]
    public readonly ?string $end_time;

    #[Post]
    public readonly ?float $value;

    #[Post]
    public readonly ?string $status;

    #[Post]
    public readonly ?array $notes;

    public function filterDefinition(): FilterDefinitionInterface
    {
        return new FilterDefinition([
            'lead_id' => [
                'string',
                ['regexp', '/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i']
            ],
            'agent_id' => [
                'string',
                ['regexp', '/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i']
            ],
            'conversation_id' => [
                'string',
                ['regexp', '/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i']
            ],
            'service' => [
                'string',
                ['string::longer', 1],
                ['string::shorter', 255]
            ],
            'start_time' => [
                'string'
            ],
            'end_time' => [
                'string'
            ],
            'value' => [
                'numeric',
                ['number::higher', 0]
            ],
            'status' => [
                'string',
                ['in_array', array_column(AppointmentStatus::cases(), 'value'), true]
            ],
            'notes' => [
                'array'
            ],
            'notes.*' => [
                'string',
                ['string::shorter', 1000]
            ]
        ]);
    }

    /**
     * Check if any update data is provided
     */
    public function hasUpdateData(): bool
    {
        return $this->lead_id !== null ||
               $this->agent_id !== null ||
               $this->conversation_id !== null ||
               $this->service !== null ||
               $this->start_time !== null ||
               $this->end_time !== null ||
               $this->value !== null ||
               $this->status !== null ||
               $this->notes !== null;
    }

    /**
     * Get validated appointment data for service
     */
    public function getAppointmentData(): array
    {
        $data = [];

        if ($this->lead_id !== null) {
            $data['lead_id'] = $this->lead_id;
        }

        if ($this->agent_id !== null) {
            $data['agent_id'] = $this->agent_id;
        }

        if ($this->conversation_id !== null) {
            $data['conversation_id'] = $this->conversation_id;
        }

        if ($this->service !== null) {
            $data['service'] = $this->service;
        }

        if ($this->start_time !== null) {
            $data['start_time'] = $this->start_time;
        }

        if ($this->end_time !== null) {
            $data['end_time'] = $this->end_time;
        }

        if ($this->value !== null) {
            $data['value'] = $this->value;
        }

        if ($this->status !== null) {
            $data['status'] = $this->status;
        }

        if ($this->notes !== null) {
            $data['notes'] = array_filter($this->notes, fn($note) => !empty(trim($note)));
        }

        return $data;
    }
}
