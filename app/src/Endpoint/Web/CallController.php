<?php

namespace App\Endpoint\Web;

use App\Database\Entity\Call;
use App\Endpoint\Web\DataGrid\CallGridSchema;
use Cycle\ORM\Select;
use Spiral\DataGrid\Annotation\DataGrid;
use Spiral\Prototype\Traits\PrototypeTrait;
use Spiral\Router\Annotation\Route;

final class CallController
{
    use PrototypeTrait;

    #[Route(route: '/call', name: 'call.list', methods: 'GET', group: 'v1')]
    #[DataGrid(grid: CallGridSchema::class)]
    public function list(): Select
    {
        return $this->calls->select()
            ->load('lead.person')
            ->load('agent')
            ->load('conversation')
            ->load('answeredBy')
            ->load('voicemail');
    }

    #[Route(route: '/call/<call:uuid>', name: 'call.get', methods: 'GET', group: 'v1')]
    public function get(Call $call)
    {
        return $this->callView->json($call);
    }
}
