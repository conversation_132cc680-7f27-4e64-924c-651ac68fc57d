<?php

namespace App\Endpoint\Web;

use App\Database\Entity\Message;
use App\Endpoint\Web\DataGrid\MessageGridSchema;
use App\Endpoint\Web\Filter\CreateMessageFilter;
use Cycle\ORM\Select;
use Psr\Http\Message\ResponseInterface;
use Spiral\DataGrid\Annotation\DataGrid;
use Spiral\Prototype\Traits\PrototypeTrait;
use Spiral\Router\Annotation\Route;

final class MessageController
{
    use PrototypeTrait;

    #[Route(route: '/message', name: 'message.list', methods: 'GET', group: 'v1')]
    #[DataGrid(grid: MessageGridSchema::class)]
    public function list(): Select
    {
        return $this->messages->select()
            ->load('conversation')
            ->load('author')
            ->load('author.person')
            ->load('lead')
            ->load('lead.person')
            ->load('agent');
    }

    #[Route(route: '/message/<message:uuid>', name: 'message.get', methods: 'GET', group: 'v1')]
    public function get(Message $message): ResponseInterface
    {
        return $this->messageView->json($message);
    }

    #[Route(route: '/message', name: 'message.create', methods: 'POST', group: 'v1')]
    public function create(CreateMessageFilter $filter): ResponseInterface
    {
        try {
            // Get current user from request context
            $currentUser = $this->request->attribute('user');

            // Prepare data for service
            $data = [
                'conversation' => $filter->conversation,
                'content' => $filter->content,
                'sender' => $filter->getSender()->value,
            ];

            // Create message using service
            $message = $this->messageService->create($data, $currentUser);

            return $this->response->json([
                'status' => 201,
                'message' => 'Message created successfully',
                'data' => $this->messageView->map($message)
            ], 201);

        } catch (\InvalidArgumentException $e) {
            return $this->response->json([
                'status' => 400,
                'error' => $e->getMessage()
            ], 400);
        } catch (\Exception $e) {
            return $this->response->json([
                'status' => 500,
                'error' => 'Internal server error'
            ], 500);
        }
    }
}
