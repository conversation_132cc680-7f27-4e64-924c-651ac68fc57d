<?php

declare(strict_types=1);

namespace App\Middleware;

use App\Service\AuthService;
use Psr\Http\Message\ResponseInterface;
use Psr\Http\Message\ServerRequestInterface;
use Psr\Http\Server\MiddlewareInterface;
use Psr\Http\Server\RequestHandlerInterface;
use Spiral\Http\Exception\ClientException\UnauthorizedException;

class AuthMiddleware implements MiddlewareInterface
{
    public function __construct(
        private readonly AuthService $authService
    ) {}

    public function process(ServerRequestInterface $request, RequestHandlerInterface $handler): ResponseInterface
    {
        $authHeader = $request->getHeaderLine('Authorization');

        if (!$authHeader) {
            throw new UnauthorizedException('Authorization header is required');
        }

        $token = $this->authService->extractTokenFromHeader($authHeader);

        if (!$token) {
            throw new UnauthorizedException('Invalid authorization header format. Expected: Bearer <token>');
        }

        $payload = $this->authService->validateToken($token);

        if (!$payload) {
            throw new UnauthorizedException('Invalid or expired token');
        }

        $user = $this->authService->getUserFromToken($token);

        if (!$user) {
            throw new UnauthorizedException('User not found');
        }

        // Add user to request attributes for use in controllers
        $request = $request->withAttribute('user', $user);
        $request = $request->withAttribute('jwt_payload', $payload);

        return $handler->handle($request);
    }
}
