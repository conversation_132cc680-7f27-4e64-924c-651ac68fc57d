<?php

declare(strict_types=1);

namespace App\Service;

use App\Database\Entity\Conversation;
use App\Database\Entity\Lead;
use App\Database\Entity\Agent;
use App\Database\Enum\ConversationStatus;
use App\Database\Repository\ConversationRepository;
use Cycle\ORM\EntityManagerInterface;
use Spiral\Prototype\Annotation\Prototyped;
use Spiral\Validation\ValidationInterface;

#[Prototyped(property: 'conversationService')]
class ConversationService
{
    public function __construct(
        private readonly ConversationRepository $conversationRepository,
        private readonly EntityManagerInterface $entityManager,
        private readonly ValidationInterface $validation
    ) {}

    /**
     * Update an existing conversation
     */
    public function update(Conversation $conversation, array $data): Conversation
    {
        $this->validateUpdateData($data);

        $updated = false;

        // Update status if provided
        if (isset($data['status'])) {
            $conversation->status = ConversationStatus::from($data['status']);
            $updated = true;
        }

        // Update agent active status if provided
        if (isset($data['isAgentActive'])) {
            $conversation->isAgentActive = (bool) $data['isAgentActive'];
            $updated = true;
        }

        if (!$updated) {
            throw new \InvalidArgumentException('No update data provided');
        }

        $this->entityManager->persist($conversation);
        $this->entityManager->run();

        return $conversation;
    }

    /**
     * Create a new conversation
     */
    public function create(Lead $lead, Agent $agent, array $data = []): Conversation
    {
        $conversation = new Conversation();
        $conversation->lead = $lead;
        $conversation->agent = $agent;
        $conversation->status = ConversationStatus::from($data['status'] ?? ConversationStatus::ACTIVE->value);
        $conversation->priority = $data['priority'] ?? null;
        $conversation->isAgentActive = $data['isAgentActive'] ?? true;

        $this->entityManager->persist($conversation);
        $this->entityManager->run();

        return $conversation;
    }

    /**
     * Archive a conversation
     */
    public function archive(Conversation $conversation): Conversation
    {
        $conversation->status = ConversationStatus::ARCHIVED;
        $conversation->isAgentActive = false;

        $this->entityManager->persist($conversation);
        $this->entityManager->run();

        return $conversation;
    }

    /**
     * Resolve a conversation
     */
    public function resolve(Conversation $conversation): Conversation
    {
        $conversation->status = ConversationStatus::RESOLVED;
        $conversation->isAgentActive = false;

        $this->entityManager->persist($conversation);
        $this->entityManager->run();

        return $conversation;
    }

    /**
     * Reactivate a conversation
     */
    public function reactivate(Conversation $conversation): Conversation
    {
        $conversation->status = ConversationStatus::ACTIVE;
        $conversation->isAgentActive = true;

        $this->entityManager->persist($conversation);
        $this->entityManager->run();

        return $conversation;
    }

    /**
     * Disable auto-response for a conversation
     */
    public function disableAutoResponse(Conversation $conversation): Conversation
    {
        $conversation->isAgentActive = false;

        $this->entityManager->persist($conversation);
        $this->entityManager->run();

        return $conversation;
    }

    /**
     * Enable auto-response for a conversation
     */
    public function enableAutoResponse(Conversation $conversation): Conversation
    {
        $conversation->isAgentActive = true;

        $this->entityManager->persist($conversation);
        $this->entityManager->run();

        return $conversation;
    }

    /**
     * Get conversation with all relationships loaded
     */
    public function getWithRelationships(string $conversationId): ?Conversation
    {
        return $this->conversationRepository->select()
            ->where('id', $conversationId)
            ->load('lead')
            ->load('lead.person')
            ->load('lead.phoneNumber')
            ->load('agent')
            ->load('messages')
            ->load('calls')
            ->fetchOne();
    }

    /**
     * Get conversations for a specific lead
     */
    public function getByLead(string $leadId): array
    {
        return $this->conversationRepository->select()
            ->where('lead.id', $leadId)
            ->load('agent')
            ->load('messages')
            ->load('calls')
            ->orderBy('updated_at', 'DESC')
            ->fetchAll();
    }

    /**
     * Get conversations for a specific agent
     */
    public function getByAgent(string $agentId): array
    {
        return $this->conversationRepository->select()
            ->where('agent.id', $agentId)
            ->load('lead')
            ->load('lead.person')
            ->load('messages')
            ->load('calls')
            ->orderBy('updated_at', 'DESC')
            ->fetchAll();
    }

    /**
     * Delete a conversation (soft delete)
     */
    public function delete(Conversation $conversation): void
    {
        $this->entityManager->delete($conversation);
        $this->entityManager->run();
    }

    /**
     * Validate update data
     */
    private function validateUpdateData(array $data): void
    {
        $rules = [
            'status' => [
                'string',
                ['in_array', array_column(ConversationStatus::cases(), 'value'), true]
            ],
            'isAgentActive' => ['boolean'],
            'priority' => ['string'],
        ];

        $validator = $this->validation->validate($data, $rules);

        if (!$validator->isValid()) {
            throw new \InvalidArgumentException('Validation failed: ' . implode(', ', $validator->getErrors()));
        }
    }
}
