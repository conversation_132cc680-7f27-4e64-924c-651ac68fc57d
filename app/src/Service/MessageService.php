<?php

declare(strict_types=1);

namespace App\Service;

use App\Database\Entity\Conversation;
use App\Database\Entity\Message;
use App\Database\Entity\User;
use App\Database\Enum\MessageSender;
use App\Database\Repository\ConversationRepository;
use App\Database\Repository\MessageRepository;
use Cycle\ORM\EntityManagerInterface;
use Spiral\Prototype\Annotation\Prototyped;
use Spiral\Validation\ValidationInterface;

#[Prototyped(property: 'messageService')]
class MessageService
{
    public function __construct(
        private readonly MessageRepository $messageRepository,
        private readonly ConversationRepository $conversationRepository,
        private readonly EntityManagerInterface $entityManager,
        private readonly ValidationInterface $validation,
        private readonly ConversationService $conversationService
    ) {}

    /**
     * Create a new message
     */
    public function create(array $data, ?User $currentUser = null): Message
    {
        $this->validateCreateData($data);

        // Find the conversation
        $conversation = $this->conversationRepository->findByPK($data['conversation']);
        if (!$conversation) {
            throw new \InvalidArgumentException('Conversation not found');
        }

        // Create the message
        $message = new Message();
        $message->conversation = $conversation;
        $message->content = $data['content'];
        $message->sender = MessageSender::from($data['sender'] ?? MessageSender::USER->value);

        // Set the appropriate author/lead/agent based on sender
        $this->setMessageRelationships($message, $conversation, $currentUser);

        // Handle auto-response logic
        $this->handleAutoResponseLogic($message, $conversation);

        // Persist the message
        $this->entityManager->persist($message);
        $this->entityManager->run();

        return $message;
    }

    /**
     * Set message relationships based on sender type
     */
    private function setMessageRelationships(Message $message, Conversation $conversation, ?User $currentUser): void
    {
        switch ($message->sender) {
            case MessageSender::USER:
                if ($currentUser) {
                    $message->author = $currentUser;
                }
                break;
            case MessageSender::LEAD:
                $message->lead = $conversation->lead;
                break;
            case MessageSender::AGENT:
                $message->agent = $conversation->agent;
                break;
        }
    }

    /**
     * Handle auto-response logic when user sends manual message
     */
    private function handleAutoResponseLogic(Message $message, Conversation $conversation): void
    {
        // If user sends a manual message, disable auto-response using ConversationService
        if ($message->sender === MessageSender::USER && $conversation->isAgentActive) {
            $this->conversationService->disableAutoResponse($conversation);
        }
    }

    /**
     * Validate create data
     */
    private function validateCreateData(array $data): void
    {
        $rules = [
            'conversation' => [
                'required',
                'string',
                ['regexp', '/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i']
            ],
            'content' => [
                'required',
                'string',
                ['string::longer', 0],
                ['string::shorter', 2001]
            ],
            'sender' => [
                'string',
                ['in_array', array_column(MessageSender::cases(), 'value'), true]
            ],
        ];

        $validator = $this->validation->validate($data, $rules);

        if (!$validator->isValid()) {
            throw new \InvalidArgumentException('Validation failed: ' . implode(', ', $validator->getErrors()));
        }
    }

    /**
     * Get messages for a conversation
     */
    public function getConversationMessages(string $conversationId, int $limit = 50, int $offset = 0): array
    {
        return $this->messageRepository->select()
            ->where('conversation.id', $conversationId)
            ->orderBy('created_at', 'ASC')
            ->limit($limit)
            ->offset($offset)
            ->load('author')
            ->load('lead')
            ->load('agent')
            ->fetchAll();
    }

    /**
     * Get latest message for a conversation
     */
    public function getLatestMessage(string $conversationId): ?Message
    {
        return $this->messageRepository->select()
            ->where('conversation.id', $conversationId)
            ->orderBy('created_at', 'DESC')
            ->limit(1)
            ->fetchOne();
    }

    /**
     * Mark message as read (for future implementation)
     */
    public function markAsRead(Message $message, User $user): void
    {
        // TODO: Implement message read status tracking
        // This would involve creating a MessageRead entity or adding read status to Message
    }

    /**
     * Delete a message (soft delete)
     */
    public function delete(Message $message): void
    {
        $this->entityManager->delete($message);
        $this->entityManager->run();
    }
}
