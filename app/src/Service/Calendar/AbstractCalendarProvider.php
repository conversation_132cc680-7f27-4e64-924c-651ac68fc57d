<?php

declare(strict_types=1);

namespace App\Service\Calendar;

use App\Database\Entity\Appointment;
use Psr\Log\LoggerInterface;

abstract class AbstractCalendarProvider implements CalendarProviderInterface
{
    protected LoggerInterface $logger;

    public function __construct(LoggerInterface $logger)
    {
        $this->logger = $logger;
    }

    /**
     * Convert internal appointment to external calendar format
     */
    protected function convertAppointmentToExternal(Appointment $appointment): array
    {
        return [
            'title' => $this->generateAppointmentTitle($appointment),
            'description' => $this->generateAppointmentDescription($appointment),
            'start_time' => $appointment->startTime->format('c'),
            'end_time' => $appointment->endTime->format('c'),
            'attendees' => $this->getAppointmentAttendees($appointment),
            'location' => $this->getAppointmentLocation($appointment),
        ];
    }

    /**
     * Generate appointment title for external calendar
     */
    protected function generateAppointmentTitle(Appointment $appointment): string
    {
        $leadName = $appointment->lead->person->firstName . ' ' . $appointment->lead->person->lastName;
        return "{$appointment->service} - {$leadName}";
    }

    /**
     * Generate appointment description for external calendar
     */
    protected function generateAppointmentDescription(Appointment $appointment): string
    {
        $description = "Service: {$appointment->service}\n";
        $description .= "Lead: {$appointment->lead->person->firstName} {$appointment->lead->person->lastName}\n";
        
        if ($appointment->lead->person->email) {
            $description .= "Email: {$appointment->lead->person->email}\n";
        }
        
        if ($appointment->lead->phoneNumber) {
            $description .= "Phone: {$appointment->lead->phoneNumber->number}\n";
        }
        
        if ($appointment->value > 0) {
            $description .= "Value: $" . number_format($appointment->value, 2) . "\n";
        }
        
        if (!$appointment->notes->isEmpty()) {
            $description .= "\nNotes:\n";
            foreach ($appointment->notes as $note) {
                $description .= "- {$note->content}\n";
            }
        }
        
        return trim($description);
    }

    /**
     * Get appointment attendees for external calendar
     */
    protected function getAppointmentAttendees(Appointment $appointment): array
    {
        $attendees = [];
        
        // Add lead as attendee if they have an email
        if ($appointment->lead->person->email) {
            $attendees[] = [
                'email' => $appointment->lead->person->email,
                'name' => $appointment->lead->person->firstName . ' ' . $appointment->lead->person->lastName,
                'role' => 'attendee'
            ];
        }
        
        return $attendees;
    }

    /**
     * Get appointment location (placeholder for now)
     */
    protected function getAppointmentLocation(Appointment $appointment): ?string
    {
        // This could be enhanced to pull from agent location or appointment-specific location
        return null;
    }

    /**
     * Log provider-specific actions
     */
    protected function logAction(string $action, array $context = []): void
    {
        $this->logger->info("Calendar provider action: {$action}", array_merge([
            'provider' => $this->getProviderName()
        ], $context));
    }

    /**
     * Log provider-specific errors
     */
    protected function logError(string $message, array $context = []): void
    {
        $this->logger->error("Calendar provider error: {$message}", array_merge([
            'provider' => $this->getProviderName()
        ], $context));
    }

    /**
     * Validate required credentials are present
     */
    protected function validateCredentials(array $credentials): void
    {
        $required = $this->getRequiredCredentials();
        $missing = array_diff($required, array_keys($credentials));
        
        if (!empty($missing)) {
            throw new \InvalidArgumentException(
                "Missing required credentials for {$this->getProviderName()}: " . implode(', ', $missing)
            );
        }
    }

    /**
     * Make HTTP request with error handling
     */
    protected function makeRequest(string $method, string $url, array $headers = [], array $data = []): array
    {
        // This is a placeholder - in a real implementation, you'd use a proper HTTP client
        // like Guzzle or Symfony HttpClient
        throw new \RuntimeException('HTTP client not implemented - use Guzzle or similar');
    }

    /**
     * Handle OAuth token refresh if needed
     */
    protected function ensureValidToken(array &$credentials): void
    {
        // Check if token is expired and refresh if needed
        if (isset($credentials['expires_at']) && time() >= $credentials['expires_at']) {
            if (isset($credentials['refresh_token'])) {
                $newTokens = $this->refreshToken($credentials['refresh_token']);
                $credentials = array_merge($credentials, $newTokens);
            } else {
                throw new \RuntimeException('Access token expired and no refresh token available');
            }
        }
    }
}
