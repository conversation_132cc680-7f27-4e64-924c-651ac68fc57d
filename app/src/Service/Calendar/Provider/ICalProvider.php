<?php

declare(strict_types=1);

namespace App\Service\Calendar\Provider;

use App\Database\Entity\Appointment;
use App\Service\Calendar\AbstractCalendarProvider;

class ICalProvider extends AbstractCalendarProvider
{
    private const PROVIDER_NAME = 'ical';
    private const DISPLAY_NAME = 'iCal/Outlook Subscription';

    public function getProviderName(): string
    {
        return self::PROVIDER_NAME;
    }

    public function getDisplayName(): string
    {
        return self::DISPLAY_NAME;
    }

    public function isAuthenticated(array $credentials): bool
    {
        // iCal doesn't require authentication - it's a subscription-based format
        return true;
    }

    public function getAuthorizationUrl(string $redirectUri, array $scopes = []): string
    {
        // iCal doesn't use OAuth - return empty string
        return '';
    }

    public function exchangeCodeForToken(string $code, string $redirectUri): array
    {
        // iCal doesn't use OAuth tokens
        return [];
    }

    public function refreshToken(string $refreshToken): array
    {
        // iCal doesn't use OAuth tokens
        return [];
    }

    public function createAppointment(Appointment $appointment, array $credentials): ?string
    {
        // iCal is read-only from external perspective
        // We generate the iCal content but don't "create" in external system
        $this->logAction('Generated iCal for appointment', ['appointment_id' => $appointment->id]);
        return 'ical_' . $appointment->id;
    }

    public function updateAppointment(Appointment $appointment, string $externalId, array $credentials): bool
    {
        // iCal is read-only from external perspective
        $this->logAction('Updated iCal for appointment', [
            'appointment_id' => $appointment->id,
            'external_id' => $externalId
        ]);
        return true;
    }

    public function deleteAppointment(string $externalId, array $credentials): bool
    {
        // iCal is read-only from external perspective
        $this->logAction('Removed from iCal', ['external_id' => $externalId]);
        return true;
    }

    public function getAppointments(\DateTimeInterface $startDate, \DateTimeInterface $endDate, array $credentials): array
    {
        // iCal is typically used for exporting, not importing
        return [];
    }

    public function getAppointment(string $externalId, array $credentials): ?array
    {
        // iCal is typically used for exporting, not importing
        return null;
    }

    public function testConnection(array $credentials): bool
    {
        // iCal doesn't require connection testing
        return true;
    }

    public function getRequiredCredentials(): array
    {
        // iCal doesn't require credentials
        return [];
    }

    public function getRequiredScopes(): array
    {
        // iCal doesn't use OAuth scopes
        return [];
    }

    /**
     * Generate iCal content for a single appointment
     */
    public function generateICalForAppointment(Appointment $appointment): string
    {
        $ical = "BEGIN:VCALENDAR\r\n";
        $ical .= "VERSION:2.0\r\n";
        $ical .= "PRODID:-//Back-Talk//Appointment System//EN\r\n";
        $ical .= "CALSCALE:GREGORIAN\r\n";
        $ical .= "METHOD:PUBLISH\r\n";
        
        $ical .= $this->generateVEventForAppointment($appointment);
        
        $ical .= "END:VCALENDAR\r\n";
        
        return $ical;
    }

    /**
     * Generate iCal content for multiple appointments
     */
    public function generateICalForAppointments(array $appointments): string
    {
        $ical = "BEGIN:VCALENDAR\r\n";
        $ical .= "VERSION:2.0\r\n";
        $ical .= "PRODID:-//Back-Talk//Appointment System//EN\r\n";
        $ical .= "CALSCALE:GREGORIAN\r\n";
        $ical .= "METHOD:PUBLISH\r\n";
        
        foreach ($appointments as $appointment) {
            $ical .= $this->generateVEventForAppointment($appointment);
        }
        
        $ical .= "END:VCALENDAR\r\n";
        
        return $ical;
    }

    /**
     * Generate VEVENT component for an appointment
     */
    private function generateVEventForAppointment(Appointment $appointment): string
    {
        $vevent = "BEGIN:VEVENT\r\n";
        $vevent .= "UID:appointment-{$appointment->id}@back-talk.com\r\n";
        $vevent .= "DTSTAMP:" . gmdate('Ymd\THis\Z') . "\r\n";
        $vevent .= "DTSTART:" . $appointment->startTime->format('Ymd\THis\Z') . "\r\n";
        $vevent .= "DTEND:" . $appointment->endTime->format('Ymd\THis\Z') . "\r\n";
        $vevent .= "SUMMARY:" . $this->escapeICalText($this->generateAppointmentTitle($appointment)) . "\r\n";
        $vevent .= "DESCRIPTION:" . $this->escapeICalText($this->generateAppointmentDescription($appointment)) . "\r\n";
        
        // Add organizer (agent)
        if ($appointment->agent) {
            $vevent .= "ORGANIZER;CN={$appointment->agent->name}:mailto:<EMAIL>\r\n";
        }
        
        // Add attendee (lead)
        if ($appointment->lead->person->email) {
            $leadName = $appointment->lead->person->firstName . ' ' . $appointment->lead->person->lastName;
            $vevent .= "ATTENDEE;CN={$leadName};ROLE=REQ-PARTICIPANT:mailto:{$appointment->lead->person->email}\r\n";
        }
        
        // Add status
        $status = match($appointment->status->value) {
            'confirmed' => 'CONFIRMED',
            'cancelled' => 'CANCELLED',
            default => 'TENTATIVE'
        };
        $vevent .= "STATUS:{$status}\r\n";
        
        $vevent .= "CREATED:" . $appointment->createdAt->format('Ymd\THis\Z') . "\r\n";
        
        if ($appointment->updatedAt) {
            $vevent .= "LAST-MODIFIED:" . $appointment->updatedAt->format('Ymd\THis\Z') . "\r\n";
        }
        
        $vevent .= "END:VEVENT\r\n";
        
        return $vevent;
    }

    /**
     * Escape text for iCal format
     */
    private function escapeICalText(string $text): string
    {
        // Escape special characters for iCal format
        $text = str_replace(['\\', ';', ',', "\n", "\r"], ['\\\\', '\\;', '\\,', '\\n', ''], $text);
        
        // Fold long lines (iCal spec requires lines to be max 75 characters)
        return $this->foldICalLine($text);
    }

    /**
     * Fold long lines according to iCal specification
     */
    private function foldICalLine(string $line): string
    {
        if (strlen($line) <= 75) {
            return $line;
        }
        
        $folded = '';
        $remaining = $line;
        
        while (strlen($remaining) > 75) {
            $folded .= substr($remaining, 0, 75) . "\r\n ";
            $remaining = substr($remaining, 75);
        }
        
        $folded .= $remaining;
        
        return $folded;
    }
}
