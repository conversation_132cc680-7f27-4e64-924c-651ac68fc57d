<?php

declare(strict_types=1);

namespace App\Service\Calendar\Provider;

use App\Database\Entity\Appointment;
use App\Service\Calendar\AbstractCalendarProvider;

class GoogleCalendarProvider extends AbstractCalendarProvider
{
    private const PROVIDER_NAME = 'google';
    private const DISPLAY_NAME = 'Google Calendar';
    private const AUTH_URL = 'https://accounts.google.com/o/oauth2/v2/auth';
    private const TOKEN_URL = 'https://oauth2.googleapis.com/token';
    private const API_BASE_URL = 'https://www.googleapis.com/calendar/v3';

    public function getProviderName(): string
    {
        return self::PROVIDER_NAME;
    }

    public function getDisplayName(): string
    {
        return self::DISPLAY_NAME;
    }

    public function isAuthenticated(array $credentials): bool
    {
        try {
            $this->validateCredentials($credentials);
            return $this->testConnection($credentials);
        } catch (\Exception $e) {
            $this->logError('Authentication check failed', ['error' => $e->getMessage()]);
            return false;
        }
    }

    public function getAuthorizationUrl(string $redirectUri, array $scopes = []): string
    {
        $defaultScopes = $this->getRequiredScopes();
        $allScopes = array_unique(array_merge($defaultScopes, $scopes));
        
        $params = [
            'client_id' => $this->getClientId(),
            'redirect_uri' => $redirectUri,
            'scope' => implode(' ', $allScopes),
            'response_type' => 'code',
            'access_type' => 'offline',
            'prompt' => 'consent'
        ];

        return self::AUTH_URL . '?' . http_build_query($params);
    }

    public function exchangeCodeForToken(string $code, string $redirectUri): array
    {
        $data = [
            'client_id' => $this->getClientId(),
            'client_secret' => $this->getClientSecret(),
            'code' => $code,
            'grant_type' => 'authorization_code',
            'redirect_uri' => $redirectUri
        ];

        // In a real implementation, make HTTP POST request to TOKEN_URL
        // For now, return placeholder structure
        return [
            'access_token' => 'placeholder_access_token',
            'refresh_token' => 'placeholder_refresh_token',
            'expires_in' => 3600,
            'expires_at' => time() + 3600,
            'token_type' => 'Bearer'
        ];
    }

    public function refreshToken(string $refreshToken): array
    {
        $data = [
            'client_id' => $this->getClientId(),
            'client_secret' => $this->getClientSecret(),
            'refresh_token' => $refreshToken,
            'grant_type' => 'refresh_token'
        ];

        // In a real implementation, make HTTP POST request to TOKEN_URL
        // For now, return placeholder structure
        return [
            'access_token' => 'new_placeholder_access_token',
            'expires_in' => 3600,
            'expires_at' => time() + 3600,
            'token_type' => 'Bearer'
        ];
    }

    public function createAppointment(Appointment $appointment, array $credentials): ?string
    {
        try {
            $this->ensureValidToken($credentials);
            $eventData = $this->convertAppointmentToGoogleEvent($appointment);
            
            // In a real implementation, make HTTP POST request to create event
            // Return the external event ID
            $this->logAction('Created appointment', ['appointment_id' => $appointment->id]);
            return 'google_event_' . uniqid();
            
        } catch (\Exception $e) {
            $this->logError('Failed to create appointment', [
                'appointment_id' => $appointment->id,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    public function updateAppointment(Appointment $appointment, string $externalId, array $credentials): bool
    {
        try {
            $this->ensureValidToken($credentials);
            $eventData = $this->convertAppointmentToGoogleEvent($appointment);
            
            // In a real implementation, make HTTP PUT request to update event
            $this->logAction('Updated appointment', [
                'appointment_id' => $appointment->id,
                'external_id' => $externalId
            ]);
            return true;
            
        } catch (\Exception $e) {
            $this->logError('Failed to update appointment', [
                'appointment_id' => $appointment->id,
                'external_id' => $externalId,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    public function deleteAppointment(string $externalId, array $credentials): bool
    {
        try {
            $this->ensureValidToken($credentials);
            
            // In a real implementation, make HTTP DELETE request
            $this->logAction('Deleted appointment', ['external_id' => $externalId]);
            return true;
            
        } catch (\Exception $e) {
            $this->logError('Failed to delete appointment', [
                'external_id' => $externalId,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    public function getAppointments(\DateTimeInterface $startDate, \DateTimeInterface $endDate, array $credentials): array
    {
        try {
            $this->ensureValidToken($credentials);
            
            // In a real implementation, make HTTP GET request to fetch events
            $this->logAction('Fetched appointments', [
                'start_date' => $startDate->format('Y-m-d'),
                'end_date' => $endDate->format('Y-m-d')
            ]);
            
            return []; // Return array of events
            
        } catch (\Exception $e) {
            $this->logError('Failed to fetch appointments', [
                'start_date' => $startDate->format('Y-m-d'),
                'end_date' => $endDate->format('Y-m-d'),
                'error' => $e->getMessage()
            ]);
            return [];
        }
    }

    public function getAppointment(string $externalId, array $credentials): ?array
    {
        try {
            $this->ensureValidToken($credentials);
            
            // In a real implementation, make HTTP GET request for specific event
            $this->logAction('Fetched single appointment', ['external_id' => $externalId]);
            
            return null; // Return event data or null if not found
            
        } catch (\Exception $e) {
            $this->logError('Failed to fetch appointment', [
                'external_id' => $externalId,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    public function testConnection(array $credentials): bool
    {
        try {
            $this->validateCredentials($credentials);
            $this->ensureValidToken($credentials);
            
            // In a real implementation, make a simple API call to test connection
            return true;
            
        } catch (\Exception $e) {
            $this->logError('Connection test failed', ['error' => $e->getMessage()]);
            return false;
        }
    }

    public function getRequiredCredentials(): array
    {
        return ['access_token', 'refresh_token', 'client_id', 'client_secret'];
    }

    public function getRequiredScopes(): array
    {
        return [
            'https://www.googleapis.com/auth/calendar',
            'https://www.googleapis.com/auth/calendar.events'
        ];
    }

    /**
     * Convert appointment to Google Calendar event format
     */
    private function convertAppointmentToGoogleEvent(Appointment $appointment): array
    {
        $baseData = $this->convertAppointmentToExternal($appointment);
        
        return [
            'summary' => $baseData['title'],
            'description' => $baseData['description'],
            'start' => [
                'dateTime' => $baseData['start_time'],
                'timeZone' => 'UTC'
            ],
            'end' => [
                'dateTime' => $baseData['end_time'],
                'timeZone' => 'UTC'
            ],
            'attendees' => array_map(function($attendee) {
                return [
                    'email' => $attendee['email'],
                    'displayName' => $attendee['name']
                ];
            }, $baseData['attendees']),
            'location' => $baseData['location']
        ];
    }

    private function getClientId(): string
    {
        // In a real implementation, get from configuration
        return env('GOOGLE_CALENDAR_CLIENT_ID', '');
    }

    private function getClientSecret(): string
    {
        // In a real implementation, get from configuration
        return env('GOOGLE_CALENDAR_CLIENT_SECRET', '');
    }
}
