<?php

declare(strict_types=1);

namespace App\Service\Calendar;

use App\Database\Entity\Appointment;

interface CalendarProviderInterface
{
    /**
     * Get the provider name (e.g., 'google', 'microsoft', 'zoho')
     */
    public function getProviderName(): string;

    /**
     * Get the display name for the provider
     */
    public function getDisplayName(): string;

    /**
     * Check if the provider is properly configured and authenticated
     */
    public function isAuthenticated(array $credentials): bool;

    /**
     * Get the authorization URL for OAuth flow
     */
    public function getAuthorizationUrl(string $redirectUri, array $scopes = []): string;

    /**
     * Exchange authorization code for access token
     */
    public function exchangeCodeForToken(string $code, string $redirectUri): array;

    /**
     * Refresh an expired access token
     */
    public function refreshToken(string $refreshToken): array;

    /**
     * Create an appointment in the external calendar
     */
    public function createAppointment(Appointment $appointment, array $credentials): ?string;

    /**
     * Update an appointment in the external calendar
     */
    public function updateAppointment(Appointment $appointment, string $externalId, array $credentials): bool;

    /**
     * Delete an appointment from the external calendar
     */
    public function deleteAppointment(string $externalId, array $credentials): bool;

    /**
     * Get appointments from the external calendar within a date range
     */
    public function getAppointments(\DateTimeInterface $startDate, \DateTimeInterface $endDate, array $credentials): array;

    /**
     * Get a specific appointment from the external calendar
     */
    public function getAppointment(string $externalId, array $credentials): ?array;

    /**
     * Test the connection with the provided credentials
     */
    public function testConnection(array $credentials): bool;

    /**
     * Get the required credential fields for this provider
     */
    public function getRequiredCredentials(): array;

    /**
     * Get the OAuth scopes required for this provider
     */
    public function getRequiredScopes(): array;
}
