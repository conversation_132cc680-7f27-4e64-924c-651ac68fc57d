<?php

declare(strict_types=1);

namespace App\Service\Calendar;

use App\Database\Entity\Appointment;
use App\Service\Calendar\Provider\GoogleCalendarProvider;
use App\Service\Calendar\Provider\ICalProvider;
use App\Service\Calendar\Provider\MicrosoftCalendarProvider;
use Psr\Log\LoggerInterface;
use Spiral\Prototype\Annotation\Prototyped;

#[Prototyped(property: 'calendarIntegrationService')]
class CalendarIntegrationService
{
    private array $providers = [];

    public function __construct(
        private readonly LoggerInterface $logger,
        GoogleCalendarProvider $googleProvider,
        MicrosoftCalendarProvider $microsoftProvider,
        ICalProvider $iCalProvider
    ) {
        // Register all available providers
        $this->registerProvider($googleProvider);
        $this->registerProvider($microsoftProvider);
        $this->registerProvider($iCalProvider);
    }

    /**
     * Register a calendar provider
     */
    public function registerProvider(CalendarProviderInterface $provider): void
    {
        $this->providers[$provider->getProviderName()] = $provider;
    }

    /**
     * Get all available providers
     */
    public function getAvailableProviders(): array
    {
        return array_map(function(CalendarProviderInterface $provider) {
            return [
                'name' => $provider->getProviderName(),
                'display_name' => $provider->getDisplayName(),
                'required_credentials' => $provider->getRequiredCredentials(),
                'required_scopes' => $provider->getRequiredScopes()
            ];
        }, $this->providers);
    }

    /**
     * Get a specific provider by name
     */
    public function getProvider(string $providerName): ?CalendarProviderInterface
    {
        return $this->providers[$providerName] ?? null;
    }

    /**
     * Check if a provider exists
     */
    public function hasProvider(string $providerName): bool
    {
        return isset($this->providers[$providerName]);
    }

    /**
     * Get authorization URL for a provider
     */
    public function getAuthorizationUrl(string $providerName, string $redirectUri, array $scopes = []): string
    {
        $provider = $this->getProvider($providerName);
        if (!$provider) {
            throw new \InvalidArgumentException("Provider '{$providerName}' not found");
        }

        return $provider->getAuthorizationUrl($redirectUri, $scopes);
    }

    /**
     * Exchange authorization code for tokens
     */
    public function exchangeCodeForToken(string $providerName, string $code, string $redirectUri): array
    {
        $provider = $this->getProvider($providerName);
        if (!$provider) {
            throw new \InvalidArgumentException("Provider '{$providerName}' not found");
        }

        return $provider->exchangeCodeForToken($code, $redirectUri);
    }

    /**
     * Test connection for a provider with given credentials
     */
    public function testConnection(string $providerName, array $credentials): bool
    {
        $provider = $this->getProvider($providerName);
        if (!$provider) {
            throw new \InvalidArgumentException("Provider '{$providerName}' not found");
        }

        return $provider->testConnection($credentials);
    }

    /**
     * Create appointment in external calendar
     */
    public function createAppointment(string $providerName, Appointment $appointment, array $credentials): ?string
    {
        $provider = $this->getProvider($providerName);
        if (!$provider) {
            $this->logger->error("Provider '{$providerName}' not found for appointment creation", [
                'appointment_id' => $appointment->id
            ]);
            return null;
        }

        try {
            return $provider->createAppointment($appointment, $credentials);
        } catch (\Exception $e) {
            $this->logger->error("Failed to create appointment in external calendar", [
                'provider' => $providerName,
                'appointment_id' => $appointment->id,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * Update appointment in external calendar
     */
    public function updateAppointment(string $providerName, Appointment $appointment, string $externalId, array $credentials): bool
    {
        $provider = $this->getProvider($providerName);
        if (!$provider) {
            $this->logger->error("Provider '{$providerName}' not found for appointment update", [
                'appointment_id' => $appointment->id,
                'external_id' => $externalId
            ]);
            return false;
        }

        try {
            return $provider->updateAppointment($appointment, $externalId, $credentials);
        } catch (\Exception $e) {
            $this->logger->error("Failed to update appointment in external calendar", [
                'provider' => $providerName,
                'appointment_id' => $appointment->id,
                'external_id' => $externalId,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Delete appointment from external calendar
     */
    public function deleteAppointment(string $providerName, string $externalId, array $credentials): bool
    {
        $provider = $this->getProvider($providerName);
        if (!$provider) {
            $this->logger->error("Provider '{$providerName}' not found for appointment deletion", [
                'external_id' => $externalId
            ]);
            return false;
        }

        try {
            return $provider->deleteAppointment($externalId, $credentials);
        } catch (\Exception $e) {
            $this->logger->error("Failed to delete appointment from external calendar", [
                'provider' => $providerName,
                'external_id' => $externalId,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Sync appointment to all configured providers for a user/organization
     */
    public function syncAppointmentToAllProviders(Appointment $appointment, array $providerConfigurations): array
    {
        $results = [];

        foreach ($providerConfigurations as $config) {
            $providerName = $config['provider'];
            $credentials = $config['credentials'];
            $externalId = $config['external_id'] ?? null;

            if ($externalId) {
                // Update existing appointment
                $success = $this->updateAppointment($providerName, $appointment, $externalId, $credentials);
                $results[$providerName] = [
                    'action' => 'update',
                    'success' => $success,
                    'external_id' => $externalId
                ];
            } else {
                // Create new appointment
                $newExternalId = $this->createAppointment($providerName, $appointment, $credentials);
                $results[$providerName] = [
                    'action' => 'create',
                    'success' => $newExternalId !== null,
                    'external_id' => $newExternalId
                ];
            }
        }

        return $results;
    }

    /**
     * Generate iCal content for appointments
     */
    public function generateICalForAppointments(array $appointments): string
    {
        $iCalProvider = $this->getProvider('ical');
        if (!$iCalProvider instanceof ICalProvider) {
            throw new \RuntimeException('iCal provider not available');
        }

        return $iCalProvider->generateICalForAppointments($appointments);
    }

    /**
     * Generate iCal content for a single appointment
     */
    public function generateICalForAppointment(Appointment $appointment): string
    {
        $iCalProvider = $this->getProvider('ical');
        if (!$iCalProvider instanceof ICalProvider) {
            throw new \RuntimeException('iCal provider not available');
        }

        return $iCalProvider->generateICalForAppointment($appointment);
    }

    /**
     * Get appointments from external calendar
     */
    public function getAppointmentsFromProvider(
        string $providerName,
        \DateTimeInterface $startDate,
        \DateTimeInterface $endDate,
        array $credentials
    ): array {
        $provider = $this->getProvider($providerName);
        if (!$provider) {
            throw new \InvalidArgumentException("Provider '{$providerName}' not found");
        }

        try {
            return $provider->getAppointments($startDate, $endDate, $credentials);
        } catch (\Exception $e) {
            $this->logger->error("Failed to fetch appointments from external calendar", [
                'provider' => $providerName,
                'start_date' => $startDate->format('Y-m-d'),
                'end_date' => $endDate->format('Y-m-d'),
                'error' => $e->getMessage()
            ]);
            return [];
        }
    }
}
