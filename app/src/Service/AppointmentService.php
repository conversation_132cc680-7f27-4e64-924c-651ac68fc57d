<?php

declare(strict_types=1);

namespace App\Service;

use App\Database\Entity\Appointment;
use App\Database\Entity\Lead;
use App\Database\Entity\Agent;
use App\Database\Entity\Conversation;
use App\Database\Entity\Note;
use App\Database\Enum\AppointmentStatus;
use App\Database\Repository\AppointmentRepository;
use App\Database\Repository\LeadRepository;
use App\Database\Repository\AgentRepository;
use App\Database\Repository\ConversationRepository;
use Cycle\ORM\EntityManagerInterface;
use Spiral\Prototype\Annotation\Prototyped;
use Spiral\Validation\ValidationInterface;

#[Prototyped(property: 'appointmentService')]
class AppointmentService
{
    public function __construct(
        private readonly AppointmentRepository $appointmentRepository,
        private readonly LeadRepository $leadRepository,
        private readonly AgentRepository $agentRepository,
        private readonly ConversationRepository $conversationRepository,
        private readonly EntityManagerInterface $entityManager,
        private readonly ValidationInterface $validation
    ) {}

    /**
     * Create a new appointment
     */
    public function create(array $data): Appointment
    {
        $this->validateCreateData($data);

        // Fetch required entities
        $lead = $this->leadRepository->findByPK($data['lead_id']);
        if (!$lead) {
            throw new \InvalidArgumentException('Lead not found');
        }

        $agent = $this->agentRepository->findByPK($data['agent_id']);
        if (!$agent) {
            throw new \InvalidArgumentException('Agent not found');
        }

        // Fetch optional conversation
        $conversation = null;
        if (isset($data['conversation_id'])) {
            $conversation = $this->conversationRepository->findByPK($data['conversation_id']);
            if (!$conversation) {
                throw new \InvalidArgumentException('Conversation not found');
            }
        }

        $appointment = new Appointment();
        $appointment->lead = $lead;
        $appointment->agent = $agent;
        $appointment->conversation = $conversation;
        $appointment->service = $data['service'];
        $appointment->startTime = new \DateTimeImmutable($data['start_time']);
        $appointment->endTime = new \DateTimeImmutable($data['end_time']);
        $appointment->value = (float) ($data['value'] ?? 0.0);
        $appointment->status = AppointmentStatus::from($data['status'] ?? AppointmentStatus::PENDING->value);

        $this->entityManager->persist($appointment);
        $this->entityManager->run();

        // Handle notes if provided
        if (isset($data['notes']) && is_array($data['notes'])) {
            $this->addNotes($appointment, $data['notes']);
        }

        return $appointment;
    }

    /**
     * Update an existing appointment
     */
    public function update(Appointment $appointment, array $data): Appointment
    {
        $this->validateUpdateData($data);

        $updated = false;

        // Update basic fields
        if (isset($data['service'])) {
            $appointment->service = $data['service'];
            $updated = true;
        }

        if (isset($data['start_time'])) {
            $appointment->startTime = new \DateTimeImmutable($data['start_time']);
            $updated = true;
        }

        if (isset($data['end_time'])) {
            $appointment->endTime = new \DateTimeImmutable($data['end_time']);
            $updated = true;
        }

        if (isset($data['value'])) {
            $appointment->value = (float) $data['value'];
            $updated = true;
        }

        if (isset($data['status'])) {
            $appointment->status = AppointmentStatus::from($data['status']);
            $updated = true;
        }

        // Update relationships
        if (isset($data['lead_id'])) {
            $lead = $this->leadRepository->findByPK($data['lead_id']);
            if (!$lead) {
                throw new \InvalidArgumentException('Lead not found');
            }
            $appointment->lead = $lead;
            $updated = true;
        }

        if (isset($data['agent_id'])) {
            $agent = $this->agentRepository->findByPK($data['agent_id']);
            if (!$agent) {
                throw new \InvalidArgumentException('Agent not found');
            }
            $appointment->agent = $agent;
            $updated = true;
        }

        if (isset($data['conversation_id'])) {
            if ($data['conversation_id'] === null) {
                $appointment->conversation = null;
            } else {
                $conversation = $this->conversationRepository->findByPK($data['conversation_id']);
                if (!$conversation) {
                    throw new \InvalidArgumentException('Conversation not found');
                }
                $appointment->conversation = $conversation;
            }
            $updated = true;
        }

        if (!$updated) {
            throw new \InvalidArgumentException('No update data provided');
        }

        $this->entityManager->persist($appointment);
        $this->entityManager->run();

        // Handle notes if provided
        if (isset($data['notes']) && is_array($data['notes'])) {
            $this->replaceNotes($appointment, $data['notes']);
        }

        return $appointment;
    }

    /**
     * Delete an appointment (soft delete)
     */
    public function delete(Appointment $appointment): void
    {
        $this->entityManager->delete($appointment);
        $this->entityManager->run();
    }

    /**
     * Confirm an appointment
     */
    public function confirm(Appointment $appointment): Appointment
    {
        $appointment->status = AppointmentStatus::CONFIRMED;
        $this->entityManager->persist($appointment);
        $this->entityManager->run();

        return $appointment;
    }

    /**
     * Cancel an appointment
     */
    public function cancel(Appointment $appointment): Appointment
    {
        $appointment->status = AppointmentStatus::CANCELLED;
        $this->entityManager->persist($appointment);
        $this->entityManager->run();

        return $appointment;
    }

    /**
     * Reschedule an appointment
     */
    public function reschedule(Appointment $appointment, string $newStartTime, string $newEndTime): Appointment
    {
        $appointment->startTime = new \DateTimeImmutable($newStartTime);
        $appointment->endTime = new \DateTimeImmutable($newEndTime);
        $appointment->status = AppointmentStatus::RESCHEDULED;

        $this->entityManager->persist($appointment);
        $this->entityManager->run();

        return $appointment;
    }

    /**
     * Get appointment with all relationships loaded
     */
    public function getWithRelationships(string $appointmentId): ?Appointment
    {
        return $this->appointmentRepository->select()
            ->where('id', $appointmentId)
            ->load('lead')
            ->load('lead.person')
            ->load('agent')
            ->load('conversation')
            ->load('notes')
            ->fetchOne();
    }

    /**
     * Get appointments for a specific lead
     */
    public function getByLead(string $leadId): array
    {
        return $this->appointmentRepository->select()
            ->where('lead.id', $leadId)
            ->load('agent')
            ->load('conversation')
            ->load('notes')
            ->orderBy('startTime', 'ASC')
            ->fetchAll();
    }

    /**
     * Get appointments for a specific agent
     */
    public function getByAgent(string $agentId): array
    {
        return $this->appointmentRepository->select()
            ->where('agent.id', $agentId)
            ->load('lead')
            ->load('lead.person')
            ->load('conversation')
            ->load('notes')
            ->orderBy('startTime', 'ASC')
            ->fetchAll();
    }

    /**
     * Get appointments within a date range
     */
    public function getByDateRange(\DateTimeInterface $startDate, \DateTimeInterface $endDate): array
    {
        return $this->appointmentRepository->select()
            ->where('startTime', '>=', $startDate)
            ->where('startTime', '<=', $endDate)
            ->load('lead')
            ->load('lead.person')
            ->load('agent')
            ->load('conversation')
            ->load('notes')
            ->orderBy('startTime', 'ASC')
            ->fetchAll();
    }

    /**
     * Add notes to an appointment
     */
    private function addNotes(Appointment $appointment, array $noteContents): void
    {
        foreach ($noteContents as $content) {
            if (is_string($content) && !empty(trim($content))) {
                $note = new Note();
                $note->content = trim($content);
                $this->entityManager->persist($note);
                $appointment->notes->add($note);
            }
        }
        $this->entityManager->persist($appointment);
        $this->entityManager->run();
    }

    /**
     * Replace all notes for an appointment
     */
    private function replaceNotes(Appointment $appointment, array $noteContents): void
    {
        // Clear existing notes
        $appointment->notes->clear();
        
        // Add new notes
        $this->addNotes($appointment, $noteContents);
    }
