<?php

namespace App\Database\Entity;

use App\Database\Enum\PhoneNumberStatus;
use App\Database\Enum\PhoneNumberType;
use App\Database\Repository\PhoneNumberRepository;
use Cycle\Annotated\Annotation\Column;
use Cycle\Annotated\Annotation\Entity;
use Cycle\Annotated\Annotation\Relation\HasOne;
use Cycle\ORM\Entity\Behavior\Uuid\Uuid4;
use Cycle\ORM\Entity\Behavior;
use Ramsey\Uuid\UuidInterface;

#[Entity(repository: PhoneNumberRepository::class)]
#[Uuid4(field: 'id', nullable: false)]
#[Behavior\CreatedAt(field: 'createdAt', column: 'created_at')]
#[Behavior\UpdatedAt(field: 'updatedAt', column: 'updated_at')]
#[Behavior\SoftDelete(field: 'deletedAt', column: 'deleted_at')]
class PhoneNumber
{
    #[Column(type: 'uuid', primary: true, nullable: false)]
    public UuidInterface $id;
    #[Column(type: 'bigInteger', nullable: false)]
    public int $number;
    #[Column(type: 'enum(landline,virtual,mobile,fax)', nullable: false, default: PhoneNumberType::VIRTUAL->value, typecast: PhoneNumberType::class)]
    public PhoneNumberType $type;
    #[Column(type: 'enum(active,inactive,pending)', nullable: false, default: PhoneNumberStatus::ACTIVE->value, typecast: PhoneNumberStatus::class)]
    public PhoneNumberStatus $status;
    #[Column(type: 'float', nullable: true)]
    public ?float $monthlyRate;
    #[Column(type: 'datetime', nullable: true)]
    public ?\DateTimeImmutable $purchaseDate;
    #[Column(type: 'datetime', nullable: false)]
    public \DateTimeImmutable $createdAt;
    #[Column(type: 'datetime', nullable: true)]
    public ?\DateTimeImmutable $updatedAt;
    #[Column(type: 'datetime', nullable: true)]
    public ?\DateTimeImmutable $deletedAt;
    // relationships
    #[HasOne(target: Agent::class, outerKey: 'phone_number_id', nullable: true)]
    public ?Agent $agent;
    #[HasOne(target: Organization::class, outerKey: 'phone_number_id', nullable: true)]
    public ?Organization $organization;
    #[HasOne(target: Person::class, outerKey: 'phone_number_id', nullable: true)]
    public ?Person $person;
    #[HasOne(target: Lead::class, outerKey: 'phone_number_id', nullable: true)]
    public ?Lead $lead;

    public function __construct(
    ) {}
}
