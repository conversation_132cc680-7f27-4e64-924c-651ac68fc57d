<?php

namespace App\Database\Entity;

use App\Database\Enum\CalendarIntegrationStatus;
use App\Database\Repository\CalendarIntegrationRepository;
use Cycle\Annotated\Annotation\Column;
use Cycle\Annotated\Annotation\Entity;
use Cycle\Annotated\Annotation\Relation\BelongsTo;
use Cycle\ORM\Entity\Behavior;
use Cycle\ORM\Entity\Behavior\Uuid\Uuid4;
use Ramsey\Uuid\UuidInterface;

#[Entity(repository: CalendarIntegrationRepository::class)]
#[Uuid4(field: 'id', nullable: false)]
#[Behavior\CreatedAt(field: 'createdAt', column: 'created_at')]
#[Behavior\UpdatedAt(field: 'updatedAt', column: 'updated_at')]
#[Behavior\SoftDelete(field: 'deletedAt', column: 'deleted_at')]
class CalendarIntegration
{
    #[Column(type: 'uuid', primary: true, nullable: false)]
    public UuidInterface $id;

    #[BelongsTo(target: Organization::class, innerKey: 'organization_id')]
    public Organization $organization;

    #[BelongsTo(target: User::class, innerKey: 'user_id', nullable: true)]
    public ?User $user;

    #[Column(type: 'string', nullable: false)]
    public string $provider; // 'google', 'microsoft', 'zoho', 'ical'

    #[Column(type: 'string', nullable: false)]
    public string $name; // User-friendly name for this integration

    #[Column(type: 'json', nullable: false)]
    public array $credentials; // Encrypted credentials (access_token, refresh_token, etc.)

    #[Column(type: 'json', nullable: true)]
    public ?array $settings; // Provider-specific settings

    #[Column(type: 'enum(active,inactive,error)', nullable: false, default: CalendarIntegrationStatus::ACTIVE->value, typecast: CalendarIntegrationStatus::class)]
    public CalendarIntegrationStatus $status;

    #[Column(type: 'datetime', nullable: true)]
    public ?\DateTimeImmutable $lastSyncAt;

    #[Column(type: 'string', nullable: true)]
    public ?string $lastSyncError;

    #[Column(type: 'boolean', nullable: false, default: true)]
    public bool $syncEnabled;

    #[Column(type: 'boolean', nullable: false, default: true)]
    public bool $bidirectionalSync; // Whether to sync both ways or just push to external

    #[Column(type: 'datetime', nullable: false)]
    public \DateTimeImmutable $createdAt;

    #[Column(type: 'datetime', nullable: true)]
    public ?\DateTimeImmutable $updatedAt;

    #[Column(type: 'datetime', nullable: true)]
    public ?\DateTimeImmutable $deletedAt = null;

    public function __construct()
    {
        $this->credentials = [];
        $this->settings = [];
    }

    /**
     * Check if the integration is active and sync is enabled
     */
    public function isActive(): bool
    {
        return $this->status === CalendarIntegrationStatus::ACTIVE && $this->syncEnabled;
    }

    /**
     * Check if the integration has valid credentials
     */
    public function hasValidCredentials(): bool
    {
        return !empty($this->credentials) &&
               isset($this->credentials['access_token']) &&
               !empty($this->credentials['access_token']);
    }

    /**
     * Get decrypted credentials
     * Note: In a real implementation, you'd decrypt the credentials here
     */
    public function getDecryptedCredentials(): array
    {
        // TODO: Implement proper encryption/decryption
        return $this->credentials;
    }

    /**
     * Set encrypted credentials
     * Note: In a real implementation, you'd encrypt the credentials here
     */
    public function setEncryptedCredentials(array $credentials): void
    {
        // TODO: Implement proper encryption
        $this->credentials = $credentials;
    }

    /**
     * Update last sync information
     */
    public function updateLastSync(?\DateTimeImmutable $syncTime = null, ?string $error = null): void
    {
        $this->lastSyncAt = $syncTime ?? new \DateTimeImmutable();
        $this->lastSyncError = $error;

        if ($error) {
            $this->status = CalendarIntegrationStatus::ERROR;
        } elseif ($this->status === CalendarIntegrationStatus::ERROR) {
            $this->status = CalendarIntegrationStatus::ACTIVE;
        }
    }

    /**
     * Check if credentials are expired (for OAuth providers)
     */
    public function areCredentialsExpired(): bool
    {
        if (!isset($this->credentials['expires_at'])) {
            return false;
        }

        return time() >= $this->credentials['expires_at'];
    }

    /**
     * Get provider-specific settings
     */
    public function getSetting(string $key, mixed $default = null): mixed
    {
        return $this->settings[$key] ?? $default;
    }

    /**
     * Set provider-specific setting
     */
    public function setSetting(string $key, mixed $value): void
    {
        if ($this->settings === null) {
            $this->settings = [];
        }
        $this->settings[$key] = $value;
    }
}
