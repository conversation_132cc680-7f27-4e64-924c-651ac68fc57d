<?php

namespace App\Database\Entity;

use App\Database\Repository\OrganizationRepository;
use Cycle\Annotated\Annotation\Column;
use Cycle\Annotated\Annotation\Entity;
use Cycle\Annotated\Annotation\Relation\BelongsTo;
use Cycle\Annotated\Annotation\Relation\HasMany;
use Cycle\Annotated\Annotation\Relation\HasOne;
use Cycle\ORM\Entity\Behavior;
use Cycle\ORM\Entity\Behavior\Uuid\Uuid4;
use Doctrine\Common\Collections\ArrayCollection;
use Ramsey\Uuid\UuidInterface;

#[Entity(repository: OrganizationRepository::class)]
#[Uuid4(field: 'id', nullable: false)]
#[Behavior\CreatedAt(field: 'createdAt', column: 'created_at')]
#[Behavior\UpdatedAt(field: 'updatedAt', column: 'updated_at')]
#[Behavior\SoftDelete(field: 'deletedAt', column: 'deleted_at')]
class Organization
{
    #[Column(type: 'uuid', primary: true, nullable: false, )]
    public UuidInterface $id;
    #[Column(type: 'string', nullable: false)]
    public string $name;
    #[BelongsTo(target: Industry::class, innerKey: 'industry_id')]
    public Industry $industry;
    #[BelongsTo(target: Address::class, innerKey: 'address_id')]
    public Address $address;
    #[BelongsTo(target: PhoneNumber::class, innerKey: 'phone_number_id')]
    public PhoneNumber $phoneNumber;
    #[Column(type: 'datetime', nullable: false)]
    public \DateTimeImmutable $createdAt;
    #[Column(type: 'datetime', nullable: true)]
    public ?\DateTimeImmutable $updatedAt = null;
    #[Column(type: 'datetime', nullable: true)]
    public ?\DateTimeImmutable $deletedAt = null;
    // relationships
    #[HasMany(target: User::class, nullable: true, collection: 'doctrine')]
    public ArrayCollection $users;
    #[HasMany(target: Lead::class, collection: 'doctrine')]
    public ArrayCollection $leads;
    #[HasMany(target: Agent::class, collection: 'doctrine')]
    public ArrayCollection $agents;
    #[HasMany(target: Conversation::class, collection: 'doctrine')]
    public ArrayCollection $conversations;
    // billing info

    public function __construct(
    ) {
        $this->users = new ArrayCollection();
        $this->leads = new ArrayCollection();
        $this->agents = new ArrayCollection();
    }
}
