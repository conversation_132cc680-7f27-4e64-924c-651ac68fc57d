<?php

namespace App\Database\Entity;

use App\Application\Kernel;
use App\Database\Enum\LeadPriority;
use App\Database\Enum\LeadStatus;
use App\Database\Repository\LeadRepository;
use App\Database\Scope\OrganizationScope;
use App\Database\Scope\SoftDeleteScope;
use Cycle\Annotated\Annotation\Column;
use Cycle\Annotated\Annotation\Entity;
use Cycle\Annotated\Annotation\Relation\BelongsTo;
use Cycle\Annotated\Annotation\Relation\HasMany;
use Cycle\Annotated\Annotation\Relation\ManyToMany;
use Cycle\ORM\Entity\Behavior\Uuid\Uuid4;
use Cycle\ORM\Entity\Behavior;
use Doctrine\Common\Collections\ArrayCollection;
use Ramsey\Uuid\UuidInterface;

#[Entity(repository: LeadRepository::class, scope: OrganizationScope::class)]
#[Uuid4(field: 'id', nullable: false)]
#[Behavior\CreatedAt(field: 'createdAt', column: 'created_at')]
#[Behavior\UpdatedAt(field: 'updatedAt', column: 'updated_at')]
#[Behavior\SoftDelete(field: 'deletedAt', column: 'deleted_at')]
class Lead
{
    #[Column(type: 'uuid', primary: true, nullable: false)]
    public UuidInterface $id;
    #[BelongsTo(target: Person::class, innerKey: 'person_id')]
    public Person $person;
    #[BelongsTo(target: Organization::class, innerKey: 'organization_id')]
    public Organization $organization;
    #[Column(type: 'string', nullable: true)]
    public ?string $service;
    #[Column(type: 'enum(new,contacted,qualified,converted,lost)', nullable: false, default: LeadStatus::NEW->value, typecast: LeadStatus::class)]
    public LeadStatus $status;
    #[Column(type: 'enum(high,medium,low)', nullable: false, default: LeadPriority::LOW->value, typecast: LeadPriority::class)]
    public LeadPriority $priority;
    #[Column(type: 'float', nullable: true)]
    public ?float $estimatedValue;
    #[Column(type: 'float', nullable: true)]
    public ?float $actualValue;
    #[Column(type: 'float', nullable: true)]
    public ?float $estimatedCost;
    #[Column(type: 'datetime', nullable: true)]
    public ?\DateTimeImmutable $lastContact;
    #[Column(type: 'integer', nullable: true)]
    public ?int $score;
    #[Column(type: 'datetime', nullable: false)]
    public \DateTimeImmutable $createdAt;
    #[Column(type: 'datetime', nullable: true)]
    public ?\DateTimeImmutable $updatedAt;
    #[Column(type: 'datetime', nullable: true)]
    public ?\DateTimeImmutable $deletedAt = null;
    // relationships
    #[BelongsTo(target: PhoneNumber::class, innerKey: 'phone_number_id')]
    public PhoneNumber $phoneNumber;
    #[ManyToMany(target: Note::class, through: LeadNotes::class, collection: 'doctrine')]
    public ArrayCollection $notes;
    #[HasMany(target: Conversation::class)]
    public ArrayCollection $conversations;
    #[HasMany(target: Appointment::class, collection: 'doctrine')]
    public ArrayCollection $appointments;
    #[BelongsTo(target: Agent::class, innerKey: 'agent_id' )]
    public Agent $agent;

//    ai analysis
//    ai summary
//    ai sentiment analysis
    public function __construct(
    ) {
        $this->notes = new ArrayCollection();
        $this->conversations = new ArrayCollection();
        $this->appointments = new ArrayCollection();
    }

    public function calculateMessageCount(): int
    {
        return $this->augmentCache(
            key: 'lead:' . $this->id . ':messageCount',
            calculation: function () {
                return $this->conversations->reduce(function ($sum, $conversation) {
                    return $sum + $conversation->messages->count();
                }, 0);
            }
        );
    }

    public function calculateCallCount(): int
    {
        return $this->augmentCache(
            key: 'lead:' . $this->id . ':callCount',
            calculation: function () {
                return $this->conversations->reduce(function ($sum, $conversation) {
                    return $sum + $conversation->calls->count();
                }, 0);
            }
        );
    }

    public function calculateVoicemailCount(): int
    {
        return $this->augmentCache(
            key: 'lead:' . $this->id . ':voicemailCount',
            calculation: function () {
                return $this->conversations->reduce(function ($sum, $conversation) {
                    return $sum + $conversation->calls->reduce(function ($sum, $call) {
                        return $sum + ($call->voicemail ? 1 : 0);
                    }, 0);
                }, 0);
            }
        );
    }

    public function calculateAppointmentCount(): int
    {
        return $this->augmentCache(
            key: 'lead:' . $this->id . ':appointmentCount',
            calculation: function () {
                return $this->appointments->count();
            }
        );
    }

    public function calculateAverageResponseTime(): string
    {
        return $this->augmentCache(
            key: 'lead:' . $this->id . ':averageResponseTime',
            calculation: function () {
                if ($this->conversations->isEmpty()) {
                    return '0 minutes';
                }

                $totalResponseTime = 0;
                $responseCount = 0;

                foreach ($this->conversations as $conversation) {
                    $messages = $conversation->messages->toArray();
                    for ($i = 1; $i < count($messages); $i++) {
                        $responseTime = $messages[$i]->createdAt->getTimestamp() - $messages[$i-1]->createdAt->getTimestamp();
                        $totalResponseTime += $responseTime;
                        $responseCount++;
                    }
                }

                if ($responseCount === 0) {
                    return '0 minutes';
                }

                $avgSeconds = $totalResponseTime / $responseCount;
                $avgMinutes = round($avgSeconds / 60, 1);

                return $avgMinutes . ' minutes';
            }
        );
    }

    public function calculateTotalCost(): string
    {
        return $this->augmentCache(
            key: 'lead:' . $this->id . ':totalCost',
            calculation: function () {
                $messageCost = $this->calculateMessageCount() * 0.0082;
                $callCost = $this->conversations->reduce(function ($sum, $conversation) {
                    return $sum + $conversation->calls->reduce(function ($sum, $call) {
                        return $sum + $call->cost;
                    }, 0);
                }, 0);

                $totalCost = $messageCost + $callCost;
                return '$' . number_format($totalCost, 2);
            }
        );
    }

    public function calculateLeadScore(): int
    {
        return $this->augmentCache(
            key: 'lead:' . $this->id . ':leadScore',
            calculation: function () {
                $score = 50; // Base score

                // Add points for engagement
                $score += min($this->calculateMessageCount() * 2, 20);
                $score += min($this->calculateCallCount() * 5, 15);
                $score += $this->calculateAppointmentCount() * 10;

                // Priority bonus
                $score += match($this->priority) {
                    LeadPriority::HIGH => 10,
                    LeadPriority::MEDIUM => 5,
                    LeadPriority::LOW => 0,
                };

                return min($score, 100);
            }
        );
    }

    public function calculateAiAnalysis(): array
    {
        return $this->augmentCache(
            key: 'lead:' . $this->id . ':aiAnalysis',
            calculation: function () {
                // Placeholder AI analysis data
                $sentiments = ['Positive', 'Neutral', 'Negative'];
                $sentiment = $sentiments[array_rand($sentiments)];

                $summaries = [
                    'High-priority lead with confirmed budget and urgency. Customer shows strong buying intent and has flexible scheduling.',
                    'Engaged prospect with multiple touchpoints. Shows interest but may need more nurturing to convert.',
                    'Active lead with good response rate. Customer is comparing options and timeline is flexible.',
                    'Warm lead with specific service needs. Budget confirmed and ready to schedule consultation.',
                    'Qualified prospect with immediate needs. High conversion probability based on engagement patterns.'
                ];

                $summary = $summaries[array_rand($summaries)];

                // Add context based on actual data
                $messageCount = $this->calculateMessageCount();
                $callCount = $this->calculateCallCount();
                $appointmentCount = $this->calculateAppointmentCount();

                if ($messageCount > 10) {
                    $summary .= ' Highly engaged with ' . $messageCount . ' messages exchanged.';
                }

                if ($callCount > 2) {
                    $summary .= ' Multiple phone interactions indicate serious interest.';
                }

                if ($appointmentCount > 0) {
                    $summary .= ' Has scheduled appointments, showing strong conversion intent.';
                }

                return [
                    'summary' => $summary,
                    'sentiment' => $sentiment . ' - Customer engagement indicates ' . strtolower($sentiment) . ' experience',
                    'confidence' => rand(75, 95),
                    'recommendations' => [
                        'Follow up within 24 hours',
                        'Provide detailed service estimate',
                        'Schedule consultation at customer convenience'
                    ],
                    'keyInsights' => [
                        'Customer prefers ' . ($messageCount > $callCount ? 'text' : 'phone') . ' communication',
                        'Response time is important to this lead',
                        'Price-conscious but values quality service'
                    ]
                ];
            },
            ttl: 3600 // Cache for 1 hour
        );
    }

    private function augmentCache($key, $calculation, $ttl = 300)
    {
        return Kernel::cache()->augmentCache($key, $calculation, $ttl);
    }
}
