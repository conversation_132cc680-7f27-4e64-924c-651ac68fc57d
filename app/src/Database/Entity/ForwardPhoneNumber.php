<?php

namespace App\Database\Entity;

use App\Database\Scope\SoftDeleteScope;
use Cycle\Annotated\Annotation\Column;
use Cycle\Annotated\Annotation\Entity;
use Cycle\Annotated\Annotation\Relation\BelongsTo;
use Cycle\ORM\Entity\Behavior\Uuid\Uuid4;
use Cycle\ORM\Entity\Behavior;
use Ramsey\Uuid\UuidInterface;

#[Entity()]
#[Uuid4(field: 'id', nullable: false)]
#[Behavior\CreatedAt(field: 'createdAt', column: 'created_at')]
#[Behavior\UpdatedAt(field: 'updatedAt', column: 'updated_at')]
#[Behavior\SoftDelete(field: 'deletedAt', column: 'deleted_at')]
class ForwardPhoneNumber
{
    #[Column(type: 'uuid', primary: true, nullable: false)]
    public UuidInterface $id;
    #[Column(type: 'integer', nullable: false)]
    public int $order;
    #[BelongsTo(target: ForwardSequence::class, innerKey: 'forward_sequence_id')]
    public ForwardSequence $forwardSequence;
    #[BelongsTo(target: PhoneNumber::class, innerKey: 'phone_number_id')]
    public PhoneNumber $phoneNumber;
    #[Column(type: 'integer', nullable: false)]
    public int $waitTime;
    #[Column(type: 'datetime', nullable: false)]
    public \DateTimeImmutable $createdAt;
    #[Column(type: 'datetime', nullable: true)]
    public ?\DateTimeImmutable $updatedAt;
    #[Column(type: 'datetime', nullable: true)]
    public ?\DateTimeImmutable $deletedAt = null;

    public function __construct(
    ) {}
}
