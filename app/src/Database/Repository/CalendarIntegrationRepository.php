<?php

namespace App\Database\Repository;

use App\Database\Entity\CalendarIntegration;
use App\Database\Enum\CalendarIntegrationStatus;
use Cycle\ORM\Select\Repository;
use Spiral\Prototype\Annotation\Prototyped;

#[Prototyped(property: 'calendarIntegrations')]
class CalendarIntegrationRepository extends Repository
{
    /**
     * Get active integrations for an organization
     */
    public function getActiveIntegrationsForOrganization(string $organizationId): array
    {
        return $this->select()
            ->where('organization.id', $organizationId)
            ->where('status', CalendarIntegrationStatus::ACTIVE->value)
            ->where('syncEnabled', true)
            ->load('organization')
            ->load('user')
            ->fetchAll();
    }

    /**
     * Get integrations for a specific user
     */
    public function getIntegrationsForUser(string $userId): array
    {
        return $this->select()
            ->where('user.id', $userId)
            ->load('organization')
            ->load('user')
            ->orderBy('createdAt', 'DESC')
            ->fetchAll();
    }

    /**
     * Get integration by provider for organization
     */
    public function getByProviderForOrganization(string $organizationId, string $provider): ?CalendarIntegration
    {
        return $this->select()
            ->where('organization.id', $organizationId)
            ->where('provider', $provider)
            ->where('status', CalendarIntegrationStatus::ACTIVE->value)
            ->load('organization')
            ->load('user')
            ->fetchOne();
    }

    /**
     * Get integrations that need token refresh
     */
    public function getIntegrationsNeedingRefresh(): array
    {
        // This would need a more complex query in a real implementation
        // For now, we'll fetch active integrations and filter in PHP
        $integrations = $this->select()
            ->where('status', CalendarIntegrationStatus::ACTIVE->value)
            ->where('syncEnabled', true)
            ->load('organization')
            ->load('user')
            ->fetchAll();

        return array_filter($integrations, function(CalendarIntegration $integration) {
            return $integration->areCredentialsExpired();
        });
    }

    /**
     * Get integrations with sync errors
     */
    public function getIntegrationsWithErrors(): array
    {
        return $this->select()
            ->where('status', CalendarIntegrationStatus::ERROR->value)
            ->load('organization')
            ->load('user')
            ->orderBy('lastSyncAt', 'DESC')
            ->fetchAll();
    }

    /**
     * Get integrations that haven't synced recently
     */
    public function getStaleIntegrations(int $hoursThreshold = 24): array
    {
        $threshold = new \DateTimeImmutable("-{$hoursThreshold} hours");
        
        return $this->select()
            ->where('status', CalendarIntegrationStatus::ACTIVE->value)
            ->where('syncEnabled', true)
            ->where('lastSyncAt', '<', $threshold)
            ->load('organization')
            ->load('user')
            ->fetchAll();
    }
}
