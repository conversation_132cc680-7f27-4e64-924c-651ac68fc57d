<?php

namespace App\Database\Scope;

use Cycle\ORM\Select\QueryBuilder;
use Cycle\ORM\Select\ScopeInterface;
use Spiral\Prototype\Traits\PrototypeTrait;

class OrganizationScope implements ScopeInterface
{
    use PrototypeTrait;

    public function apply(QueryBuilder $query): void
    {
        $user = $this->request->attribute('user');
        if ($user->organization) {
            $query->where('organization_id', '=', $user->organization->id);
        }
    }
}
