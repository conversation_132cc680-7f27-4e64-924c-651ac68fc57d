'use client';

import React, {
  createContext,
  useContext,
  useEffect,
  useMemo,
  useRef,
  useCallback,
  PropsWithChildren,
} from 'react';
import {useAuth} from "@app/lib/use-auth";


/**
 * If your useAuth() already exports validateTokenQuietly, we'll use it.
 * Otherwise we fall back to validateToken({ quiet: true }).
 */
type UseAuthReturn = ReturnType<typeof useAuth>;
type AuthContextValue = UseAuthReturn;

const AuthContext = createContext<AuthContextValue | undefined>(undefined);

export function AuthProvider({ children }: PropsWithChildren) {
  const auth = useAuth();
  const lastRunRef = useRef(0);
  // Normalize a "quiet" validator so the visibility/focus check never flickers the global UI.
  const validateQuietly = useCallback(async () => {
    // Prefer an explicit quiet method if your hook exposes it
    const maybeQuiet = (auth as any).validateTokenQuietly as
      | (() => Promise<void>)
      | undefined;

    if (typeof maybeQuiet === 'function') {
      await maybeQuiet();
      return;
    }

    // Fallback: call validateToken with a "quiet" option if supported
    const maybeValidate = (auth as any).validateToken as
      | ((opts?: { quiet?: boolean }) => Promise<void>)
      | undefined;

    if (typeof maybeValidate === 'function') {
      await maybeValidate({ quiet: true });
    }
    // If neither exists, do nothing; the app’s API calls will 401->logout on their own.
  }, [auth]);

  /**
   * Throttled revalidation on tab visibility/focus
   * - Only runs if authenticated
   * - Throttled (default 5 min) to avoid spamming on rapid focus changes
   * - Uses "quiet" path to avoid global loading flicker
   */
  useEffect(() => {
    if (!auth.isAuthenticated) return;

    const THROTTLE_MS = 5 * 60 * 1000; // 5 minutes

    const maybeRevalidate = async () => {
      if (document.visibilityState !== 'visible') return;
      const now = Date.now();
      if (now - lastRunRef.current < THROTTLE_MS) return;
      lastRunRef.current = now;
      try {
        await validateQuietly();
      } catch {
        // Swallow errors here; your apiClient 401 path should handle logout if needed.
      }
    };

    // Run once on mount if we're visible
    if (typeof document !== 'undefined' && document.visibilityState === 'visible') {
      // Don’t await; fire-and-forget to keep UI snappy
      void maybeRevalidate();
    }

    // Listen to both visibilitychange and window focus (covers different browser nuances)
    const onVisibility = () => void maybeRevalidate();
    const onFocus = () => void maybeRevalidate();

    document.addEventListener('visibilitychange', onVisibility);
    window.addEventListener('focus', onFocus);

    return () => {
      document.removeEventListener('visibilitychange', onVisibility);
      window.removeEventListener('focus', onFocus);
    };
  }, [auth.isAuthenticated, validateQuietly]);

  const value = useMemo<AuthContextValue>(() => auth, [auth]);

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
}

export function useAuthContext(): AuthContextValue {
  const ctx = useContext(AuthContext);
  if (!ctx) {
    throw new Error('useAuthContext must be used within an <AuthProvider>');
  }
  return ctx;
}
