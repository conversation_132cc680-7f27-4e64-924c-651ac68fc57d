'use client';

import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { apiClient } from './api-client';
import { useTokenHandlerContext } from './token-handler';

type User = {
  id: string;
  email: string;
  name?: string;
  // ...extend as your API returns
};

type ValidateResponse = {
  user: User;
  // optionally if your backend rotates tokens on validate:
  token?: string | null;
  // optionally token expiry, etc.
};

export type UseAuth = {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  token: string | null;

  // actions
  logout: () => Promise<void>;
  validateToken: (opts?: { quiet?: boolean }) => Promise<void>;
  validateTokenQuietly: () => Promise<void>;
  loginWithTokenFromUrl: () => void; // convenience if you prefer hook-based URL handling
};

export function useAuth(): UseAuth {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [token, setTokenState] = useState<string | null>(null);

  // Get token handler context to coordinate timing
  const { isTokenHandled, hasUrlToken } = useTokenHandlerContext();

  // bootstrap from apiClient (which may have loaded from storage or TokenHandler)
  useEffect(() => {
    setTokenState(apiClient.getToken());
  }, []);

  const setToken = useCallback((t: string | null, { persist = true }: { persist?: boolean } = {}) => {
    apiClient.setToken(t, { persist });
    setTokenState(t);
  }, []);

  // centralized 401 -> logout
  useEffect(() => {
    apiClient.setOnUnauthorized(() => {
      // clear and bounce to login
      setToken(null, { persist: false });
      setUser(null);
      if (typeof window !== 'undefined') {
        window.location.href = '/login';
      }
    });
    return () => apiClient.setOnUnauthorized(undefined);
  }, [setToken]);

  const validateToken = useCallback(
    async (opts?: { quiet?: boolean }) => {
      if (!apiClient.getToken()) {
        setUser(null);
        setIsLoading(false);
        return;
      }

      if (!opts?.quiet) setIsLoading(true);

      try {
        const data = await apiClient.post<ValidateResponse>('/auth/validate');

        // if server rotated token, store it
        if (data?.token) {
          setToken(data.token, { persist: true });
        }

        setUser(data.user);
      } catch (err) {
        // On any error, clear token and user; apiClient 401 handler may also run
        setToken(null, { persist: false });
        setUser(null);
      } finally {
        setIsLoading(false);
      }
    },
    [setToken]
  );

  const validateTokenQuietly = useCallback(async () => {
    await validateToken({ quiet: true });
  }, [validateToken]);

  // Initial validation on mount - WAIT for token handling to complete
  useEffect(() => {
    // Don't validate until token handling is complete
    if (!isTokenHandled) return;

    let cancelled = false;
    (async () => {
      // If we had a URL token, refresh the token state first
      if (hasUrlToken) {
        setTokenState(apiClient.getToken());
      }

      await validateToken({ quiet: false });
      if (!cancelled) {
        // no-op, isLoading updated in validateToken
      }
    })();
    return () => {
      cancelled = true;
    };
  }, [validateToken, isTokenHandled, hasUrlToken]);

  // Optional: login via URL query (if you prefer hook-based instead of the <TokenHandler/> component)
  const loginWithTokenFromUrl = useCallback(() => {
    if (typeof window === 'undefined') return;
    const url = new URL(window.location.href);
    const incoming = url.searchParams.get('token');
    if (incoming) {
      setToken(incoming, { persist: true });
      // clean URL
      url.searchParams.delete('token');
      window.history.replaceState({}, '', url.toString());
      // then validate to fetch user
      void validateToken({ quiet: false });
    }
  }, [setToken, validateToken]);

  const value = useMemo<UseAuth>(
    () => ({
      user,
      isAuthenticated: !!user && !!token,
      isLoading,
      token,

      logout: async () => {
        // optional: call an API endpoint to invalidate token server-side
        try {
          await apiClient.post('/auth/logout');
        } catch {
          // ignore errors, we'll still clear locally
        } finally {
          setUser(null);
          setToken(null, { persist: false });
          // redirect to login after logout
          if (typeof window !== 'undefined') {
            window.location.href = process.env.NEXT_PUBLIC_BACK_TALK_WEB_ENDPOINT+'/login';
          }
        }
      },

      validateToken,
      validateTokenQuietly,
      loginWithTokenFromUrl,
    }),
    [user, token, isLoading, setToken, validateToken, validateTokenQuietly, loginWithTokenFromUrl]
  );

  return value;
}
