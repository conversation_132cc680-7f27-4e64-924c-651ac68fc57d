export interface Call {
  id: string;
  lead?: {
    id: string;
    name: string;
  };
  agent?: {
    id: string;
    name: string;
  };
  conversation?: {
    id: string;
  };
  answered: boolean;
  answeredBy?: {
    id: string;
    number: string;
  };
  voicemail?: {
    id: string;
    transcription: string;
    audioUrl: string;
    duration: number;
  };
  duration: number;
  cost: number;
  status: string;
  startTime: string;
  answerTime?: string;
  endTime?: string;
  createdAt: string;
  updatedAt?: string;
}

export interface CallResponse {
  status: number;
  data: Call[];
}

export interface SingleCallResponse {
  status: number;
  data: Call;
}
