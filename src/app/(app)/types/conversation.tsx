export default interface Conversation {
    id: string,
    lead: {
        id: string,
        person: {
            id: string,
            name: {
                full: string,
                first: string,
                last: string,
            }
        }
    },
    agent: {
        id: string,
        name: string,
    },
    leadPhone: string,
    lastMessage: string,
    lastMessageTime: string,
    messageCount: number,
    status: string,
    isAgentActive: boolean,
    searchMatch?: {
        type: 'name' | 'message',
        matchedText: string,
        messageId?: string,
        messageContent?: string,
        messageCreatedAt?: string,
    },
}

export interface ConversationResponse {
    status: number;
    data: Conversation[];
}

export interface SingleConversationResponse {
    status: number;
    data: Conversation;
}
