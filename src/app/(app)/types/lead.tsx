import Person from "@app/types/person";

export const getStatusColor = (status: string) => {
  switch (status) {
    case "new":
      return "default"
    case "contacted":
      return "secondary"
    case "qualified":
      return "warning"
    case "converted":
      return "success"
    case "lost":
      return "destructive"
    default:
      return "secondary"
  }
}

export const getPriorityColor = (priority: string) => {
  switch (priority) {
    case "high":
      return "text-red-500"
    case "medium":
      return "text-yellow-500"
    case "low":
      return "text-green-500"
    default:
      return "text-gray-500"
  }
}

export const getScoreColor = (score: number) => {
  if (score >= 80) return "text-green-500"
  if (score >= 60) return "text-yellow-500"
  return "text-red-500"
}

export interface LeadResponse {
  status: number,
  data: Lead[],
}

export interface LeadMetrics {
  messageCount: number;
  callCount: number;
  voicemailCount: number;
  appointmentCount: number;
  leadScore: number;
  averageResponseTime: string;
  totalCost: string;
  lastContact?: string;
}

export interface LeadMetricsResponse {
  status: number;
  data: LeadMetrics;
}

export interface LeadAnalysis {
  summary: string;
  sentiment: string;
  confidence: number;
  recommendations: string[];
  keyInsights: string[];
}

export interface LeadAnalysisResponse {
  status: number;
  data: LeadAnalysis;
}

export interface RelatedLeadsResponse {
  status: number;
  data: Lead[];
}

export default interface Lead {
    id: string,
    person: Person,
    phone: string,
    organization: string,
    service: string,
    status: string,
    priority: string,
    estimatedValue: number,
    actualValue: number,
    estimatedCost: number,
    lastContact: string,
    score: number,
    notes: string[],
    agent?: {
        id: string,
        name: string,
        phone?: string,
        metrics?: {
            totalLeads: number,
            totalConversations: number,
            totalCalls: number,
            totalAppointments: number,
            averageResponseTime: string,
            conversionRate: number,
            leadsThisMonth: number,
            interactionsWithThisLead: number,
        }
    },
    metadata: {
        createdAt: string,
        createdBy: string,
        updatedAt: string,
        updatedBy: string,
    }
}
