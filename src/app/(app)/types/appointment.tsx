export interface Appointment {
  id: string;
  lead?: {
    id: string;
    name: string;
  };
  agent?: {
    id: string;
    name: string;
  };
  conversation?: {
    id: string;
  };
  service: string;
  startTime: string;
  endTime: string;
  value: number;
  status: string;
  notes: string[];
  createdAt: string;
  updatedAt?: string;
}

export interface AppointmentResponse {
  status: number;
  data: Appointment[];
}

export interface SingleAppointmentResponse {
  status: number;
  data: Appointment;
}
