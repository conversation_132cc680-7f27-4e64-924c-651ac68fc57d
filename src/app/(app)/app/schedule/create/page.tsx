"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { ArrowLeft, Calendar, Clock, User, Phone, MapPin, FileText, Loader2 } from "lucide-react"
import { Button } from "@app/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@app/components/ui/card"
import { Input } from "@app/components/ui/input"
import { Label } from "@app/components/ui/label"
import { Textarea } from "@app/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@app/components/ui/select"
import { Separator } from "@app/components/ui/separator"
import { apiClient } from "@app/lib/api-client"
import Lead, { LeadResponse } from "@app/types/lead"
import Agent from "@app/types/agent"



const serviceTypes = [
    "Vehicle Inspection",
    "Test Drive",
    "Financing Discussion",
    "Trade-in Evaluation",
    "Service Consultation",
    "Parts Consultation",
    "Insurance Discussion",
    "Warranty Review",
]

export default function CreateAppointmentPage() {
    const router = useRouter()
    const [formData, setFormData] = useState({
        leadId: "",
        agentId: "",
        service: "",
        date: "",
        time: "",
        value: "",
        notes: "",
    })
    const [leads, setLeads] = useState<Lead[]>([])
    const [agents, setAgents] = useState<Agent[]>([])
    const [loading, setLoading] = useState(true)
    const [submitting, setSubmitting] = useState(false)
    const [error, setError] = useState<string | null>(null)

    // Fetch leads and agents on component mount
    useEffect(() => {
        fetchData()
    }, [])

    const fetchData = async () => {
        setLoading(true)
        setError(null)
        try {
            const [leadsResponse, agentsResponse] = await Promise.all([
                apiClient.get<LeadResponse>('/api/v1/lead'),
                apiClient.get<{status: number, data: Agent[]}>('/api/v1/agent')
            ])
            setLeads(leadsResponse.data)
            setAgents(agentsResponse.data)
        } catch (err) {
            setError(err instanceof Error ? err.message : 'Failed to fetch data')
        } finally {
            setLoading(false)
        }
    }

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault()
        setSubmitting(true)
        setError(null)

        try {
            // Combine date and time into start_time and calculate end_time
            const startDateTime = new Date(`${formData.date}T${formData.time}`)
            const endDateTime = new Date(startDateTime.getTime() + 60 * 60 * 1000) // Add 1 hour

            const appointmentData = {
                lead_id: formData.leadId,
                agent_id: formData.agentId,
                service: formData.service,
                start_time: startDateTime.toISOString(),
                end_time: endDateTime.toISOString(),
                value: formData.value ? parseFloat(formData.value) : 0,
                notes: formData.notes ? [formData.notes] : [],
                status: 'pending'
            }

            await apiClient.post('/api/v1/appointment', appointmentData)

            // Redirect back to appointments page
            router.push("/schedule")
        } catch (err) {
            setError(err instanceof Error ? err.message : 'Failed to create appointment')
        } finally {
            setSubmitting(false)
        }
    }

    const selectedLead = leads.find((lead) => lead.id === formData.leadId)
    const selectedAgent = agents.find((agent) => agent.id === formData.agentId)

    if (loading) {
        return (
            <div className="flex-1 space-y-6 p-6">
                <div className="flex items-center justify-center py-12">
                    <Loader2 className="h-8 w-8 animate-spin" />
                    <span className="ml-2 text-lg">Loading...</span>
                </div>
            </div>
        )
    }

    return (
        <div className="flex-1 space-y-6 p-6">
            {/* Header */}
            <div className="flex items-center gap-4">
                <Button variant="ghost" size="sm" onClick={() => router.back()}>
                    <ArrowLeft className="mr-2 h-4 w-4" />
                    Back
                </Button>
                <div>
                    <h1 className="text-3xl font-bold tracking-tight">Create Appointment</h1>
                    <p className="text-muted-foreground">Schedule a new appointment with a lead</p>
                </div>
            </div>

            {error && (
                <Card className="border-red-200 bg-red-50">
                    <CardContent className="pt-6">
                        <p className="text-red-600">{error}</p>
                    </CardContent>
                </Card>
            )}

            <div className="grid gap-6 lg:grid-cols-3">
                {/* Main Form */}
                <div className="lg:col-span-2">
                    <Card>
                        <CardHeader>
                            <CardTitle>Appointment Details</CardTitle>
                            <CardDescription>Fill in the information below to create a new appointment</CardDescription>
                        </CardHeader>
                        <CardContent>
                            <form onSubmit={handleSubmit} className="space-y-6">
                                {/* Lead Selection */}
                                <div className="space-y-2">
                                    <Label htmlFor="leadId">Select Lead *</Label>
                                    <Select
                                        value={formData.leadId}
                                        onValueChange={(value) => setFormData({ ...formData, leadId: value })}
                                    >
                                        <SelectTrigger>
                                            <SelectValue placeholder="Choose a lead for this appointment" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            {leads.map((lead) => (
                                                <SelectItem key={lead.id} value={lead.id}>
                                                    <div className="flex items-center gap-2">
                                                        <User className="h-4 w-4" />
                                                        <span>{lead.person.firstName} {lead.person.lastName}</span>
                                                    </div>
                                                </SelectItem>
                                            ))}
                                        </SelectContent>
                                    </Select>
                                </div>

                                {/* Agent Selection */}
                                <div className="space-y-2">
                                    <Label htmlFor="agentId">Select Agent *</Label>
                                    <Select
                                        value={formData.agentId}
                                        onValueChange={(value) => setFormData({ ...formData, agentId: value })}
                                    >
                                        <SelectTrigger>
                                            <SelectValue placeholder="Choose an agent for this appointment" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            {agents.map((agent) => (
                                                <SelectItem key={agent.id} value={agent.id}>
                                                    <div className="flex items-center gap-2">
                                                        <Phone className="h-4 w-4" />
                                                        <span>{agent.name}</span>
                                                        <span className="text-muted-foreground">({agent.phone})</span>
                                                    </div>
                                                </SelectItem>
                                            ))}
                                        </SelectContent>
                                    </Select>
                                </div>

                                <Separator />

                                {/* Service Type */}
                                <div className="space-y-2">
                                    <Label htmlFor="service">Service Type *</Label>
                                    <Select
                                        value={formData.service}
                                        onValueChange={(value) => setFormData({ ...formData, service: value })}
                                    >
                                        <SelectTrigger>
                                            <SelectValue placeholder="Select the type of service" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            {serviceTypes.map((service) => (
                                                <SelectItem key={service} value={service}>
                                                    {service}
                                                </SelectItem>
                                            ))}
                                        </SelectContent>
                                    </Select>
                                </div>

                                {/* Date and Time */}
                                <div className="grid gap-4 md:grid-cols-2">
                                    <div className="space-y-2">
                                        <Label htmlFor="date">Date *</Label>
                                        <div className="relative">
                                            <Calendar className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                                            <Input
                                                id="date"
                                                type="date"
                                                value={formData.date}
                                                onChange={(e) => setFormData({ ...formData, date: e.target.value })}
                                                className="pl-10"
                                                required
                                            />
                                        </div>
                                    </div>
                                    <div className="space-y-2">
                                        <Label htmlFor="time">Time *</Label>
                                        <div className="relative">
                                            <Clock className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                                            <Input
                                                id="time"
                                                type="time"
                                                value={formData.time}
                                                onChange={(e) => setFormData({ ...formData, time: e.target.value })}
                                                className="pl-10"
                                                required
                                            />
                                        </div>
                                    </div>
                                </div>

                                {/* Value */}
                                <div className="space-y-2">
                                    <Label htmlFor="value">Appointment Value ($)</Label>
                                    <Input
                                        id="value"
                                        type="number"
                                        step="0.01"
                                        placeholder="0.00"
                                        value={formData.value}
                                        onChange={(e) => setFormData({ ...formData, value: e.target.value })}
                                    />
                                </div>

                                {/* Notes */}
                                <div className="space-y-2">
                                    <Label htmlFor="notes">Notes</Label>
                                    <div className="relative">
                                        <FileText className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                                        <Textarea
                                            id="notes"
                                            placeholder="Add any additional notes or special instructions"
                                            value={formData.notes}
                                            onChange={(e) => setFormData({ ...formData, notes: e.target.value })}
                                            className="pl-10 min-h-[100px]"
                                        />
                                    </div>
                                </div>

                                {/* Submit Buttons */}
                                <div className="flex gap-4 pt-4">
                                    <Button type="submit" className="flex-1" disabled={submitting}>
                                        {submitting ? (
                                            <>
                                                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                                Creating...
                                            </>
                                        ) : (
                                            'Create Appointment'
                                        )}
                                    </Button>
                                    <Button type="button" variant="outline" onClick={() => router.back()} disabled={submitting}>
                                        Cancel
                                    </Button>
                                </div>
                            </form>
                        </CardContent>
                    </Card>
                </div>

                {/* Summary Sidebar */}
                <div className="space-y-6">
                    <Card>
                        <CardHeader>
                            <CardTitle>Appointment Summary</CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            {selectedLead ? (
                                <div className="space-y-2">
                                    <h4 className="font-medium">Lead Information</h4>
                                    <div className="text-sm space-y-1">
                                        <div className="flex items-center gap-2">
                                            <User className="h-4 w-4 text-muted-foreground" />
                                            <span>{selectedLead.name}</span>
                                        </div>
                                        <div className="flex items-center gap-2">
                                            <Phone className="h-4 w-4 text-muted-foreground" />
                                            <span>{selectedLead.phone}</span>
                                        </div>
                                        <div className="text-muted-foreground">{selectedLead.email}</div>
                                    </div>
                                </div>
                            ) : (
                                <div className="text-sm text-muted-foreground">Select a lead to see their information</div>
                            )}

                            <Separator />

                            {selectedAgent ? (
                                <div className="space-y-2">
                                    <h4 className="font-medium">Agent Information</h4>
                                    <div className="text-sm space-y-1">
                                        <div className="flex items-center gap-2">
                                            <Phone className="h-4 w-4 text-muted-foreground" />
                                            <span>{selectedAgent.name}</span>
                                        </div>
                                        <div className="text-muted-foreground">{selectedAgent.phone}</div>
                                    </div>
                                </div>
                            ) : (
                                <div className="text-sm text-muted-foreground">Select an agent to see their information</div>
                            )}

                            <Separator />

                            {(formData.date || formData.time || formData.service) && (
                                <div className="space-y-2">
                                    <h4 className="font-medium">Appointment Details</h4>
                                    <div className="text-sm space-y-1">
                                        {formData.service && (
                                            <div>
                                                Service: <span className="font-medium">{formData.service}</span>
                                            </div>
                                        )}
                                        {formData.date && (
                                            <div>
                                                Date: <span className="font-medium">{formData.date}</span>
                                            </div>
                                        )}
                                        {formData.time && (
                                            <div>
                                                Time: <span className="font-medium">{formData.time}</span>
                                            </div>
                                        )}
                                        {formData.location && (
                                            <div>
                                                Location: <span className="font-medium">{formData.location}</span>
                                            </div>
                                        )}
                                    </div>
                                </div>
                            )}
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader>
                            <CardTitle className="text-sm">Quick Tips</CardTitle>
                        </CardHeader>
                        <CardContent className="text-sm text-muted-foreground space-y-2">
                            <p>• Double-check the date and time before creating</p>
                            <p>• Include location details for better coordination</p>
                            <p>• Add notes about specific lead requirements</p>
                            <p>• Confirm agent availability before scheduling</p>
                        </CardContent>
                    </Card>
                </div>
            </div>
        </div>
    )
}
