"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON> } from "next/navigation"
import Link from "next/link"
import {
    ArrowLeft,
    Calendar,
    Clock,
    User,
    Phone,
    MapPin,
    MessageSquare,
    DollarSign,
    Edit3,
    Save,
    X,
    Bot,
    Sparkles,
    Loader2,
} from "lucide-react"
import { <PERSON><PERSON> } from "@app/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@app/components/ui/card"
import { Badge } from "@app/components/ui/badge"
import { Input } from "@app/components/ui/input"
import { Label } from "@app/components/ui/label"
import { Textarea } from "@app/components/ui/textarea"
import { Separator } from "@app/components/ui/separator"
import { Avatar, AvatarFallback, AvatarInitials } from "@app/components/ui/avatar"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@app/components/ui/select"
import { apiClient } from "@app/lib/api-client"
import { Appointment, SingleAppointmentResponse } from "@app/types/appointment"

const serviceTypes = [
    "Vehicle Inspection",
    "Test Drive",
    "Financing Discussion",
    "Trade-in Evaluation",
    "Service Consultation",
    "Parts Consultation",
    "Insurance Discussion",
    "Warranty Review",
]

// Mock conversation data
const mockConversations = [
    {
        id: "conv-1",
        type: "call",
        direction: "inbound",
        duration: "4:32",
        timestamp: "2024-01-12T14:30:00Z",
        summary: "Lead called asking about vehicle availability and pricing",
    },
    {
        id: "conv-2",
        type: "sms",
        messageCount: 8,
        timestamp: "2024-01-11T16:45:00Z",
        lastMessage: "Great! I'll see you on Monday at 10 AM for the inspection.",
    },
    {
        id: "conv-3",
        type: "call",
        direction: "outbound",
        duration: "2:15",
        timestamp: "2024-01-10T11:20:00Z",
        summary: "Follow-up call to schedule appointment",
    },
]

// Mock AI summary
const aiSummary = {
    jobType: "Vehicle Purchase Consultation",
    leadIntent: "High - actively looking to purchase within 2 weeks",
    keyRequirements: [
        "Reliable sedan under $30,000",
        "Good fuel economy for daily commuting",
        "Financing options with low down payment",
        "Trade-in evaluation for current vehicle",
    ],
    recommendedApproach:
        "Focus on certified pre-owned vehicles with warranty coverage. Emphasize financing flexibility and total cost of ownership.",
    riskFactors: ["First-time buyer may need education on financing", "Price-sensitive - may shop around"],
    opportunityScore: 85,
}

interface AppointmentDetailsPageProps {
    params: Promise<{
        id: string
    }>;
}

export default function AppointmentDetailsPage(props: AppointmentDetailsPageProps) {
    const params = use(props.params);
    const router = useRouter()
    const [appointment, setAppointment] = useState<Appointment | null>(null)
    const [loading, setLoading] = useState(true)
    const [saving, setSaving] = useState(false)
    const [error, setError] = useState<string | null>(null)
    const [isEditing, setIsEditing] = useState(false)
    const [editData, setEditData] = useState({
        service: '',
        date: '',
        time: '',
        value: '',
        status: '',
        notes: '',
    })

    // Fetch appointment data
    useEffect(() => {
        fetchAppointment()
    }, [params.id])

    const fetchAppointment = async () => {
        setLoading(true)
        setError(null)
        try {
            const response: SingleAppointmentResponse = await apiClient.get(`/api/v1/appointment/${params.id}`)
            setAppointment(response.data)

            // Initialize edit form with current data
            const startDate = new Date(response.data.startTime)

            setEditData({
                service: response.data.service,
                date: startDate.toISOString().split('T')[0],
                time: startDate.toTimeString().slice(0, 5),
                value: response.data.value.toString(),
                status: response.data.status,
                notes: response.data.notes.join(', '),
            })
        } catch (err) {
            setError(err instanceof Error ? err.message : 'Failed to fetch appointment')
        } finally {
            setLoading(false)
        }
    }

    const handleSave = async () => {
        setSaving(true)
        setError(null)

        try {
            // Combine date and time into start_time and calculate end_time
            const startDateTime = new Date(`${editData.date}T${editData.time}`)
            const endDateTime = new Date(startDateTime.getTime() + 60 * 60 * 1000) // Add 1 hour

            const updateData = {
                service: editData.service,
                start_time: startDateTime.toISOString(),
                end_time: endDateTime.toISOString(),
                value: parseFloat(editData.value) || 0,
                status: editData.status,
                notes: editData.notes ? [editData.notes] : [],
            }

            await apiClient.patch(`/api/v1/appointment/${params.id}`, updateData)

            // Refresh appointment data
            await fetchAppointment()
            setIsEditing(false)
        } catch (err) {
            setError(err instanceof Error ? err.message : 'Failed to update appointment')
        } finally {
            setSaving(false)
        }
    }

    const handleCancel = () => {
        if (appointment) {
            const startDate = new Date(appointment.startTime)

            setEditData({
                service: appointment.service,
                date: startDate.toISOString().split('T')[0],
                time: startDate.toTimeString().slice(0, 5),
                value: appointment.value.toString(),
                status: appointment.status,
                notes: appointment.notes.join(', '),
            })
        }
        setIsEditing(false)
        setError(null)
    }

    if (loading) {
        return (
            <div className="flex-1 space-y-6 p-6">
                <div className="flex items-center justify-center py-12">
                    <Loader2 className="h-8 w-8 animate-spin" />
                    <span className="ml-2 text-lg">Loading appointment...</span>
                </div>
            </div>
        )
    }

    if (error || !appointment) {
        return (
            <div className="flex-1 space-y-6 p-6">
                <div className="text-center py-12">
                    <p className="text-red-600 mb-4">
                        {error || 'Appointment not found'}
                    </p>
                    <Button onClick={() => router.back()}>
                        Back to Schedule
                    </Button>
                </div>
            </div>
        )
    }

    return (
        <div className="flex-1 space-y-6 p-6">
            {/* Header */}
            <div className="flex items-center justify-between">
                <div className="flex items-center gap-4">
                    <Button variant="ghost" size="sm" onClick={() => router.back()}>
                        <ArrowLeft className="mr-2 h-4 w-4" />
                        Back to Schedule
                    </Button>
                    <div>
                        <h1 className="text-3xl font-bold tracking-tight">Appointment Details</h1>
                        <p className="text-muted-foreground">
                            {appointment.service} with {appointment.lead?.person?.firstName} {appointment.lead?.person?.lastName}
                        </p>
                    </div>
                </div>
                <div className="flex items-center gap-2">
                    {isEditing ? (
                        <>
                            <Button onClick={handleSave} size="sm" disabled={saving}>
                                {saving ? (
                                    <>
                                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                        Saving...
                                    </>
                                ) : (
                                    <>
                                        <Save className="mr-2 h-4 w-4" />
                                        Save Changes
                                    </>
                                )}
                            </Button>
                            <Button variant="outline" onClick={handleCancel} size="sm" disabled={saving}>
                                <X className="mr-2 h-4 w-4" />
                                Cancel
                            </Button>
                        </>
                    ) : (
                        <Button onClick={() => setIsEditing(true)} size="sm">
                            <Edit3 className="mr-2 h-4 w-4" />
                            Edit Appointment
                        </Button>
                    )}
                </div>
            </div>

            <div className="grid gap-6 lg:grid-cols-3">
                {/* Main Content */}
                <div className="lg:col-span-2 space-y-6">
                    {/* Appointment Details */}
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <Calendar className="h-5 w-5" />
                                Appointment Information
                            </CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div className="grid gap-4 md:grid-cols-2">
                                <div className="space-y-2">
                                    <Label>Service</Label>
                                    {isEditing ? (
                                        <Select
                                            value={editData.service}
                                            onValueChange={(value) => setEditData({ ...editData, service: value })}
                                        >
                                            <SelectTrigger>
                                                <SelectValue placeholder="Select service type" />
                                            </SelectTrigger>
                                            <SelectContent>
                                                {serviceTypes.map((service) => (
                                                    <SelectItem key={service} value={service}>
                                                        {service}
                                                    </SelectItem>
                                                ))}
                                            </SelectContent>
                                        </Select>
                                    ) : (
                                        <div className="text-sm">
                                            {appointment.service}
                                        </div>
                                    )}
                                </div>
                                <div className="space-y-2">
                                    <Label>Status</Label>
                                    {isEditing ? (
                                        <Select
                                            value={editData.status}
                                            onValueChange={(value) => setEditData({ ...editData, status: value })}
                                        >
                                            <SelectTrigger>
                                                <SelectValue placeholder="Select status" />
                                            </SelectTrigger>
                                            <SelectContent>
                                                <SelectItem value="pending">Pending</SelectItem>
                                                <SelectItem value="confirmed">Confirmed</SelectItem>
                                                <SelectItem value="completed">Completed</SelectItem>
                                                <SelectItem value="cancelled">Cancelled</SelectItem>
                                                <SelectItem value="rescheduled">Rescheduled</SelectItem>
                                            </SelectContent>
                                        </Select>
                                    ) : (
                                        <Badge variant={
                                            appointment.status === 'confirmed' ? 'default' :
                                            appointment.status === 'pending' ? 'secondary' :
                                            appointment.status === 'completed' ? 'default' :
                                            appointment.status === 'cancelled' ? 'destructive' :
                                            'secondary'
                                        }>
                                            {appointment.status}
                                        </Badge>
                                    )}
                                </div>
                            </div>

                            <div className="grid gap-4 md:grid-cols-2">
                                <div className="space-y-2">
                                    <Label>Date</Label>
                                    {isEditing ? (
                                        <Input
                                            type="date"
                                            value={editData.date}
                                            onChange={(e) => setEditData({ ...editData, date: e.target.value })}
                                        />
                                    ) : (
                                        <div className="flex items-center gap-2 text-sm">
                                            <Calendar className="h-4 w-4 text-muted-foreground" />
                                            {new Date(appointment.startTime).toLocaleDateString()}
                                        </div>
                                    )}
                                </div>
                                <div className="space-y-2">
                                    <Label>Time</Label>
                                    {isEditing ? (
                                        <Input
                                            type="time"
                                            value={editData.time}
                                            onChange={(e) => setEditData({ ...editData, time: e.target.value })}
                                        />
                                    ) : (
                                        <div className="flex items-center gap-2 text-sm">
                                            <Clock className="h-4 w-4 text-muted-foreground" />
                                            {new Date(appointment.startTime).toLocaleTimeString('en-US', {
                                                hour: 'numeric',
                                                minute: '2-digit',
                                                hour12: true
                                            })}
                                        </div>
                                    )}
                                </div>
                            </div>

                            <div className="space-y-2">
                                <Label>Estimated Value</Label>
                                {isEditing ? (
                                    <Input
                                        type="number"
                                        step="0.01"
                                        value={editData.value}
                                        onChange={(e) => setEditData({ ...editData, value: e.target.value })}
                                        placeholder="Enter estimated value"
                                    />
                                ) : (
                                    <div className="flex items-center gap-2 text-sm">
                                        <DollarSign className="h-4 w-4 text-muted-foreground" />
                                        ${appointment.value.toLocaleString()}
                                    </div>
                                )}
                            </div>

                            <div className="space-y-2">
                                <Label>Notes</Label>
                                {isEditing ? (
                                    <Textarea
                                        value={editData.notes}
                                        onChange={(e) => setEditData({ ...editData, notes: e.target.value })}
                                        placeholder="Add appointment notes"
                                        className="min-h-[100px]"
                                    />
                                ) : (
                                    <div className="text-sm bg-muted/50 p-3 rounded-lg">
                                        {appointment.notes.length > 0 ? appointment.notes.join(', ') : 'No notes added'}
                                    </div>
                                )}
                            </div>

                            {error && (
                                <div className="text-sm text-red-600 bg-red-50 p-3 rounded-lg">
                                    {error}
                                </div>
                            )}
                        </CardContent>
                    </Card>

                    {/* AI Job Summary */}
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <Sparkles className="h-5 w-5" />
                                AI Job Summary
                            </CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div className="grid gap-4 md:grid-cols-2">
                                <div>
                                    <h4 className="font-medium mb-2">Job Type</h4>
                                    <p className="text-sm text-muted-foreground">{aiSummary.jobType}</p>
                                </div>
                                <div>
                                    <h4 className="font-medium mb-2">Lead Intent</h4>
                                    <p className="text-sm text-muted-foreground">{aiSummary.leadIntent}</p>
                                </div>
                            </div>

                            <div>
                                <h4 className="font-medium mb-2">Key Requirements</h4>
                                <ul className="text-sm text-muted-foreground space-y-1">
                                    {aiSummary.keyRequirements.map((req, index) => (
                                        <li key={index} className="flex items-start gap-2">
                                            <span className="text-primary-300">•</span>
                                            {req}
                                        </li>
                                    ))}
                                </ul>
                            </div>

                            <div>
                                <h4 className="font-medium mb-2">Recommended Approach</h4>
                                <p className="text-sm text-muted-foreground">{aiSummary.recommendedApproach}</p>
                            </div>

                            <div className="grid gap-4 md:grid-cols-2">
                                <div>
                                    <h4 className="font-medium mb-2">Risk Factors</h4>
                                    <ul className="text-sm text-muted-foreground space-y-1">
                                        {aiSummary.riskFactors.map((risk, index) => (
                                            <li key={index} className="flex items-start gap-2">
                                                <span className="text-destructive">•</span>
                                                {risk}
                                            </li>
                                        ))}
                                    </ul>
                                </div>
                                <div>
                                    <h4 className="font-medium mb-2">Opportunity Score</h4>
                                    <div className="flex items-center gap-2">
                                        <div className="flex-1 bg-muted rounded-full h-2">
                                            <div
                                                className="bg-primary-300 h-2 rounded-full"
                                                style={{ width: `${aiSummary.opportunityScore}%` }}
                                            />
                                        </div>
                                        <span className="text-sm font-medium">{aiSummary.opportunityScore}%</span>
                                    </div>
                                </div>
                            </div>
                        </CardContent>
                    </Card>

                    {/* Recent Conversations */}
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <MessageSquare className="h-5 w-5" />
                                Recent Conversations
                            </CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="space-y-4">
                                {mockConversations.map((conversation) => (
                                    <div key={conversation.id} className="flex items-start gap-3 p-3 rounded-lg border">
                                        <div className="flex-shrink-0">
                                            {conversation.type === "call" ? (
                                                <div className="w-8 h-8 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center">
                                                    <Phone className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                                                </div>
                                            ) : (
                                                <div className="w-8 h-8 rounded-full bg-green-100 dark:bg-green-900 flex items-center justify-center">
                                                    <MessageSquare className="h-4 w-4 text-green-600 dark:text-green-400" />
                                                </div>
                                            )}
                                        </div>
                                        <div className="flex-1 min-w-0">
                                            <div className="flex items-center gap-2 mb-1">
                                                <span className="text-sm font-medium capitalize">{conversation.type}</span>
                                                {conversation.type === "call" && (
                                                    <>
                                                        <Badge variant="outline" className="text-xs">
                                                            {conversation.direction}
                                                        </Badge>
                                                        <span className="text-xs text-muted-foreground">{conversation.duration}</span>
                                                    </>
                                                )}
                                                {conversation.type === "sms" && (
                                                    <Badge variant="outline" className="text-xs">
                                                        {conversation.messageCount} messages
                                                    </Badge>
                                                )}
                                            </div>
                                            <p className="text-sm text-muted-foreground">
                                                {conversation.summary || conversation.lastMessage}
                                            </p>
                                            <p className="text-xs text-muted-foreground mt-1">
                                                {new Date(conversation.timestamp).toLocaleString()}
                                            </p>
                                        </div>
                                    </div>
                                ))}
                            </div>
                        </CardContent>
                    </Card>
                </div>

                {/* Sidebar */}
                <div className="space-y-6">
                    {/* Lead Information */}
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <User className="h-5 w-5" />
                                Lead Information
                            </CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div className="flex items-center gap-3">
                                <Avatar>
                                    <AvatarFallback>
                                        <AvatarInitials name={`${appointment.lead?.person?.firstName} ${appointment.lead?.person?.lastName}`} />
                                    </AvatarFallback>
                                </Avatar>
                                <div>
                                    <h3 className="font-semibold">
                                        {appointment.lead?.person?.firstName} {appointment.lead?.person?.lastName}
                                    </h3>
                                    <p className="text-sm text-muted-foreground">Lead ID: {appointment.lead?.id}</p>
                                </div>
                            </div>

                            <Separator />

                            <div className="space-y-3">
                                {appointment.lead?.person?.email && (
                                    <div className="flex items-center gap-2 text-sm">
                                        <MessageSquare className="h-4 w-4 text-muted-foreground" />
                                        <Link href={`mailto:${appointment.lead.person.email}`} className="hover:underline">
                                            {appointment.lead.person.email}
                                        </Link>
                                    </div>
                                )}
                            </div>

                            <Button variant="outline" size="sm" className="w-full bg-transparent" asChild>
                                <Link href={`/leads/${appointment.lead?.id}`}>View Full Lead Profile</Link>
                            </Button>
                        </CardContent>
                    </Card>

                    {/* Agent Information */}
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <Bot className="h-5 w-5" />
                                Agent Information
                            </CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div className="flex items-center gap-3">
                                <Avatar>
                                    <AvatarFallback>
                                        <AvatarInitials name={appointment.agent?.name || 'Agent'} />
                                    </AvatarFallback>
                                </Avatar>
                                <div>
                                    <h3 className="font-semibold">{appointment.agent?.name}</h3>
                                    <p className="text-sm text-muted-foreground">Agent ID: {appointment.agent?.id}</p>
                                </div>
                            </div>

                            <Separator />

                            <div className="space-y-3">
                                <div className="text-sm">
                                    <span className="text-muted-foreground">Service:</span>
                                    <span className="ml-2 font-medium">{appointment.service}</span>
                                </div>
                            </div>

                            <Button variant="outline" size="sm" className="w-full bg-transparent" asChild>
                                <Link href={`/agents/${appointment.agent?.id}`}>View Agent Details</Link>
                            </Button>
                        </CardContent>
                    </Card>

                    {/* Quick Actions */}
                    <Card>
                        <CardHeader>
                            <CardTitle>Quick Actions</CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-2">
                            <Button variant="outline" size="sm" className="w-full justify-start bg-transparent">
                                <Phone className="mr-2 h-4 w-4" />
                                Call Lead
                            </Button>
                            <Button variant="outline" size="sm" className="w-full justify-start bg-transparent">
                                <MessageSquare className="mr-2 h-4 w-4" />
                                Send Message
                            </Button>
                            <Button variant="outline" size="sm" className="w-full justify-start bg-transparent">
                                <Calendar className="mr-2 h-4 w-4" />
                                Reschedule
                            </Button>
                            <Button variant="destructive" size="sm" className="w-full justify-start">
                                <X className="mr-2 h-4 w-4" />
                                Cancel Appointment
                            </Button>
                        </CardContent>
                    </Card>
                </div>
            </div>
        </div>
    )
}
