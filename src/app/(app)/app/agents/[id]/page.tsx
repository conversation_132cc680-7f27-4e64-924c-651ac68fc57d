"use client"

import {<PERSON>, CardContent, CardDescription, CardHeader, CardTitle} from "@app/components/ui/card"
import {Badge} from "@app/components/ui/badge"
import {Button} from "@app/components/ui/button"
import {Avatar, AvatarFallback} from "@app/components/ui/avatar"
import {
    Bot,
    Phone,
    MessageSquare,
    DollarSign,
    Calendar,
    Settings,
    ArrowLeft,
    Star,
    PhoneCall,
    Mail,
    Clock,
    Forward,
    MessageCircle,
    CheckCircle,
    XCircle,
} from "lucide-react"
import Link from "next/link"
import {useState, useEffect, use} from "react"
import {PhoneNumber} from "@app/components/ui/phoneNumber";
import Agent from "@app/types/agent";
import ConversationList from "@/app/(app)/components/conversation/conversation-list";
import LeadList from "@app/components/lead/lead-list";
import {apiClient} from "@app/lib/api-client";

interface AgentDetailsPageProps {
    params: Promise<{
        id: string
    }>
}

interface AgentResponse {
    status: number,
    data: Agent;
}

interface recentConversation {
    id: string,
    lead: {
        id: string,
        person: {
            id: string,
            name: {
                full: string,
                first: string,
                last: string,
            }
        }
    }
    leadPhone: string,
    lastMessage: string,
    lastMessageTime: string,
    messageCount: number,
    status: string,
}

export default function AgentDetailsPage(props: AgentDetailsPageProps) {
    const params = use(props.params);
    const [loading, setLoading] = useState(true)
    const [error, setError] = useState<string | null>(null)
    const [agent, setAgent] = useState<Agent | null>(null)

    const [loadingConversations, setLoadingConversations] = useState(true);
    const [errorConversations, setErrorConversations] = useState<string | null>(null)
    const [recentConversations, setRecentConversations] = useState<recentConversation[]>([])

    const fetchAgent = async () => {
        setLoading(true)
        setError(null)
        try {
            const response: Agent = await apiClient.get(`/api/v1/agent/${params.id}`)

            const data = response;

            setAgent(data)
            console.log(data)
        } catch (err) {
            setError(err instanceof Error ? err.message : 'Failed to fetch agent')
        } finally {
            setLoading(false)
        }
    }

    useEffect(() => {
        fetchAgent();
    }, [params])

    // Mock data - in real app, fetch based on id

    // if (!agent) {
    //     notFound()
    // }

    // const breadcrumbs = [{ label: "Agents", href: "/agents" }, { label: agent.name }]

    const conversions = [
        {
            id: "1",
            leadName: "David Martinez",
            appointmentDate: "2024-03-15T14:00:00Z",
            value: 8500,
            conversionTime: "2.1 days",
        },
        {
            id: "2",
            leadName: "Amanda Taylor",
            appointmentDate: "2024-03-18T10:30:00Z",
            value: 12000,
            conversionTime: "1.8 days",
        },
    ]

    const getStatusColor = (status: string) => {
        switch (status) {
            case "active":
                return "success"
            case "pending":
                return "warning"
            case "scheduled":
                return "warning"
            case "new":
                return "default"
            case "qualified":
                return "default"
            case "inactive":
                return "error"
            default:
                return "secondary"
        }
    }

    return (
        <>
            {loading || !agent ? (
                <div>Loading...</div>
            ) : error ? (
                <div>Error: {error}</div>
            ) : agent ? (
                <div className="space-y-6">
                    {/* Header */}
                    <div className="flex items-center justify-between">
                        <div className="flex items-start space-x-4 flex-col">
                            <Button variant="ghost" size="sm" asChild className="grow-0 flex justify-start">
                                <Link href="/agents">
                                    <ArrowLeft className="h-4 w-4 mr-2"/>
                                    Back to Agents
                                </Link>
                            </Button>
                            <div className="flex items-center space-x-3">
                                <Avatar className="h-12 w-12">
                                    <AvatarFallback className="bg-primary/10">
                                        <Bot className="h-6 w-6 text-primary"/>
                                    </AvatarFallback>
                                </Avatar>
                                <div>
                                    <h1 className="text-3xl font-bold tracking-tight">{agent.name}</h1>
                                    <div className="flex items-center space-x-2 text-muted-foreground">
                                        <Phone className="h-4 w-4"/>
                                        <span><PhoneNumber phone={agent.phone}/></span>
                                        <Badge variant={getStatusColor(agent.status)}>{agent.status}</Badge>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <Button asChild>
                            <Link href={`/agents/${agent.id}/configure`}>
                                <Settings className="mr-2 h-4 w-4"/>
                                Configure Agent
                            </Link>
                        </Button>
                    </div>

                    {/* Key Metrics */}
                    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                        <Card>
                            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                <CardTitle className="text-sm font-medium text-muted-foreground">Calls Handled</CardTitle>
                                <Phone className="h-4 w-4 text-muted-foreground"/>
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold">{agent.metrics.callsHandled}</div>
                                <p className="text-xs text-muted-foreground">{agent.metrics.voicemailCount} voicemails</p>
                            </CardContent>
                        </Card>

                        <Card>
                            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                <CardTitle className="text-sm font-medium text-muted-foreground">Conversations</CardTitle>
                                <MessageSquare className="h-4 w-4 text-muted-foreground"/>
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold">{agent.metrics.conversationsHandled}</div>
                                <p className="text-xs text-muted-foreground">{agent.metrics.conversionRate}% conversion rate</p>
                            </CardContent>
                        </Card>

                        <Card>
                            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                <CardTitle className="text-sm font-medium text-muted-foreground">Lead Sentiment</CardTitle>
                                <Star className="h-4 w-4 text-muted-foreground"/>
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold">{agent.metrics.avgLeadSentiment}/100</div>
                                <p className="text-xs text-muted-foreground">Average sentiment score</p>
                            </CardContent>
                        </Card>

                        <Card>
                            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                <CardTitle className="text-sm font-medium text-muted-foreground">Monthly Cost</CardTitle>
                                <DollarSign className="h-4 w-4 text-muted-foreground"/>
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold">${agent.metrics.totalMonthlyCost}</div>
                                <p className="text-xs text-muted-foreground">Current billing period</p>
                            </CardContent>
                        </Card>
                    </div>

                    {/* Additional Metrics */}
                    <div className="grid gap-4 md:grid-cols-3">
                        <Card>
                            <CardHeader>
                                <CardTitle className="text-lg">Response Performance</CardTitle>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div className="flex justify-between">
                                    <span className="text-sm text-muted-foreground">Avg Response Time</span>
                                    <span className="font-medium">{agent.metrics.avgLeadResponseTime} seconds</span>
                                </div>
                                <div className="flex justify-between">
                                    <span className="text-sm text-muted-foreground">Messages Sent</span>
                                    <span className="font-medium">{agent.metrics.messageCount}</span>
                                </div>
                                <div className="flex justify-between">
                                    <span className="text-sm text-muted-foreground">Avg Messages/Conversation</span>
                                    <span className="font-medium">{agent.metrics.avgMessagePerConversation}</span>
                                </div>
                                <div className="flex justify-between">
                                    <span className="text-sm text-muted-foreground">SMS Reputation</span>
                                    <span className="font-medium">{agent.metrics.smsReputation}%</span>
                                </div>
                            </CardContent>
                        </Card>

                        <Card>
                            <CardHeader>
                                <CardTitle className="text-lg">Conversion Metrics</CardTitle>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div className="flex justify-between">
                                    <span className="text-sm text-muted-foreground">Avg Conversion Time</span>
                                    <span className="font-medium">{agent.metrics.avgConversionTime} seconds</span>
                                </div>
                                <div className="flex justify-between">
                                    <span className="text-sm text-muted-foreground">Avg Conversion Cost</span>
                                    <span className="font-medium">${agent.metrics.avgCostPerConversion}</span>
                                </div>
                                <div className="flex justify-between">
                                    <span className="text-sm text-muted-foreground">Last Activity</span>
                                    <span className="font-medium">{agent.metrics.lastActivity.formatted}</span>
                                </div>
                            </CardContent>
                        </Card>

                        <Card>
                            <CardHeader>
                                <CardTitle className="text-lg">Agent Info</CardTitle>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div className="flex justify-between">
                                    <span className="text-sm text-muted-foreground">Created</span>
                                    <span className="font-medium" title={agent.metadata.createdAt.raw.date}>{agent.metadata.createdAt.formatted}</span>
                                </div>
                                <div className="flex justify-between">
                                    <span className="text-sm text-muted-foreground">Created By</span>
                                    <span className="font-medium">{agent.metadata.createdBy}</span>
                                </div>
                                <div className="flex justify-between">
                                    <span className="text-sm text-muted-foreground">Last Updated</span>
                                    <span className="font-medium">{agent.metadata.updatedAt.formatted}</span>
                                </div>
                                <div className="flex justify-between">
                                    <span className="text-sm text-muted-foreground">Updated By</span>
                                    <span className="font-medium">{agent.metadata.updatedBy}</span>
                                </div>
                            </CardContent>
                        </Card>
                    </div>

                    {/* Configuration Details */}
                    <div className="grid gap-6 md:grid-cols-2">
                        {/* Call Forwarding Configuration */}
                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center space-x-2">
                                    <Forward className="h-5 w-5"/>
                                    <span>Call Forwarding</span>
                                </CardTitle>
                                <CardDescription>Current forwarding configuration</CardDescription>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                {agent.forwardSequence && agent.forwardSequence.length > 0 ? (
                                    <div className="space-y-3">
                                        {agent.forwardSequence
                                            .sort((a, b) => a.waitTime - b.waitTime)
                                            .map((forward, index) => (
                                                <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                                                    <div className="flex items-center space-x-3">
                                                        <div className="flex items-center justify-center w-6 h-6 bg-primary/10 text-primary rounded-full text-xs font-medium">
                                                            {index + 1}
                                                        </div>
                                                        <div>
                                                            <p className="font-medium">
                                                                <PhoneNumber phone={forward.number}/>
                                                            </p>
                                                            <p className="text-xs text-muted-foreground">
                                                                {forward.waitTime === 0 ? 'Primary number' : `Wait ${forward.waitTime} seconds`}
                                                            </p>
                                                        </div>
                                                    </div>
                                                    <div className="flex items-center space-x-1 text-xs text-muted-foreground">
                                                        <Clock className="h-3 w-3"/>
                                                        <span>{forward.waitTime}s</span>
                                                    </div>
                                                </div>
                                            ))}
                                    </div>
                                ) : agent.forwardNumber ? (
                                    <div className="flex items-center justify-between p-3 border rounded-lg">
                                        <div className="flex items-center space-x-3">
                                            <div className="flex items-center justify-center w-6 h-6 bg-primary/10 text-primary rounded-full text-xs font-medium">
                                                1
                                            </div>
                                            <div>
                                                <p className="font-medium">
                                                    <PhoneNumber phone={agent.forwardNumber}/>
                                                </p>
                                                <p className="text-xs text-muted-foreground">Primary number</p>
                                            </div>
                                        </div>
                                        <div className="flex items-center space-x-1 text-xs text-muted-foreground">
                                            <Clock className="h-3 w-3"/>
                                            <span>0s</span>
                                        </div>
                                    </div>
                                ) : (
                                    <div className="text-center py-6 text-muted-foreground">
                                        <Forward className="h-8 w-8 mx-auto mb-2 opacity-50"/>
                                        <p className="text-sm">No forwarding configured</p>
                                    </div>
                                )}
                            </CardContent>
                        </Card>

                        {/* Auto Responder Configuration */}
                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center space-x-2">
                                    <MessageCircle className="h-5 w-5"/>
                                    <span>Auto Responder</span>
                                </CardTitle>
                                <CardDescription>Automated response configuration</CardDescription>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                {agent.autoResponder ? (
                                    <div className="space-y-4">
                                        <div className="flex items-center gap-4">
                                            <span className="text-sm font-medium">Status</span>
                                            <div className="flex items-center space-x-2">
                                                {agent.autoResponder.enabled ? (
                                                    <Badge variant="default" className="bg-green-100 text-green-800">Enabled</Badge>
                                                ) : (
                                                    <Badge variant="secondary" className="bg-red-100 text-red-800">Disabled</Badge>
                                                )}
                                            </div>
                                        </div>

                                        {agent.autoResponder.enabled && (
                                            <>
                                                <div className="space-y-2">
                                                    <span className="text-sm font-medium">Response Delay</span>
                                                    <div className="flex items-center space-x-2">
                                                        <Clock className="h-4 w-4 text-muted-foreground"/>
                                                        <span className="text-sm text-muted-foreground">
                                                            {agent.autoResponder.responseDelay === '0' ? 'Immediate' :
                                                             agent.autoResponder.responseDelay === '1-30' ? '1-30 seconds' :
                                                             agent.autoResponder.responseDelay === '30-60' ? '30-60 seconds' :
                                                             agent.autoResponder.responseDelay === '60-300' ? '1-5 minutes' :
                                                             agent.autoResponder.responseDelay === '300-600' ? '5-10 minutes' :
                                                             agent.autoResponder.responseDelay === '600-1800' ? '10-30 minutes' :
                                                             agent.autoResponder.responseDelay === '1800-3600' ? '30 min - 1 hour' :
                                                             agent.autoResponder.responseDelay === 'random' ? 'Random (1-15 min)' :
                                                             agent.autoResponder.responseDelay}
                                                        </span>
                                                    </div>
                                                </div>

                                                {agent.autoResponder.toneSample && (
                                                    <div className="space-y-2">
                                                        <span className="text-sm font-medium">Tone Sample</span>
                                                        <div className="p-3 bg-muted rounded-lg">
                                                            <p className="text-sm text-muted-foreground">
                                                                {agent.autoResponder.toneSample}
                                                            </p>
                                                        </div>
                                                    </div>
                                                )}
                                            </>
                                        )}
                                    </div>
                                ) : (
                                    <div className="text-center py-6 text-muted-foreground">
                                        <MessageCircle className="h-8 w-8 mx-auto mb-2 opacity-50"/>
                                        <p className="text-sm">No auto responder configured</p>
                                    </div>
                                )}
                            </CardContent>
                        </Card>
                    </div>

                    {/* Recent Conversations */}
                    <ConversationList agent={agent.id} title="Recent Conversations" description="Latest interactions with this agent" limit={5} sort="lastMessageTime"/>

                    {/* Recent Leads and Conversions */}
                    <div className="grid gap-6 md:grid-cols-2">
                        <LeadList agent={agent.id} title="Recent Leads" description="New leads from missed calls and auto-responses" limit={5} sort="createdAt"/>

                        <Card>
                            <CardHeader>
                                <div className="flex items-center justify-between">
                                    <div>
                                        <CardTitle className="text-lg">Recent Conversions</CardTitle>
                                        <CardDescription>Leads that scheduled appointments</CardDescription>
                                    </div>
                                    <Button asChild>
                                        <Link href={`/agents/${agent.id}/conversions`}>
                                            View All
                                        </Link>
                                    </Button>
                                </div>
                            </CardHeader>
                            <CardContent>
                                <div className="space-y-4">
                                    {conversions.map((conversion) => (
                                        <div key={conversion.id} className="flex items-center justify-between p-3 border rounded-lg">
                                            <div>
                                                <p className="font-medium">{conversion.leadName}</p>
                                                <div className="flex items-center space-x-1 text-sm text-muted-foreground">
                                                    <Calendar className="h-3 w-3"/>
                                                    <span>{new Date(conversion.appointmentDate).toLocaleDateString()}</span>
                                                </div>
                                                <p className="text-xs text-muted-foreground mt-1">Converted in {conversion.conversionTime}</p>
                                            </div>
                                            <div className="text-right">
                                                <p className="font-medium text-green-600">${conversion.value.toLocaleString()}</p>
                                                <Badge variant="outline" className="text-xs">
                                                    Scheduled
                                                </Badge>
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            </CardContent>
                        </Card>
                    </div>
                </div>
            ) : (
                <div>Failed to load agent...</div>
            )}
        </>
    )
}
