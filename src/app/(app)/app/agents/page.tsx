"use client"

import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@app/components/ui/card"
import { Badge } from "@app/components/ui/badge"
import { Button } from "@app/components/ui/button"
import { Avatar, AvatarFallback } from "@app/components/ui/avatar"
import { Bot, Plus, Phone as PhoneIcon, MessageSquare, Settings, MoreHorizontal, Power, Edit, Trash2, Activity } from "lucide-react"
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuLabel,
    DropdownMenuSeparator,
    DropdownMenuTrigger,
} from "@app/components/ui/dropdown-menu"
import Link from "next/link"
import { useState, useEffect } from "react"
import {Skeleton} from "@app/components/ui/skeleton";
import {formatPhoneNumber, PhoneNumber} from "@app/components/ui/phoneNumber";
import Agent from "@app/types/agent";
import {apiClient} from "@app/lib/api-client";

interface AgentResponse {
    status: number,
    data: Agent[],
    pagination: {
        limit: number
        page: number
        count: number
    }
}

export default function AgentsPage() {
    const breadcrumbs = [{ label: "Agents" }]
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);
    const [agents, setAgents] = useState<Agent[]>([]);
    const [currentPage, setCurrentPage] = useState(1);
    const [totalPages, setTotalPages] = useState(1);
    const limit = 12;

    const fetchAgents = async (page: number) => {
        setLoading(true)
        setError(null)
        try {
            const response: AgentResponse = await apiClient.get(`/api/v1/agent?paginate[page]=${page}&paginate[limit]=${limit}&fetchCount=1`)
            setAgents(response.data);
            setCurrentPage(response.pagination.page)
            setTotalPages(Math.ceil(response.pagination.count / limit))
        } catch (err) {
            setError(err instanceof Error ? err.message : 'Failed to fetch agents')
        }finally {
            setLoading(false)
        }
    }

    useEffect(() => {
        fetchAgents(currentPage)
    }, [currentPage])

    const handlePageChange = (page: number) => {
        if (page >= 1 && page <= totalPages) {
            setCurrentPage(page)
        }
    }
    const getStatusColor = (status: string) => {
        return status === "active" ? "default" : "secondary"
    }

    return (
        <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
            <div>
                <h1 className="text-3xl font-bold tracking-tight">Agents</h1>
                <p className="text-muted-foreground">Manage your auto-responder agents and their configurations</p>
            </div>
            <Button asChild>
                <Link href="/agents/create">
                    <Plus className="mr-2 h-4 w-4"/>
                    Create Agent
                </Link>
            </Button>
        </div>

        <div className="grid gap-4 md:grid-cols-4">
            <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium text-muted-foreground">Total Agents</CardTitle>
                    <Bot className="h-4 w-4 text-muted-foreground"/>
                </CardHeader>
                {loading ? (
                    <CardContent className="p-6 text-center">
                        <p>Loading...</p>
                    </CardContent>
                ) : error ? (
                    <CardContent className="p-6 text-center">
                        <p className="text-red-500">Error: {error}</p>
                    </CardContent>
                ) : (
                    <CardContent>
                        <div className="text-2xl font-bold">{agents.length}</div>
                        <p className="text-xs text-muted-foreground">
                            {agents.filter((a) => a.status === "active").length} active
                        </p>
                    </CardContent>
                    )}
            </Card>

            <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium text-muted-foreground">Calls Handled</CardTitle>
                    <PhoneIcon className="h-4 w-4 text-muted-foreground"/>
                </CardHeader>
                {loading ? (
                    <CardContent className="p-6 text-center">
                        <p>Loading...</p>
                    </CardContent>
                ) : error ? (
                    <CardContent className="p-6 text-center">
                        <p className="text-red-500">Error: {error}</p>
                    </CardContent>
                ) : (
                    <CardContent>
                        <div className="text-2xl font-bold">{agents.reduce((sum, agent) => sum + agent.metrics.callsHandled, 0)}</div>
                        <p className="text-xs text-muted-foreground">This month</p>
                    </CardContent>
                )}
            </Card>

            <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium text-muted-foreground">Avg Response Rate</CardTitle>
                    <MessageSquare className="h-4 w-4 text-muted-foreground"/>
                </CardHeader>
                {loading ? (
                    <CardContent className="p-6 text-center">
                        <p>Loading...</p>
                    </CardContent>
                ) : error ? (
                    <CardContent className="p-6 text-center">
                        <p className="text-red-500">Error: {error}</p>
                    </CardContent>
                ) : (
                    <CardContent>
                        <div className="text-2xl font-bold">
                            {Math.round(agents.reduce((sum, agent) => sum + agent.metrics.leadResponseRate, 0) / agents.filter((a)=>a.metrics.callsHandled>0).length)}%
                        </div>
                        <p className="text-xs text-muted-foreground">Across all agents</p>
                    </CardContent>
                )}
            </Card>

            <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium text-muted-foreground">Active Now</CardTitle>
                    <Activity className="h-4 w-4 text-muted-foreground"/>
                </CardHeader>
                {loading ? (
                    <CardContent className="p-6 text-center">
                        <p>Loading...</p>
                    </CardContent>
                ) : error ? (
                    <CardContent className="p-6 text-center">
                        <p className="text-red-500">Error: {error}</p>
                    </CardContent>
                ) : (
                    <CardContent>
                        <div className="text-2xl font-bold">{agents.filter((a) => a.status === "active").length}</div>
                        <p className="text-xs text-muted-foreground">Monitoring calls</p>
                    </CardContent>
                )}
            </Card>
        </div>

        {/* Agents Grid */}
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
            {loading ? (
                <>
                {Array.from({ length: 3 }).map((_, i) => (
                <Card key={i} className="relative">
                    <CardHeader className="pb-3">
                        <div className="flex items-start justify-between">
                            <div className="flex items-center space-x-3">
                                <Skeleton className="h-10 w-10 rounded-full" />
                                <div className="space-y-2">
                                    <Skeleton className="h-4 w-32" />
                                    <Skeleton className="h-3 w-24" />
                                </div>
                            </div>
                        </div>

                    </CardHeader>
                    <CardContent className="space-y-4">
                        <Skeleton className="h-20 w-full" />
                    </CardContent>
                </Card>
                ))}
                </>
            ) : error ? (
                <div className="col-span-3">
                    <p className="text-red-500">Error: {error}</p>
                </div>
            ) : (
                <>
                {agents.map((agent) => (
                    <Card key={agent.id} className="relative">
                        <CardHeader className="pb-3">
                            <div className="flex items-start justify-between">
                                <div className="flex items-center space-x-3">
                                    <Link
                                        href={`/agents/${agent.id}`}
                                        className="flex items-center space-x-3 hover:opacity-80 transition-opacity"
                                    >
                                        <Avatar className="h-10 w-10">
                                            <AvatarFallback className="bg-primary/10">
                                                <Bot className="h-5 w-5 text-primary"/>
                                            </AvatarFallback>
                                        </Avatar>
                                        <div>
                                            <CardTitle className="text-lg hover:text-primary transition-colors">{agent.name}</CardTitle>
                                            <CardDescription className="flex items-center gap-1 hover:text-foreground transition-colors">
                                                <PhoneIcon className="h-3 w-3"/>
                                                <PhoneNumber phone={agent.phone} className="text-muted-foreground"></PhoneNumber>
                                            </CardDescription>
                                        </div>
                                    </Link>
                                </div>
                                <DropdownMenu>
                                    <DropdownMenuTrigger asChild>
                                        <Button variant="ghost" size="sm">
                                            <MoreHorizontal className="h-4 w-4"/>
                                        </Button>
                                    </DropdownMenuTrigger>
                                    <DropdownMenuContent align="end">
                                        <DropdownMenuLabel>Actions</DropdownMenuLabel>
                                        <DropdownMenuItem>
                                            <Edit className="mr-2 h-4 w-4"/>
                                            Edit Agent
                                        </DropdownMenuItem>
                                        <DropdownMenuItem asChild>
                                            <Link href={`/agents/${agent.id}/configure`}>
                                                <Settings className="mr-2 h-4 w-4"/>
                                                Configure
                                            </Link>
                                        </DropdownMenuItem>
                                        <DropdownMenuItem>
                                            <Power className="mr-2 h-4 w-4"/>
                                            {agent.status === "active" ? "Deactivate" : "Activate"}
                                        </DropdownMenuItem>
                                        <DropdownMenuSeparator/>
                                        <DropdownMenuItem className="text-destructive">
                                            <Trash2 className="mr-2 h-4 w-4"/>
                                            Delete
                                        </DropdownMenuItem>
                                    </DropdownMenuContent>
                                </DropdownMenu>
                            </div>
                            <Badge variant={getStatusColor(agent.status)} className="w-fit">
                                {agent.status}
                            </Badge>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <Link href={`/agents/${agent.id}`} className="block hover:opacity-80 transition-opacity">
                                <div>
                                    <p className="text-sm font-medium mb-1">Response Template</p>
                                    <p className="text-xs text-muted-foreground line-clamp-2">{agent.autoResponder?.toneSample || "Not configured"}</p>
                                </div>
                            </Link>
                            <div className="grid grid-cols-2 gap-4 text-sm">
                                <div>
                                    <p className="font-medium">{agent.metrics.callsHandled}</p>
                                    <p className="text-xs text-muted-foreground">Calls handled</p>
                                </div>
                                <div>
                                    <p className="font-medium">{agent.metrics.leadResponseRate}%</p>
                                    <p className="text-xs text-muted-foreground">Response rate</p>
                                </div>
                            </div>

                            <div className="pt-2 border-t">
                                <p className="text-xs text-muted-foreground">Last active: {agent.metrics.lastActivity.formatted}</p>
                            </div>
                        </CardContent>
                    </Card>
                ))}
                </>
            )}
        </div>

        {/* Empty State for New Users */}
        {!loading && agents.length === 0 && (
            <Card className="text-center py-12">
                <CardContent>
                    <Bot className="mx-auto h-12 w-12 text-muted-foreground mb-4"/>
                    <h3 className="text-lg font-semibold mb-2">No agents yet</h3>
                    <p className="text-muted-foreground mb-4">
                        Create your first auto-responder agent to start handling missed calls
                    </p>
                    <Button asChild>
                        <Link href="/agents/create">
                            <Plus className="mr-2 h-4 w-4"/>
                            Create Your First Agent
                        </Link>
                    </Button>
                </CardContent>
            </Card>
        )}
    </div>
    )
}
