"use client"

import {Card, CardContent, CardDescription, CardHeader, CardTitle} from "@app/components/ui/card"
import {Badge} from "@app/components/ui/badge"
import {But<PERSON>} from "@app/components/ui/button"
import {Avatar, AvatarFallback, AvatarInitials} from "@app/components/ui/avatar"
import {Input} from "@app/components/ui/input"
import Link from "next/link"
import {
  Users,
  Search,
  Phone,
  Mail,
  Calendar,
  MoreHorizontal,
  Download,
  Plus,
  TrendingUp,
  Target,
  CheckCircle,
} from "lucide-react"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@app/components/ui/dropdown-menu"
import {Select, SelectContent, SelectItem, SelectTrigger, SelectValue} from "@app/components/ui/select"
import {Table, TableBody, TableCell, TableHead, TableHeader, TableRow} from "@app/components/ui/table"
import Lead, {getPriorityColor, getScoreColor, getStatusColor, LeadResponse} from "@app/types/lead";
import {useEffect, useState} from "react";
import {apiClient} from "@app/lib/api-client";
import {PhoneNumber} from "@app/components/ui/phoneNumber";
import {AgentResponse} from "@app/types/agent";
import { useSearchParams, useRouter, usePathname } from "next/navigation";

interface LeadSource {
  id: string,
  name: string,
}

interface SourceResponse {
  status: number,
  data: LeadSource[],
}

export default function LeadsPage() {
  const breadcrumbs = [{label: "Leads"}]
  const searchParams = useSearchParams();
  const router = useRouter();
  const pathname = usePathname();

  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Initialize filters from URL parameters
  const [searchQuery, setSearchQuery] = useState(searchParams.get("search") || "");
  const [selectedStatus, setSelectedStatus] = useState(searchParams.get("status") || "");
  const [selectedSource, setSelectedSource] = useState(searchParams.get("agent") || "");
  const [leads, setLeads] = useState<Lead[]>([]);


  const [sourcesLoading, setSourcesLoading] = useState(true);
  const [sourcesError, setSourcesError] = useState<string | null>(null);
  const [sources, setSources] = useState<LeadSource[]>([]);

  // Function to update URL with current filter state
  const updateURL = (search: string, status: string, agent: string) => {
    const params = new URLSearchParams();

    if (search && search.trim()) {
      params.set("search", search);
    }
    if (status && status !== "all") {
      params.set("status", status);
    }
    if (agent && agent !== "all-sources") {
      params.set("agent", agent);
    }

    const queryString = params.toString();
    const newURL = queryString ? `${pathname}?${queryString}` : pathname;

    // Update URL without triggering a page reload
    router.replace(newURL, { scroll: false });
  };

  // Wrapper functions to update both state and URL
  const handleSearchChange = (value: string) => {
    setSearchQuery(value);
    updateURL(value, selectedStatus, selectedSource);
  };

  const handleStatusChange = (value: string) => {
    setSelectedStatus(value);
    updateURL(searchQuery, value, selectedSource);
  };

  const handleSourceChange = (value: string) => {
    setSelectedSource(value);
    updateURL(searchQuery, selectedStatus, value);
  };

  const fetchLeads = async () => {
    setLoading(true)
    setError(null)
    try {
      const endpoint = `/api/v1/lead`;
      const queryParams: string[] = [];
      if (searchQuery) {
        queryParams.push(`filter[search]=${encodeURIComponent(searchQuery)}`)
      }
      if (selectedStatus && selectedStatus !== "all") {
        queryParams.push(`filter[status]=${encodeURIComponent(selectedStatus)}`)
      }

      if (selectedSource && selectedSource !== "all-sources") {
        queryParams.push(`filter[agent]=${encodeURIComponent(selectedSource)}`)
      }

      const response: LeadResponse = await apiClient.get(endpoint+'?'+queryParams.join('&'))
      setLeads(response.data)
      console.log(response.data)
      setLoading(false)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch leads')
    } finally {
      setLoading(false)
    }
  }

  // Sync state with URL parameters when they change
  useEffect(() => {
    const search = searchParams.get("search") || "";
    const status = searchParams.get("status") || "";
    const agent = searchParams.get("agent") || "";

    setSearchQuery(search);
    setSelectedStatus(status);
    setSelectedSource(agent);
  }, [searchParams]);

  // Trigger fetchLeads when filters change
  useEffect(() => {
    fetchLeads();
  }, [searchQuery, selectedStatus, selectedSource])

  const fetchSources = async () => {
    setSourcesLoading(true)
    setSourcesError(null)
    try {
      const response: SourceResponse = await apiClient.get(`/api/v1/agent/names`)

      setSources(response.data)
      setSourcesLoading(false)
    }
    catch (err) {
      setSourcesError(err instanceof Error ? err.message : 'Failed to fetch sources')
    } finally {
      setSourcesLoading(false)
    }
  }

  useEffect(() => {
    fetchSources();
  }, [])

  const stats = [
    {
      title: "Total Leads",
      value: leads.length.toString(),
      description: "All time",
      icon: Users,
    },
    {
      title: "New Leads",
      value: leads.filter((l) => l.status === "new").length.toString(),
      description: "This week",
      icon: Plus,
    },
    {
      title: "Conversion Rate",
      value: `${Math.round((leads.filter((l) => l.status === "converted").length / leads.length) * 100)}%`,
      description: "This month",
      icon: TrendingUp,
    },
    {
      title: "Pipeline Value",
      value: "$19,150",
      description: "Active leads",
      icon: Target,
    },
  ]

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Leads</h1>
          <p className="text-muted-foreground">Manage leads generated from your auto-responder conversations</p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline">
            <Download className="mr-2 h-4 w-4"/>
            Export
          </Button>
          <Button>
            <Plus className="mr-2 h-4 w-4"/>
            Add Lead
          </Button>
        </div>
      </div>

      {/* Stats Overview */}
      <div className="grid gap-4 md:grid-cols-4">
        {stats.map((stat) => (
          <Card key={stat.title}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-muted-foreground">{stat.title}</CardTitle>
              <stat.icon className="h-4 w-4 text-muted-foreground"/>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stat.value}</div>
              <p className="text-xs text-muted-foreground">{stat.description}</p>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Filters and Search */}
      <div className="flex items-center gap-4">
        <div className="relative flex-1 max-w-sm">
          <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground"/>
          <Input placeholder="Search leads..." className="pl-9" value={searchQuery} onChange={(e) => handleSearchChange(e.target.value)}/>
        </div>
        <Select value={selectedStatus || "all"} onValueChange={handleStatusChange}>
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="Filter by status"/>
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Status</SelectItem>
            <SelectItem value="new">New</SelectItem>
            <SelectItem value="contacted">Contacted</SelectItem>
            <SelectItem value="qualified">Qualified</SelectItem>
            <SelectItem value="converted">Converted</SelectItem>
            <SelectItem value="lost">Lost</SelectItem>
          </SelectContent>
        </Select>
        {!sourcesLoading && !sourcesError && (
          <Select value={selectedSource || "all-sources"} onValueChange={handleSourceChange}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Filter by source"/>
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all-sources">All Sources</SelectItem>
              {sources.map((source) => (
                <SelectItem key={source.name} value={source.id}>
                  {source.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        )}
      </div>

      {/* Leads Table */}
      <Card>
        <CardHeader>
          <CardTitle>All Leads</CardTitle>
          <CardDescription>Complete list of leads from your agents</CardDescription>
        </CardHeader>
        <CardContent className="p-0">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Lead</TableHead>
                {/*<TableHead>Contact</TableHead>*/}
                {/*<TableHead>Service</TableHead>*/}
                <TableHead>Agent</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Priority</TableHead>
                <TableHead>Score</TableHead>
                <TableHead>Value</TableHead>
                <TableHead>Created</TableHead>
                <TableHead className="w-[50px]"></TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {leads.map((lead) => (
                <TableRow key={lead.id}>
                  <TableCell>
                    <div className="flex items-center space-x-3">
                      <Avatar className="h-8 w-8">
                        <AvatarFallback>
                          <AvatarInitials name={lead?.person?.name?.full || ' '}/>
                        </AvatarFallback>
                      </Avatar>
                      <div className="flex flex-col space-y-1">
                        <Link href={`/leads/${lead.id}`} className="font-medium hover:text-primary-300 hover:underline">
                          {lead?.person?.name?.full || <span className="text-muted-foreground italic">Unknown Name</span>}
                        </Link>
                        <div className="flex items-center gap-1 text-sm text-muted-foreground">
                          <Phone className="h-3 w-3"/>
                          <PhoneNumber phone={lead.phone}/>
                        </div>
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="space-y-1">
                      <div className="flex items-center gap-1 text-sm">
                        {lead?.agent?.name || "No agent"}
                      </div>
                      {/*<div className="flex items-center gap-1 text-sm text-muted-foreground">*/}
                      {/*  <Mail className="h-3 w-3"/>*/}
                      {/*  {lead.person?.email || "No email"}*/}
                      {/*</div>*/}
                    </div>
                  </TableCell>
                  {/*<TableCell>*/}
                  {/*  <p className="text-sm">{lead.service}</p>*/}
                  {/*</TableCell>*/}
                  <TableCell>
                    <Badge variant={getStatusColor(lead.status)} className="capitalize">
                      {lead.status}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <span className={`text-sm font-medium ${getPriorityColor(lead.priority)}`}>{lead.priority}</span>
                  </TableCell>
                  <TableCell>
                    <span className={`text-sm font-medium ${getScoreColor(lead.score)}`}>{lead.score}</span>
                  </TableCell>
                  <TableCell>
                    <p className="text-sm font-medium">{lead.estimatedValue}</p>
                  </TableCell>
                  {/*<TableCell>*/}
                  {/*  <p className="text-sm text-muted-foreground">{lead.source}</p>*/}
                  {/*</TableCell>*/}
                  <TableCell>
                    <p className="text-sm text-muted-foreground">{new Date(lead.metadata.createdAt).toLocaleDateString()}</p>
                  </TableCell>
                  <TableCell>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="sm">
                          <MoreHorizontal className="h-4 w-4"/>
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuLabel>Actions</DropdownMenuLabel>
                        <DropdownMenuItem>
                          <Phone className="mr-2 h-4 w-4"/>
                          Call Lead
                        </DropdownMenuItem>
                        <DropdownMenuItem>
                          <Mail className="mr-2 h-4 w-4"/>
                          Send Email
                        </DropdownMenuItem>
                        <DropdownMenuItem>
                          <Calendar className="mr-2 h-4 w-4"/>
                          Schedule Meeting
                        </DropdownMenuItem>
                        <DropdownMenuSeparator/>
                        <DropdownMenuItem>
                          <CheckCircle className="mr-2 h-4 w-4"/>
                          Mark as Converted
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* Empty State */}
      {leads.length === 0 && (
        <Card className="text-center py-12">
          <CardContent>
            <Users className="mx-auto h-12 w-12 text-muted-foreground mb-4"/>
            <h3 className="text-lg font-semibold mb-2">No leads yet</h3>
            <p className="text-muted-foreground">
              Leads will appear here when your agents start converting conversations into business opportunities
            </p>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
