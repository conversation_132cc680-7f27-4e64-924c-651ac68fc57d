import type React from "react"
import type { <PERSON>ada<PERSON> } from "next"
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono } from "next/font/google";
import { ThemeProvider } from "next-themes"
import "@/globals.css"
import { getCSSVariables } from "@/lib/colors"
import { Suspense } from "react"
import {AppLayout} from "@app/components/app-layout";
import { AuthGuard } from "@app/lib/auth-guard";
import { TokenHandlerProvider } from "@app/lib/token-handler";
import {AuthProvider} from "@app/lib/auth-context";

const geistSans = Geist({
    variable: "--font-geist-sans",
    subsets: ["latin"],
});

const geistMono = Geist_Mono({
    variable: "--font-geist-mono",
    subsets: ["latin"],
});

export const metadata: Metadata = {
    title: "Back-Talk",
    description: "Never lose another lead to voicemail again",
}

export default function RootLayout({
                                       children,
                                   }: Readonly<{
    children: React.ReactNode
}>) {
    return (
        <html lang="en" suppressHydrationWarning>
        <head>
            <link rel="icon" href="/favicon.svg" />
            <link
                rel="apple-touch-icon"
                href="/favicon.svg"
                type="image/svg+xml"
                sizes="any"
            />
        </head>
        <body
            className={`font-sans ${geistSans.variable} ${geistMono.variable} antialiased flex flex-col min-h-screen`}
            style={getCSSVariables()}
        >
        <Suspense fallback={null}>
            <ThemeProvider attribute="class" defaultTheme="system" enableSystem disableTransitionOnChange>
                <TokenHandlerProvider>
                    <AuthProvider>
                        <AppLayout>
                            {children}
                        </AppLayout>
                    </AuthProvider>
                </TokenHandlerProvider>
            </ThemeProvider>
        </Suspense>
        </body>
        </html>
    )
}
