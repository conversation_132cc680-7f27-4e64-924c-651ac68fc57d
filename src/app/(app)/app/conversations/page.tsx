"use client"

import {useState, useEffect, useR<PERSON>, use<PERSON><PERSON>back} from "react"
import {Card, CardContent, CardDescription, CardHeader, CardTitle} from "@app/components/ui/card"
import {Badge} from "@app/components/ui/badge"
import {Button} from "@app/components/ui/button"
import {Avatar, AvatarFallback, AvatarInitials} from "@app/components/ui/avatar"
import {Input} from "@app/components/ui/input"
import {
  MessageSquare,
  Search,
  Phone,
  Clock,
  Bot,
  User,
  MoreHorizontal,
  Reply,
  Send,
  ArrowLeft,
  Calendar,
  PhoneCall,
  Voicemail,
  Play,
  Loader2,
  AlertTriangle,
  Link as LinkIcon,
} from "lucide-react"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@app/components/ui/dropdown-menu"
import {Select, SelectContent, SelectItem, SelectTrigger, SelectValue} from "@app/components/ui/select"
import {Switch} from "@app/components/ui/switch"
import {Label} from "@app/components/ui/label"
import {Textarea} from "@app/components/ui/textarea"
import {Alert, AlertDescription} from "@app/components/ui/alert"
import {cn} from "@app/lib/utils"
import {useSearchParams, useRouter, usePathname} from "next/navigation"
import {apiClient} from "@app/lib/api-client"
import Conversation, {ConversationResponse} from "@app/types/conversation"
import {Message, MessageResponse} from "@app/types/message"
import {Call} from "@app/types/call"
import {HighlightText} from "@app/components/ui/highlight-text"
import Link from "next/link"

// Enhanced types for conversation items (messages, calls, voicemails)
interface ConversationItem {
  id: string;
  type: 'message' | 'call' | 'voicemail';
  createdAt: string;
  // Message fields
  content?: string;
  sender?: 'lead' | 'agent' | 'user';
  senderName?: string;
  // Call fields
  duration?: number;
  answered?: boolean;
  // Voicemail fields
  transcription?: string;
  audioUrl?: string;
}

interface ConversationStats {
  totalConversations: number;
  activeConversations: number;
  pendingConversations: number;
  avgResponseTime: string;
}

export default function ConversationsPage() {
  const searchParams = useSearchParams();
  const router = useRouter();
  const pathname = usePathname();
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const messagesContainerRef = useRef<HTMLDivElement>(null);

  // URL state management
  const [selectedConversation, setSelectedConversation] = useState<string | null>(
    searchParams.get("conversation") || null
  );
  const [searchQuery, setSearchQuery] = useState(searchParams.get("search") || "");
  const [selectedStatus, setSelectedStatus] = useState(searchParams.get("status") || "");

  // Message and conversation state
  const [newMessage, setNewMessage] = useState("");
  const [autoResponseEnabled, setAutoResponseEnabled] = useState(true);
  const [showAutoResponseWarning, setShowAutoResponseWarning] = useState(false);
  const [isUserScrolling, setIsUserScrolling] = useState(false);

  // Data state
  const [conversations, setConversations] = useState<Conversation[]>([]);
  const [conversationItems, setConversationItems] = useState<ConversationItem[]>([]);
  const [stats, setStats] = useState<ConversationStats>({
    totalConversations: 0,
    activeConversations: 0,
    pendingConversations: 0,
    avgResponseTime: "0m"
  });

  // Loading and error states
  const [conversationsLoading, setConversationsLoading] = useState(true);
  const [conversationsError, setConversationsError] = useState<string | null>(null);
  const [messagesLoading, setMessagesLoading] = useState(false);
  const [messagesError, setMessagesError] = useState<string | null>(null);
  const [sendingMessage, setSendingMessage] = useState(false);

  // Pagination state for infinite scroll
  const [hasMoreMessages, setHasMoreMessages] = useState(true);
  const [messagesPage, setMessagesPage] = useState(1);
  const [loadingMoreMessages, setLoadingMoreMessages] = useState(false);

  // URL management functions
  const updateURL = (conversation: string | null, search: string, status: string) => {
    const params = new URLSearchParams();

    if (conversation) {
      params.set("conversation", conversation);
    }
    if (search && search.trim()) {
      params.set("search", search);
    }
    if (status && status !== "all") {
      params.set("status", status);
    }

    const queryString = params.toString();
    const newURL = queryString ? `${pathname}?${queryString}` : pathname;

    router.replace(newURL, {scroll: false});
  };

  const handleConversationSelect = (conversationId: string | null) => {
    setSelectedConversation(conversationId);
    updateURL(conversationId, searchQuery, selectedStatus);
    if (conversationId) {
      // Set auto-response state based on conversation data
      const conversation = conversations.find(c => c.id === conversationId);
      if (conversation) {
        setAutoResponseEnabled(conversation.isAgentActive);
      }

      setMessagesPage(1);
      setHasMoreMessages(true);
      setConversationItems([]);
      fetchConversationItems(conversationId, 1);
    }
  };

  const handleSearchChange = (value: string) => {
    setSearchQuery(value);
    updateURL(selectedConversation, value, selectedStatus);
  };

  const handleStatusChange = (value: string) => {
    setSelectedStatus(value);
    updateURL(selectedConversation, searchQuery, value);
  };

  // API fetch functions
  const fetchConversations = async () => {
    setConversationsLoading(true);
    setConversationsError(null);
    try {
      const endpoint = `/api/v1/conversation`;
      const queryParams: string[] = [];

      if (searchQuery) {
        queryParams.push(`filter[search]=${encodeURIComponent(searchQuery)}`);
      }
      if (selectedStatus && selectedStatus !== "all") {
        queryParams.push(`filter[status]=${encodeURIComponent(selectedStatus)}`);
      }

      // Sort by most recent activity
      queryParams.push(`sort=-lastMessageTime`);

      const response: ConversationResponse = await apiClient.get(
        endpoint + (queryParams.length ? '?' + queryParams.join('&') : '')
      );

      setConversations(response.data);

      // Update stats
      setStats({
        totalConversations: response.data.length,
        activeConversations: response.data.filter(c => c.status === 'active').length,
        pendingConversations: response.data.filter(c => c.status === 'pending').length,
        avgResponseTime: "12m" // TODO: Calculate from API
      });

    } catch (err) {
      setConversationsError(err instanceof Error ? err.message : 'Failed to fetch conversations');
    } finally {
      setConversationsLoading(false);
    }
  };

  const fetchConversationItems = async (conversationId: string, page: number = 1) => {
    if (page === 1) {
      setMessagesLoading(true);
      setMessagesError(null);
    } else {
      setLoadingMoreMessages(true);
    }

    try {
      // Fetch messages
      const messagesResponse: MessageResponse = await apiClient.get(
        `/api/v1/message?filter[conversation]=${conversationId}&sort=-createdAt&paginate[page]=${page}&paginate[limit]=20`
      );

      // Fetch calls for this conversation
      const callsResponse = await apiClient.get(
        `/api/v1/call?filter[conversation]=${conversationId}&sort=-createdAt`
      );

      // Combine and sort all items chronologically
      const messages: ConversationItem[] = messagesResponse.data.map(msg => ({
        id: msg.id,
        type: 'message' as const,
        createdAt: msg.createdAt,
        content: msg.content,
        sender: msg.sender,
        senderName: getSenderName(msg.sender)
      }));

      const calls: ConversationItem[] = callsResponse.data.map(call => ({
        id: call.id,
        type: call.voicemail ? 'voicemail' as const : 'call' as const,
        createdAt: call.startTime,
        duration: call.duration,
        answered: call.answered,
        transcription: call.voicemail?.transcription,
        audioUrl: call.voicemail?.audioUrl
      }));

      const allItems = [...messages, ...calls].sort((a, b) =>
        new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()
      );

      if (page === 1) {
        setConversationItems(allItems);
      } else {
        setConversationItems(prev => [...allItems, ...prev]);
      }

      setHasMoreMessages(messagesResponse.data.length === 20);

    } catch (err) {
      setMessagesError(err instanceof Error ? err.message : 'Failed to fetch conversation items');
    } finally {
      setMessagesLoading(false);
      setLoadingMoreMessages(false);
    }
  };

  const getSenderName = (sender: string) => {
    const selectedConv = conversations.find(c => c.id === selectedConversation);
    if (!selectedConv) return sender;

    switch (sender) {
      case 'lead':
        return selectedConv.lead.person.name.full;
      case 'agent':
        return selectedConv.agent.name;
      case 'user':
        return 'You';
      default:
        return sender;
    }
  };

  const sendMessage = async () => {
    if (!newMessage.trim() || !selectedConversation || sendingMessage) return;

    setSendingMessage(true);
    try {
      const response = await apiClient.post('/api/v1/message', {
        conversation: selectedConversation,
        content: newMessage.trim(),
        sender: 'user'
      });

      // Add the new message to the conversation items
      const newItem: ConversationItem = {
        id: response.data.id,
        type: 'message',
        createdAt: response.data.createdAt,
        content: response.data.content,
        sender: 'user',
        senderName: 'You'
      };

      setConversationItems(prev => [...prev, newItem]);
      setNewMessage("");

      // Update conversation in the list with new message count and last message
      setConversations(prev => prev.map(conv => {
        if (conv.id === selectedConversation) {
          return {
            ...conv,
            messageCount: conv.messageCount + 1,
            lastMessage: response.data.content,
            lastMessageTime: response.data.createdAt
          };
        }
        return conv;
      }));

      // Disable auto-response when user sends manual message
      if (autoResponseEnabled) {
        setAutoResponseEnabled(false);
        await updateConversationAutoResponse(selectedConversation, false);
      }

      // Scroll to bottom
      setTimeout(() => scrollToBottom(), 100);

    } catch (err) {
      console.error('Failed to send message:', err);
    } finally {
      setSendingMessage(false);
    }
  };

  const updateConversationAutoResponse = async (conversationId: string, isAgentActive: boolean) => {
    try {
      await apiClient.patch(`/api/v1/conversation/${conversationId}`, {
        isAgentActive
      });
    } catch (err) {
      console.error('Failed to update auto-response:', err);
      throw err;
    }
  };

  const toggleAutoResponse = async (enabled: boolean) => {
    if (!selectedConversation) return;

    try {
      await updateConversationAutoResponse(selectedConversation, enabled);
      setAutoResponseEnabled(enabled);

      // Update conversation in the list to reflect the new auto-response state
      setConversations(prev => prev.map(conv => {
        if (conv.id === selectedConversation) {
          return {
            ...conv,
            isAgentActive: enabled
          };
        }
        return conv;
      }));
    } catch (err) {
      console.error('Failed to toggle auto-response:', err);
      // Revert the UI state if API call failed
      setAutoResponseEnabled(!enabled);
    }
  };

  // Scroll management
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({behavior: 'smooth'});
  };

  const handleScroll = useCallback(() => {
    if (!messagesContainerRef.current) return;

    const {scrollTop, scrollHeight, clientHeight} = messagesContainerRef.current;
    const isAtBottom = scrollHeight - scrollTop <= clientHeight + 50;

    setIsUserScrolling(!isAtBottom);

    // Load more messages when scrolling to top
    if (scrollTop === 0 && hasMoreMessages && !loadingMoreMessages && selectedConversation) {
      setMessagesPage(prev => prev + 1);
      fetchConversationItems(selectedConversation, messagesPage + 1);
    }
  }, [hasMoreMessages, loadingMoreMessages, selectedConversation, messagesPage]);

  // Message input handlers
  const handleMessageInputChange = (value: string) => {
    setNewMessage(value);
    if (value.trim() && autoResponseEnabled) {
      setShowAutoResponseWarning(true);
    } else {
      setShowAutoResponseWarning(false);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      sendMessage();
    }
  };

  // Effects
  useEffect(() => {
    // Sync state with URL parameters
    const conversation = searchParams.get("conversation") || null;
    const search = searchParams.get("search") || "";
    const status = searchParams.get("status") || "";

    setSelectedConversation(conversation);
    setSearchQuery(search);
    setSelectedStatus(status);
  }, [searchParams]);

  useEffect(() => {
    // Fetch conversations when filters change
    fetchConversations();
  }, [searchQuery, selectedStatus]);

  useEffect(() => {
    // Fetch conversation items when conversation is selected
    if (selectedConversation) {
      setMessagesPage(1);
      setHasMoreMessages(true);
      setConversationItems([]);
      fetchConversationItems(selectedConversation, 1);
    }
  }, [selectedConversation]);

  useEffect(() => {
    // Auto-scroll to bottom when new messages arrive (unless user is scrolling)
    if (!isUserScrolling && conversationItems.length > 0) {
      setTimeout(() => scrollToBottom(), 100);
    }
  }, [conversationItems, isUserScrolling]);

  useEffect(() => {
    // Add scroll listener
    const container = messagesContainerRef.current;
    if (container) {
      container.addEventListener('scroll', handleScroll);
      return () => container.removeEventListener('scroll', handleScroll);
    }
  }, [handleScroll]);

  // Utility functions
  const getStatusColor = (status: string) => {
    switch (status) {
      case "active":
        return "default"
      case "pending":
        return "destructive"
      case "resolved":
        return "secondary"
      case "archived":
        return "outline"
      default:
        return "secondary"
    }
  };

  const formatDuration = (seconds: number): string => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  const formatTimestamp = (timestamp: string): string => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.floor(diffMs / (1000 * 60));
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

    if (diffMins < 1) return "Just now";
    if (diffMins < 60) return `${diffMins}m ago`;
    if (diffHours < 24) return `${diffHours}h ago`;
    if (diffDays < 7) return `${diffDays}d ago`;
    return date.toLocaleDateString();
  };

  const selectedConversationData = conversations.find((c) => c.id === selectedConversation);

  const statsData = [
    {
      title: "Total Conversations",
      value: stats.totalConversations.toString(),
      description: "All time",
      icon: MessageSquare,
    },
    {
      title: "Active Conversations",
      value: stats.activeConversations.toString(),
      description: "Ongoing discussions",
      icon: User,
    },
    {
      title: "Pending Response",
      value: stats.pendingConversations.toString(),
      description: "Awaiting reply",
      icon: Clock,
    },
    {
      title: "Avg Response Time",
      value: stats.avgResponseTime,
      description: "This week",
      icon: Reply,
    },
  ];

  const renderConversationItem = (item: ConversationItem) => {
    const messageId = `message-${item.id}`;

    if (item.type === "call" || item.type === "voicemail") {
      return (
        <div key={item.id} id={messageId} className="flex justify-center my-4">
          <div className="bg-muted rounded-lg p-3 max-w-md text-center relative group">
            <Link
              href={`#${messageId}`}
              className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity"
            >
              <LinkIcon className="h-3 w-3 text-muted-foreground"/>
            </Link>
            <div className="flex items-center justify-center gap-2 text-sm text-muted-foreground mb-2">
              <PhoneCall className="h-4 w-4"/>
              <span>
                                {item.answered ? "Call answered" : "Missed call"} • {formatDuration(item.duration || 0)}
                            </span>
            </div>
            {item.type === "voicemail" && item.transcription && (
              <div className="mt-2 p-2 bg-background rounded border">
                <div className="flex items-center gap-2 mb-2">
                  <Voicemail className="h-4 w-4"/>
                  <span className="text-sm font-medium">Voicemail</span>
                  {item.audioUrl && (
                    <Button size="sm" variant="ghost" className="h-6 w-6 p-0">
                      <Play className="h-3 w-3"/>
                    </Button>
                  )}
                </div>
                <p className="text-xs text-muted-foreground italic">"{item.transcription}"</p>
              </div>
            )}
            <div className="text-xs text-muted-foreground mt-2">
              {formatTimestamp(item.createdAt)}
            </div>
          </div>
        </div>
      );
    }

    const isLead = item.sender === "lead";
    const isUser = item.sender === "user";
    return (
      <div key={item.id} id={messageId} className={cn("flex mb-4 group", isLead ? "justify-end" : "justify-start")}>
        <div className={cn(
          "max-w-[70%] rounded-lg p-3 relative",
          isLead ? "bg-primary text-primary-foreground" :
            isUser ? "bg-blue-500 text-white" : "bg-muted"
        )}>
          <Link
            href={`#${messageId}`}
            className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity"
          >
            <LinkIcon className="h-3 w-3 text-current opacity-50"/>
          </Link>
          <div className="text-xs opacity-70 mb-1">{item.senderName}</div>
          <div className="text-sm pr-6">{item.content}</div>
          <div className="text-xs opacity-70 mt-1">{formatTimestamp(item.createdAt)}</div>
        </div>
      </div>
    );
  };

  return (
    <div>

      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold tracking-tight">Conversations</h1>
          <p className="text-sm text-muted-foreground">Manage agent conversations</p>
        </div>
      </div>
      <div className="flex h-[calc(100vh-8rem)] gap-6">
        {/* Left Column - Conversations List */}
        <div
          className={cn(
            "space-y-6 transition-all duration-300",
            selectedConversation ? "w-1/4 min-w-[300px]" : "w-full",
            "md:block",
            selectedConversation ? "hidden md:block" : "block",
          )}
        >

          {/* Stats Overview - Condensed */}
          <div className="grid gap-2 grid-cols-2">
            {statsData.map((stat) => (
              <Card key={stat.title} className="p-3">
                <div className="flex items-center justify-between">
                  <div>
                    <div className="text-lg font-bold">{stat.value}</div>
                    <p className="text-xs text-muted-foreground">{stat.title}</p>
                  </div>
                  <stat.icon className="h-4 w-4 text-muted-foreground"/>
                </div>
              </Card>
            ))}
          </div>

          {/* Search and Filters - Condensed */}
          <div className="space-y-2">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground"/>
              <Input
                placeholder="Search conversations..."
                className="pl-9"
                value={searchQuery}
                onChange={(e) => handleSearchChange(e.target.value)}
              />
            </div>
            <div className="flex gap-2">
              <Select value={selectedStatus || "all"} onValueChange={handleStatusChange}>
                <SelectTrigger className="flex-1">
                  <SelectValue/>
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value="active">Active</SelectItem>
                  <SelectItem value="pending">Pending</SelectItem>
                  <SelectItem value="resolved">Resolved</SelectItem>
                  <SelectItem value="archived">Archived</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Conversations List - Condensed */}
          <Card className="flex-1">
            <CardContent className="p-0">
              {conversationsLoading ? (
                <div className="flex items-center justify-center p-8">
                  <Loader2 className="h-6 w-6 animate-spin"/>
                  <span className="ml-2">Loading conversations...</span>
                </div>
              ) : conversationsError ? (
                <div className="p-4">
                  <Alert variant="destructive">
                    <AlertTriangle className="h-4 w-4"/>
                    <AlertDescription>{conversationsError}</AlertDescription>
                  </Alert>
                  <Button
                    variant="outline"
                    size="sm"
                    className="mt-2"
                    onClick={fetchConversations}
                  >
                    Retry
                  </Button>
                </div>
              ) : conversations.length === 0 ? (
                <div className="p-8 text-center text-muted-foreground">
                  <MessageSquare className="h-12 w-12 mx-auto mb-4 opacity-50"/>
                  <p>No conversations found</p>
                  {(searchQuery || selectedStatus) && (
                    <p className="text-sm mt-1">Try adjusting your filters</p>
                  )}
                </div>
              ) : (
                <div className="divide-y max-h-[400px] overflow-y-auto">
                  {conversations.map((conversation) => (
                    <div
                      key={conversation.id}
                      className={cn(
                        "p-3 hover:bg-muted/50 transition-colors cursor-pointer",
                        selectedConversation === conversation.id && "bg-muted border-l-4 border-l-primary",
                      )}
                      onClick={() => handleConversationSelect(conversation.id)}
                    >
                      <div className="flex items-start space-x-3">
                        <Avatar className="h-8 w-8">
                          <AvatarFallback>
                            <AvatarInitials name={conversation.lead.person.name.full}/>
                          </AvatarFallback>
                        </Avatar>

                        <div className="flex-1 min-w-0">
                          <div className="flex items-center gap-2">
                            <p className="font-medium text-sm truncate">
                              <HighlightText
                                text={conversation.lead.person.name.full}
                                searchTerm={searchQuery}
                              />
                            </p>
                            {!conversation.isAgentActive && (
                              <Badge variant="outline" className="text-xs px-1 py-0">
                                Manual
                              </Badge>
                            )}
                          </div>

                          {/* Show search context - check if search term appears in last message */}
                          {searchQuery && conversation.lastMessage.toLowerCase().includes(searchQuery.toLowerCase()) ? (
                            <div className="text-xs text-muted-foreground">
                              <div className="flex items-center gap-1 mb-1">
                                <MessageSquare className="h-3 w-3" />
                                <span className="text-blue-600 font-medium">Message match:</span>
                              </div>
                              <p className="truncate">
                                <HighlightText
                                  text={conversation.lastMessage}
                                  searchTerm={searchQuery}
                                />
                              </p>
                            </div>
                          ) : (
                            <p className="text-xs text-muted-foreground truncate">
                              <HighlightText
                                text={conversation.lastMessage}
                                searchTerm={searchQuery && conversation.lead.person.name.full.toLowerCase().includes(searchQuery.toLowerCase()) ? '' : searchQuery}
                              />
                            </p>
                          )}

                          <div className="flex items-center justify-between mt-1">
                            <div className="flex items-center gap-1">
                              <Badge variant={getStatusColor(conversation.status)} className="text-xs">
                                {conversation.status}
                              </Badge>
                              {conversation.isAgentActive && (
                                <Bot className="h-3 w-3 text-green-500" title="Auto-response enabled"/>
                              )}
                            </div>
                            <span className="text-xs text-muted-foreground">
                              {formatTimestamp(conversation.lastMessageTime)}
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Right Column - Conversation Messages */}
        {selectedConversation && selectedConversationData && (
          <div
            className={cn(
              "flex-1 flex flex-col transition-all duration-300",
              "fixed inset-0 z-50 bg-background md:relative md:z-auto",
              "md:block",
            )}
          >
            <div className="md:hidden flex items-center gap-2 p-4 border-b">
              <Button variant="ghost" size="sm" onClick={() => handleConversationSelect(null)}>
                <ArrowLeft className="h-4 w-4 mr-2"/>
                All Conversations
              </Button>
            </div>

            {/* Conversation Header */}
            <Card className="mb-4">
              <CardHeader className="pb-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <Avatar className="h-10 w-10">
                      <AvatarFallback>
                        <AvatarInitials name={selectedConversationData.lead.person.name.full}/>
                      </AvatarFallback>
                    </Avatar>
                    <div>
                      <CardTitle className="text-lg">{selectedConversationData.lead.person.name.full}</CardTitle>
                      <CardDescription className="flex items-center gap-2">
                        <Phone className="h-3 w-3"/>
                        {selectedConversationData.leadPhone}
                        <span>•</span>
                        <Bot className="h-3 w-3"/>
                        {selectedConversationData.agent.name}
                      </CardDescription>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <Button variant="outline" size="sm">
                      <Calendar className="h-4 w-4 mr-2"/>
                      Schedule
                    </Button>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="sm">
                          <MoreHorizontal className="h-4 w-4"/>
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem>View Lead Details</DropdownMenuItem>
                        <DropdownMenuItem>Mark Important</DropdownMenuItem>
                        <DropdownMenuItem>Archive</DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                </div>

                {/* Conversation Controls */}
                <div className="flex items-center justify-between pt-4 border-t">
                  <div className="flex items-center gap-4">
                    <Select defaultValue={selectedConversationData.status}>
                      <SelectTrigger className="w-32">
                        <SelectValue/>
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="active">Active</SelectItem>
                        <SelectItem value="pending">Pending</SelectItem>
                        <SelectItem value="resolved">Resolved</SelectItem>
                        <SelectItem value="archived">Archived</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Switch
                      id="auto-response"
                      checked={autoResponseEnabled}
                      onCheckedChange={toggleAutoResponse}
                    />
                    <Label htmlFor="auto-response" className="text-sm">
                      Auto-response {!autoResponseEnabled && "(Disabled)"}
                    </Label>
                  </div>
                </div>

                {/* Metrics */}
                <div className="grid grid-cols-4 gap-4 pt-4 border-t">
                  <div className="text-center">
                    <div className="text-lg font-semibold">{selectedConversationData.messageCount}</div>
                    <div className="text-xs text-muted-foreground">Messages</div>
                  </div>
                  <div className="text-center">
                    <div className="text-lg font-semibold">12m</div>
                    <div className="text-xs text-muted-foreground">Avg Response</div>
                  </div>
                  <div className="text-center">
                    <div className="text-lg font-semibold">85%</div>
                    <div className="text-xs text-muted-foreground">Sentiment</div>
                  </div>
                  <div className="text-center">
                    <div className="text-lg font-semibold">$450</div>
                    <div className="text-xs text-muted-foreground">Est. Value</div>
                  </div>
                </div>
              </CardHeader>
            </Card>

            {/* Messages Area */}
            <Card className="flex-1 flex flex-col">
              <CardContent
                ref={messagesContainerRef}
                className="flex-1 p-4 overflow-y-auto max-h-[400px]"
                onScroll={handleScroll}
              >
                {loadingMoreMessages && (
                  <div className="flex items-center justify-center p-4">
                    <Loader2 className="h-4 w-4 animate-spin mr-2"/>
                    <span className="text-sm text-muted-foreground">Loading more messages...</span>
                  </div>
                )}

                {messagesLoading ? (
                  <div className="flex items-center justify-center p-8">
                    <Loader2 className="h-6 w-6 animate-spin"/>
                    <span className="ml-2">Loading messages...</span>
                  </div>
                ) : messagesError ? (
                  <div className="p-4">
                    <Alert variant="destructive">
                      <AlertTriangle className="h-4 w-4"/>
                      <AlertDescription>{messagesError}</AlertDescription>
                    </Alert>
                    <Button
                      variant="outline"
                      size="sm"
                      className="mt-2"
                      onClick={() => selectedConversation && fetchConversationItems(selectedConversation, 1)}
                    >
                      Retry
                    </Button>
                  </div>
                ) : conversationItems.length === 0 ? (
                  <div className="p-8 text-center text-muted-foreground">
                    <MessageSquare className="h-12 w-12 mx-auto mb-4 opacity-50"/>
                    <p>No messages yet</p>
                    <p className="text-sm mt-1">Start the conversation below</p>
                  </div>
                ) : (
                  <>
                    {conversationItems.map(renderConversationItem)}
                    <div ref={messagesEndRef}/>
                  </>
                )}
              </CardContent>

              {/* Auto-response warning */}
              {showAutoResponseWarning && (
                <div className="px-4 py-2 bg-yellow-50 border-t border-yellow-200">
                  <Alert className="border-yellow-300 bg-yellow-50">
                    <AlertTriangle className="h-4 w-4 text-yellow-600"/>
                    <AlertDescription className="text-yellow-800">
                      Manual messages will disable auto-response for this conversation.
                    </AlertDescription>
                  </Alert>
                </div>
              )}

              {/* Message Input */}
              <div className="border-t p-4">
                <div className="flex gap-2">
                  <Textarea
                    placeholder="Type your message..."
                    value={newMessage}
                    onChange={(e) => handleMessageInputChange(e.target.value)}
                    className="flex-1 min-h-[60px] resize-none"
                    onKeyDown={handleKeyDown}
                    disabled={sendingMessage}
                  />
                  <Button
                    onClick={sendMessage}
                    disabled={!newMessage.trim() || sendingMessage}
                    className="self-end"
                  >
                    {sendingMessage ? (
                      <Loader2 className="h-4 w-4 animate-spin"/>
                    ) : (
                      <Send className="h-4 w-4"/>
                    )}
                  </Button>
                </div>
              </div>
            </Card>
          </div>
        )}
      </div>
    </div>
  )
}
