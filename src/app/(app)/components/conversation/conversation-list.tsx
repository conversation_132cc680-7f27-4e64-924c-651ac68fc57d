"use client"

import {useState, useEffect} from "react";
import {Card, CardContent, CardDescription, CardHeader, CardTitle} from "@app/components/ui/card";
import {Button} from "@app/components/ui/button";
import {Avatar, AvatarFallback} from "@app/components/ui/avatar";
import {PhoneNumber} from "@app/components/ui/phoneNumber";
import {Badge} from "@app/components/ui/badge";
import Link from "next/link"
import Agent from "@app/types/agent";
import Conversation from "@app/types/conversation";
import {apiClient} from "@app/lib/api-client";

interface ConversationListProps {
    agent?: string,
    title?: string,
    description?: string,
    limit?: number,
    sort?: string,
}

const getStatusColor = (status: string) => {
    switch (status) {
        case "active":
            return "default"
        case "pending":
            return "secondary"
        case "scheduled":
            return "outline"
        case "new":
            return "default"
        case "qualified":
            return "default"
        default:
            return "secondary"
    }
}

export default function ConversationList({agent, title, description, limit, sort}: ConversationListProps) {
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);
    const [conversations, setConversations] = useState<Conversation[]>([]);

    const fetchConversations = async () => {
        setLoading(true)
        setError(null)
        try {
            const fetchUrl = agent ?
                `/api/v1/agent/${agent}/conversation?paginate[limit]=${limit || 5}&sort=${sort || 'lastMessageTime'}` :
                `/api/v1/conversation?paginate[limit]=${limit || 5}&sort=${sort || 'lastMessageTime'}`;
            const response = await apiClient.get(fetchUrl)

            setConversations(response.data)
        } catch (err) {
            setError(err instanceof Error ? err.message : 'Failed to fetch conversations')
        } finally {
            setLoading(false)
        }
    }

    useEffect(() => {
        fetchConversations();
    }, [agent])

    return (
        <Card>
            <CardHeader>
                <div className="flex items-center justify-between">
                    <div>
                        <CardTitle className="text-lg">{title || "Conversations"}</CardTitle>
                        <CardDescription>{description || "Interactions with leads"}</CardDescription>
                    </div>
                    {agent &&
                        <Button asChild  className="bg-primary-200">
                            <Link href={`/agents/${agent}/conversations`}>
                                View All
                            </Link>
                        </Button>
                    }
                </div>
            </CardHeader>
            <CardContent>
                <div className="space-y-4">
                    {loading ? (
                        <p>Loading...</p>
                    ) : error ? (
                        <p className="text-red-500">Error: {error}</p>
                    ) : (
                        conversations.length === 0 ? (
                            <p className="text-muted-foreground">No conversations</p>
                        ) : (
                            conversations.map((conversation) => (
                                <Link href={`/conversations/${conversation.id}`} className="block" key={conversation.id}>
                                    <div className="flex items-center justify-between p-4 border rounded-lg hover:bg-muted/50 transition-colors">
                                        <div className="flex items-center space-x-3">
                                            <Avatar className="h-8 w-8">
                                                <AvatarFallback>
                                                    {conversation.lead.person.name.full
                                                        .split(" ")
                                                        .map((n) => n[0])
                                                        .join("")}
                                                </AvatarFallback>
                                            </Avatar>
                                            <div>
                                                <p className="font-medium">{conversation.lead.person.name.full}</p>
                                                <p className="text-sm text-muted-foreground"><PhoneNumber phone={conversation.leadPhone}/></p>
                                            </div>
                                        </div>
                                        <div className="flex-1 mx-4">
                                            <p className="text-sm line-clamp-1">{conversation.lastMessage}</p>
                                            <div className="flex items-center space-x-2 mt-1">
                                                <Badge variant={getStatusColor(conversation.status)} className="text-xs">
                                                    {conversation.status}
                                                </Badge>
                                                <span className="text-xs text-muted-foreground">{conversation.messageCount} messages</span>
                                            </div>
                                        </div>
                                        <div className="text-right">
                                            <p className="text-sm text-muted-foreground">{conversation.lastMessageTime}</p>
                                            <div className="flex space-x-1 mt-1">
                                                <Button size="sm" variant="secondary">
                                                    View Conversation
                                                </Button>
                                                {/*<Button size="sm" variant="ghost">*/}
                                                {/*    <Mail className="h-3 w-3"/>*/}
                                                {/*</Button>*/}
                                            </div>
                                        </div>
                                    </div>
                                </Link>
                            ))
                        )
                    )}
                </div>
            </CardContent>
        </Card>
    );
}
