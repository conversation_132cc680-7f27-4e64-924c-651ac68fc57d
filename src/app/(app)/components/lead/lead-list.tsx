"use client";

import {useState, useEffect} from "react";
import {Card, CardContent, CardDescription, CardHeader, CardTitle} from "@app/components/ui/card";
import {Button} from "@app/components/ui/button";
import {Avatar, AvatarFallback} from "@app/components/ui/avatar";
import {formatPhoneNumber, PhoneNumber} from "@app/components/ui/phoneNumber";
import {Badge} from "@app/components/ui/badge";
import Link from "next/link"
import Agent from "@app/types/agent";
import Lead, {LeadResponse} from "@app/types/lead";
import {apiClient} from "@app/lib/api-client";

interface LeadListProps {
    agent?: string,
    title?: string,
    description?: string,
    limit?: number,
    sort?: string,
}

const getStatusColor = (status: string) => {
    switch (status) {
        case "active":
            return "default"
        case "pending":
            return "secondary"
        case "scheduled":
            return "outline"
        case "new":
            return "default"
        case "qualified":
            return "default"
        default:
            return "secondary"
    }
}

export default function LeadList({agent, title, description, limit, sort}: LeadListProps)
{
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);
    const [leads, setLeads] = useState<Lead[]>([]);

    const fetchLeads = async () => {
        setLoading(true)
        setError(null)
        try {
            const fetchUrl = `/api/v1/lead`;
            const queryParams: string[] = [];
            if (agent) {
                queryParams.push(`filter[agent]=${agent}`)
            }
            if (limit) {
                queryParams.push(`paginate[limit]=${limit}`)
            }
            if (sort) {
                queryParams.push(`sort=${sort}`)
            }
            const response: LeadResponse = await apiClient.get(fetchUrl+'?'+queryParams.join('&'))

            setLeads(response.data)
        } catch (err) {
            setError(err instanceof Error ? err.message : 'Failed to fetch leads')
        } finally {
            setLoading(false)
        }
    }

    useEffect(() => {
        fetchLeads();
    }, [agent])

    return (
        <Card>
            <CardHeader>
                <div className="flex items-center justify-between">
                    <div>
                        <CardTitle className="text-lg">{title || "Leads"}</CardTitle>
                        <CardDescription>{description || "Leads generated from your agents"}</CardDescription>
                    </div>
                    {agent &&
                        <Button asChild  className="bg-primary-200">
                            <Link href={`/leads?agent=${agent}`} className="bg-primary-300">
                                View All
                            </Link>
                        </Button>
                    }
                </div>
            </CardHeader>
            <CardContent>
                <div className="space-y-4">
                    {loading ? (
                        <p>Loading...</p>
                    ) : error ? (
                        <p className="text-red-500">Error: {error}</p>
                    ) : (
                        leads.length === 0 ? (
                            <p className="text-muted-foreground">No leads</p>
                        ) : (
                            leads.map((lead) => (
                                <Link href={`/leads/${lead.id}`} className="block" key={lead.id}>
                                    <div key={lead.id} className="flex items-center justify-between p-3 border rounded-lg">
                                        <div>
                                            <p className="font-medium">{lead.person?.name?.full || formatPhoneNumber(lead.phone)}</p>
                                            <p className="text-sm text-muted-foreground"><PhoneNumber phone={lead.phone}/></p>
                                            <div className="flex items-center space-x-2 mt-1">
                                                <Badge variant={getStatusColor(lead.status)} className="text-xs">
                                                    {lead.status}
                                                </Badge>
                                                <span className="text-xs text-muted-foreground">Score: {lead.score}</span>
                                            </div>
                                        </div>
                                        <div className="text-right">
                                            <p className="font-medium">${lead.estimatedValue?.toLocaleString()}</p>
                                            <p className="text-xs text-muted-foreground">{lead.metadata.createdAt}</p>
                                        </div>
                                    </div>
                                </Link>

                            ))
                        )
                    )}
                </div>
            </CardContent>
        </Card>
    );
}
