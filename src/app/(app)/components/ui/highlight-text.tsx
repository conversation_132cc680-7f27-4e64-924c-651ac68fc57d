import React from 'react';
import { cn } from '@app/lib/utils';

interface HighlightTextProps {
  text: string;
  searchTerm: string;
  className?: string;
  highlightClassName?: string;
}

export function HighlightText({ 
  text, 
  searchTerm, 
  className,
  highlightClassName = "bg-yellow-200 text-yellow-900 px-0.5 rounded"
}: HighlightTextProps) {
  if (!searchTerm || !text) {
    return <span className={className}>{text}</span>;
  }

  // Create a case-insensitive regex to find all matches
  const regex = new RegExp(`(${searchTerm.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})`, 'gi');
  const parts = text.split(regex);

  return (
    <span className={className}>
      {parts.map((part, index) => {
        // Check if this part matches the search term (case-insensitive)
        const isMatch = regex.test(part);
        // Reset regex lastIndex for next test
        regex.lastIndex = 0;
        
        return isMatch ? (
          <mark key={index} className={cn(highlightClassName)}>
            {part}
          </mark>
        ) : (
          <span key={index}>{part}</span>
        );
      })}
    </span>
  );
}
