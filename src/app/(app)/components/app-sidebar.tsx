"use client"

import type * as React from "react"
import Link from "next/link"
import { usePathname } from "next/navigation"
import {LayoutDashboard, Bot, MessageSquare, Users, Settings, Calendar, LogOut} from "lucide-react"

import {
    Sidebar,
    SidebarContent,
    SidebarFooter,
    SidebarGroup,
    SidebarGroupContent,
    SidebarHeader,
    SidebarMenu,
    SidebarMenuButton,
    SidebarMenuItem,
    SidebarRail, SidebarSeparator,
} from "@app/components/ui/sidebar"
import Logo from "@/assets/logo.svg";
import {useAuth, User} from "@app/lib/use-auth";
import { Avatar, AvatarFallback, AvatarInitials } from "@app/components/ui/avatar"
import {useAuthContext} from "@app/lib/auth-context";

// Navigation items for Back-Talk
const items = [
    {
        title: "Dashboard",
        url: "/dashboard",
        icon: LayoutDashboard,
        attention: false
    },
    {
        title: "Agents",
        url: "/agents",
        icon: Bo<PERSON>,
        attention: false
    },
    {
        title: "Conversations",
        url: "/conversations",
        icon: MessageSquare,
        attention: true
    },
    {
        title: "Leads",
        url: "/leads",
        icon: Users,
        attention: false
    },
    {
        title: "Schedule",
        url: "/schedule",
        icon: Calendar,
        attention: true
    },
    // {
    //     title: "Settings",
    //     url: "/settings",
    //     icon: Settings,
    // },
]

export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
    const pathname = usePathname()

    const {user, logout} = useAuthContext();
    return (
        <Sidebar collapsible="icon" {...props}>
            <SidebarHeader>
                <SidebarMenu>
                    <SidebarMenuItem>
                        <SidebarMenuButton size="lg" asChild>
                            <Link href="/dashboard">
                                <div className="flex items-center space-x-3">
                                    <Logo aria-label="Back-Talk logo" width={32} height={32} style={{ "--logo-primary": "var(--secondary-foreground)", "--logo-secondary": "var(--color-primary-300)" }} className="w-8 h-8" />
                                    <span className="text-foreground text-xl font-bold"><span className="text-foreground">back-</span><span className="text-primary-300">talk</span></span>
                                </div>
                            </Link>
                        </SidebarMenuButton>
                    </SidebarMenuItem>
                </SidebarMenu>
            </SidebarHeader>
            <SidebarContent>
                <SidebarGroup>
                    <SidebarGroupContent>
                        <SidebarMenu>
                            {items.map((item) => (
                                <SidebarMenuItem key={item.title}>
                                    <SidebarMenuButton tooltip={item.title} isActive={pathname === item.url} asChild>
                                        <Link href={item.url} className="flex">
                                            <div className="inline-flex grow gap-2 items-center">
                                                <item.icon className="text-primary-200 max-w-5"/>
                                                <span>{item.title}</span>
                                            </div>
                                            {item.attention && <div className="h-2 w-2 bg-primary-500 rounded-full flex-shrink-0" />}
                                        </Link>
                                    </SidebarMenuButton>
                                </SidebarMenuItem>
                            ))}
                        </SidebarMenu>
                    </SidebarGroupContent>
                </SidebarGroup>
            </SidebarContent>
            <SidebarFooter>
                <SidebarMenu>
                    <SidebarSeparator />
                    <SidebarMenuItem>
                        <SidebarMenuButton size="lg" asChild>
                            <Link href="/settings">
                                <div className="flex aspect-square size-8 items-center justify-center rounded-lg bg-muted">
                                    <Avatar className="h-8 w-8">
                                        <AvatarFallback className="bg-primary/10">
                                            <AvatarInitials name={`${user?.person?.firstName || ''} ${user?.person?.lastName || ''}`.trim() || '??'} />
                                        </AvatarFallback>
                                    </Avatar>
                                </div>
                                <div className="grid flex-1 text-left text-sm leading-tight">
                                    <span className="truncate font-semibold">{user?.person?.firstName} {user?.person?.lastName}</span>
                                </div>
                            </Link>
                        </SidebarMenuButton>
                    </SidebarMenuItem>
                    <SidebarMenuItem>
                        <SidebarMenuButton size="lg" asChild>
                            <Link href="/settings">
                                <div className="flex aspect-square size-8 items-center justify-center rounded-lg bg-muted">
                                    <Settings className="size-4" />
                                </div>
                                <div className="grid flex-1 text-left text-sm leading-tight">
                                    <span className="truncate text-xs">Manage settings</span>
                                </div>
                            </Link>
                        </SidebarMenuButton>
                    </SidebarMenuItem>
                    <SidebarMenuItem>
                        <SidebarMenuButton size="lg" asChild>
                            <Link href="#" onClick={logout}>
                                <div className="flex aspect-square size-8 items-center justify-center rounded-lg bg-muted">
                                    <LogOut className="size-4" />
                                </div>
                                <div className="grid flex-1 text-left text-sm leading-tight">
                                    <span className="truncate font-semibold">Logout</span>
                                </div>
                            </Link>
                        </SidebarMenuButton>
                    </SidebarMenuItem>
                </SidebarMenu>
            </SidebarFooter>
            <SidebarRail />
        </Sidebar>
    )
}
