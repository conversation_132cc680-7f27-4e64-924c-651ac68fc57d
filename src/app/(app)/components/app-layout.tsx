"use client"

import React from "react"

import {<PERSON>bar<PERSON>nset, SidebarProvider, SidebarTrigger} from "@app/components/ui/sidebar"
import {AppSidebar} from "@app/components/app-sidebar"
import {Separator} from "@app/components/ui/separator"
import {
    Breadcrumb,
    BreadcrumbItem,
    BreadcrumbLink,
    BreadcrumbList,
    BreadcrumbPage,
    BreadcrumbSeparator,
} from "@app/components/ui/breadcrumb"

interface AppLayoutProps {
    children: React.ReactNode
    breadcrumbs?: Array<{
        label: string
        href?: string
    }>
}

export function AppLayout({children, breadcrumbs}: AppLayoutProps) {
    return (
        <SidebarProvider>
                <AppSidebar/>
                <SidebarInset>
                    <header className="flex h-16 shrink-0 items-center gap-2 transition-[width,height] ease-linear group-has-data-[collapsible=icon]/sidebar-wrapper:h-12">
                        <div className="flex items-center gap-2 px-4">
                            <SidebarTrigger className="-ml-1"/>
                            <Separator orientation="vertical" className="mr-2 h-4"/>
                            {breadcrumbs && (
                                <Breadcrumb>
                                    <BreadcrumbList>
                                        {breadcrumbs.map((crumb, index) => (
                                            <React.Fragment key={index}>
                                                <BreadcrumbItem className="hidden md:block">
                                                    {crumb.href ? (
                                                        <BreadcrumbLink href={crumb.href}>{crumb.label}</BreadcrumbLink>
                                                    ) : (
                                                        <BreadcrumbPage>{crumb.label}</BreadcrumbPage>
                                                    )}
                                                </BreadcrumbItem>
                                                {index < breadcrumbs.length - 1 && <BreadcrumbSeparator className="hidden md:block"/>}
                                            </React.Fragment>
                                        ))}
                                    </BreadcrumbList>
                                </Breadcrumb>
                            )}
                        </div>
                    </header>
                    <div className="flex flex-1 flex-col gap-4 p-4 pt-0">{children}</div>
                </SidebarInset>
        </SidebarProvider>
    )
}
