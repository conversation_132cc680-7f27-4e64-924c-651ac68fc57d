{"_readme": ["This file locks the dependencies of your project to a known state", "Read more about it at https://getcomposer.org/doc/01-basic-usage.md#installing-dependencies", "This file is @generated automatically"], "content-hash": "4b1e82672d8e30eecb41b7309482fb3d", "packages": [{"name": "alexkart/curl-builder", "version": "1.1.0", "source": {"type": "git", "url": "https://github.com/alexkart/curl-builder.git", "reference": "ce9224d8a33625059ae1c549b101061cf8d8bc27"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/alexkart/curl-builder/zipball/ce9224d8a33625059ae1c549b101061cf8d8bc27", "reference": "ce9224d8a33625059ae1c549b101061cf8d8bc27", "shasum": ""}, "require": {"php": ">=7.3", "psr/http-message": "^2.0"}, "require-dev": {"nyholm/psr7": "^1.3", "phan/phan": "^5.4", "phpstan/phpstan": "^1.10", "phpunit/phpunit": "^9.5"}, "type": "library", "autoload": {"psr-4": {"Alexkart\\CurlBuilder\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "PSR-7 compatible curl builder.", "support": {"issues": "https://github.com/alexkart/curl-builder/issues", "source": "https://github.com/alexkart/curl-builder/tree/1.1.0"}, "time": "2025-06-03T23:28:32+00:00"}, {"name": "brick/math", "version": "0.14.0", "source": {"type": "git", "url": "https://github.com/brick/math.git", "reference": "113a8ee2656b882d4c3164fa31aa6e12cbb7aaa2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/brick/math/zipball/113a8ee2656b882d4c3164fa31aa6e12cbb7aaa2", "reference": "113a8ee2656b882d4c3164fa31aa6e12cbb7aaa2", "shasum": ""}, "require": {"php": "^8.2"}, "require-dev": {"php-coveralls/php-coveralls": "^2.2", "phpstan/phpstan": "2.1.22", "phpunit/phpunit": "^11.5"}, "type": "library", "autoload": {"psr-4": {"Brick\\Math\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Arbitrary-precision arithmetic library", "keywords": ["Arbitrary-precision", "BigInteger", "BigRational", "arithmetic", "bigdecimal", "bignum", "bignumber", "brick", "decimal", "integer", "math", "mathematics", "rational"], "support": {"issues": "https://github.com/brick/math/issues", "source": "https://github.com/brick/math/tree/0.14.0"}, "funding": [{"url": "https://github.com/BenMorel", "type": "github"}], "time": "2025-08-29T12:40:03+00:00"}, {"name": "butschster/cron-expression-generator", "version": "v1.10.2", "source": {"type": "git", "url": "https://github.com/butschster/CronExpressionGenerator.git", "reference": "d47e3d36b7d67c58c6e46e6f14e0f00ed6a37ef4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/butschster/CronExpressionGenerator/zipball/d47e3d36b7d67c58c6e46e6f14e0f00ed6a37ef4", "reference": "d47e3d36b7d67c58c6e46e6f14e0f00ed6a37ef4", "shasum": ""}, "require": {"dragonmantank/cron-expression": "^3.1", "php": "^8.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "^3.0", "phpunit/phpunit": "^9.5", "spatie/ray": "^1.28", "vimeo/psalm": "^4.8"}, "type": "library", "autoload": {"psr-4": {"Butschster\\CronExpression\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Cron expression generator", "keywords": ["cron", "generator", "php8"], "support": {"issues": "https://github.com/butschster/CronExpressionGenerator/issues", "source": "https://github.com/butschster/CronExpressionGenerator/tree/v1.10.2"}, "funding": [{"url": "https://github.com/butschster", "type": "github"}], "time": "2021-08-09T13:39:19+00:00"}, {"name": "carbonphp/carbon-doctrine-types", "version": "3.2.0", "source": {"type": "git", "url": "https://github.com/CarbonPHP/carbon-doctrine-types.git", "reference": "18ba5ddfec8976260ead6e866180bd5d2f71aa1d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/CarbonPHP/carbon-doctrine-types/zipball/18ba5ddfec8976260ead6e866180bd5d2f71aa1d", "reference": "18ba5ddfec8976260ead6e866180bd5d2f71aa1d", "shasum": ""}, "require": {"php": "^8.1"}, "conflict": {"doctrine/dbal": "<4.0.0 || >=5.0.0"}, "require-dev": {"doctrine/dbal": "^4.0.0", "nesbot/carbon": "^2.71.0 || ^3.0.0", "phpunit/phpunit": "^10.3"}, "type": "library", "autoload": {"psr-4": {"Carbon\\Doctrine\\": "src/Carbon/Doctrine/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "KyleKatarn", "email": "<EMAIL>"}], "description": "Types to use Carbon in Doctrine", "keywords": ["carbon", "date", "datetime", "doctrine", "time"], "support": {"issues": "https://github.com/CarbonPHP/carbon-doctrine-types/issues", "source": "https://github.com/CarbonPHP/carbon-doctrine-types/tree/3.2.0"}, "funding": [{"url": "https://github.com/kylekatarnls", "type": "github"}, {"url": "https://opencollective.com/Carbon", "type": "open_collective"}, {"url": "https://tidelift.com/funding/github/packagist/nesbot/carbon", "type": "tidelift"}], "time": "2024-02-09T16:56:22+00:00"}, {"name": "cebe/markdown", "version": "1.2.1", "source": {"type": "git", "url": "https://github.com/cebe/markdown.git", "reference": "9bac5e971dd391e2802dca5400bbeacbaea9eb86"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/cebe/markdown/zipball/9bac5e971dd391e2802dca5400bbeacbaea9eb86", "reference": "9bac5e971dd391e2802dca5400bbeacbaea9eb86", "shasum": ""}, "require": {"lib-pcre": "*", "php": ">=5.4.0"}, "require-dev": {"cebe/indent": "*", "facebook/xhprof": "*@dev", "phpunit/phpunit": "4.1.*"}, "bin": ["bin/markdown"], "type": "library", "extra": {"branch-alias": {"dev-master": "1.2.x-dev"}}, "autoload": {"psr-4": {"cebe\\markdown\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://cebe.cc/", "role": "Creator"}], "description": "A super fast, highly extensible markdown parser for PHP", "homepage": "https://github.com/cebe/markdown#readme", "keywords": ["extensible", "fast", "gfm", "markdown", "markdown-extra"], "support": {"issues": "https://github.com/cebe/markdown/issues", "source": "https://github.com/cebe/markdown"}, "time": "2018-03-26T11:24:36+00:00"}, {"name": "cocur/slugify", "version": "v4.6.0", "source": {"type": "git", "url": "https://github.com/cocur/slugify.git", "reference": "1d674022e9cbefa80b4f51aa3e2375b6e3c14fdb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/cocur/slugify/zipball/1d674022e9cbefa80b4f51aa3e2375b6e3c14fdb", "reference": "1d674022e9cbefa80b4f51aa3e2375b6e3c14fdb", "shasum": ""}, "require": {"ext-mbstring": "*", "php": "~8.0.0 || ~8.1.0 || ~8.2.0 || ~8.3.0 || ~8.4.0"}, "conflict": {"symfony/config": "<3.4 || >=4,<4.3", "symfony/dependency-injection": "<3.4 || >=4,<4.3", "symfony/http-kernel": "<3.4 || >=4,<4.3", "twig/twig": "<2.12.1"}, "require-dev": {"laravel/framework": "^5.0|^6.0|^7.0|^8.0", "latte/latte": "~2.2", "league/container": "^2.2.0", "mikey179/vfsstream": "~1.6.8", "mockery/mockery": "^1.3", "nette/di": "~2.4", "pimple/pimple": "~1.1", "plumphp/plum": "~0.1", "symfony/config": "^3.4 || ^4.3 || ^5.0 || ^6.0", "symfony/dependency-injection": "^3.4 || ^4.3 || ^5.0 || ^6.0", "symfony/http-kernel": "^3.4 || ^4.3 || ^5.0 || ^6.0", "symfony/phpunit-bridge": "^5.4 || ^6.0", "twig/twig": "^2.12.1 || ~3.0", "zendframework/zend-modulemanager": "~2.2", "zendframework/zend-servicemanager": "~2.2", "zendframework/zend-view": "~2.2"}, "type": "library", "autoload": {"psr-4": {"Cocur\\Slugify\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://florian.ec"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Converts a string into a slug.", "keywords": ["slug", "slugify"], "support": {"issues": "https://github.com/cocur/slugify/issues", "source": "https://github.com/cocur/slugify/tree/v4.6.0"}, "time": "2024-09-10T14:09:25+00:00"}, {"name": "codedungeon/php-cli-colors", "version": "1.12.2", "source": {"type": "git", "url": "https://github.com/mike<PERSON>son/php-cli-colors.git", "reference": "e346156f75717140a3dd622124d2ec686aa7ff8e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/mike<PERSON>son/php-cli-colors/zipball/e346156f75717140a3dd622124d2ec686aa7ff8e", "reference": "e346156f75717140a3dd622124d2ec686aa7ff8e", "shasum": ""}, "require-dev": {"phpunit/phpunit": ">=5.2"}, "type": "library", "autoload": {"psr-4": {"Codedungeon\\PHPCliColors\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Liven up you PHP Console Apps with standard colors", "homepage": "https://github.com/mike<PERSON>son/php-cli-colors", "keywords": ["color", "colors", "composer", "package", "php"], "support": {"issues": "https://github.com/mike<PERSON>son/php-cli-colors/issues", "source": "https://github.com/mike<PERSON>son/php-cli-colors/tree/1.12.2"}, "time": "2021-01-05T04:48:27+00:00"}, {"name": "composer/semver", "version": "3.4.4", "source": {"type": "git", "url": "https://github.com/composer/semver.git", "reference": "198166618906cb2de69b95d7d47e5fa8aa1b2b95"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/composer/semver/zipball/198166618906cb2de69b95d7d47e5fa8aa1b2b95", "reference": "198166618906cb2de69b95d7d47e5fa8aa1b2b95", "shasum": ""}, "require": {"php": "^5.3.2 || ^7.0 || ^8.0"}, "require-dev": {"phpstan/phpstan": "^1.11", "symfony/phpunit-bridge": "^3 || ^7"}, "type": "library", "extra": {"branch-alias": {"dev-main": "3.x-dev"}}, "autoload": {"psr-4": {"Composer\\Semver\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.naderman.de"}, {"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "http://seld.be"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://robbast.nl"}], "description": "Semver library that offers utilities, version constraint parsing and validation.", "keywords": ["semantic", "semver", "validation", "versioning"], "support": {"irc": "ircs://irc.libera.chat:6697/composer", "issues": "https://github.com/composer/semver/issues", "source": "https://github.com/composer/semver/tree/3.4.4"}, "funding": [{"url": "https://packagist.com", "type": "custom"}, {"url": "https://github.com/composer", "type": "github"}], "time": "2025-08-20T19:15:30+00:00"}, {"name": "cycle/annotated", "version": "v4.3.1", "source": {"type": "git", "url": "https://github.com/cycle/annotated.git", "reference": "f996d3ee0c22aa8f2c03dca5d693408f8b7fdbbe"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/cycle/annotated/zipball/f996d3ee0c22aa8f2c03dca5d693408f8b7fdbbe", "reference": "f996d3ee0c22aa8f2c03dca5d693408f8b7fdbbe", "shasum": ""}, "require": {"cycle/database": "^2.15", "cycle/orm": "^2.9.2", "cycle/schema-builder": "^2.11.1", "doctrine/inflector": "^2.0", "php": ">=8.1", "spiral/attributes": "^2.8|^3.0", "spiral/tokenizer": "^2.8|^3.0"}, "require-dev": {"doctrine/annotations": "^1.14.3 || ^2.0.1", "phpunit/phpunit": "^10.1", "spiral/code-style": "^2.2", "spiral/dumper": "^3.3", "vimeo/psalm": "^5.26 || ^6.0"}, "type": "library", "autoload": {"psr-4": {"Cycle\\Annotated\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON> (wolfy-j)", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON> (roxblnfk)", "email": "<EMAIL>"}, {"name": "<PERSON> (but<PERSON><PERSON>)", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON> (msmakouz)", "email": "<EMAIL>"}], "description": "Cycle ORM Annotated Entities generator", "homepage": "https://cycle-orm.dev", "support": {"chat": "https://discord.gg/spiralphp", "docs": "https://cycle-orm.dev/docs", "issues": "https://github.com/cycle/annotated/issues", "source": "https://github.com/cycle/annotated"}, "funding": [{"url": "https://github.com/sponsors/cycle", "type": "github"}], "time": "2025-07-22T06:19:06+00:00"}, {"name": "cycle/database", "version": "2.15.0", "source": {"type": "git", "url": "https://github.com/cycle/database.git", "reference": "3d7ee3524b299c5897e2b03dc51bad2ddd609a90"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/cycle/database/zipball/3d7ee3524b299c5897e2b03dc51bad2ddd609a90", "reference": "3d7ee3524b299c5897e2b03dc51bad2ddd609a90", "shasum": ""}, "require": {"ext-pdo": "*", "php": ">=8.0", "psr/log": "1 - 3", "spiral/core": "^2.8 || ^3.0", "spiral/pagination": "^2.8 || ^3.0", "symfony/polyfill-php83": "^1.28"}, "conflict": {"spiral/database": "*"}, "require-dev": {"ergebnis/composer-normalize": "^2.42", "infection/infection": ">=0.26.10", "mockery/mockery": "^1.5", "phpunit/phpunit": "^9.5", "spiral/code-style": "^2.2.0", "spiral/tokenizer": "^2.14 || ^3.0", "vimeo/psalm": "^5.26 || ^6.6"}, "type": "library", "autoload": {"files": ["src/polyfill.php", "src/functions_polyfill.php"], "psr-4": {"Cycle\\Database\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON> (wolfy-j)", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON> (roxblnfk)", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON> (msmakouz)", "email": "<EMAIL>"}, {"name": "<PERSON> (but<PERSON><PERSON>)", "email": "<EMAIL>"}], "description": "DBAL, schema introspection, migration and pagination", "homepage": "https://cycle-orm.dev", "keywords": ["database", "dbal", "mssql", "mysql", "orm", "postgresql", "query-builder", "sql", "sqlite"], "support": {"chat": "https://discord.gg/spiralphp", "docs": "https://cycle-orm.dev/docs", "issues": "https://github.com/cycle/database/issues", "source": "https://github.com/cycle/database"}, "funding": [{"url": "https://github.com/sponsors/cycle", "type": "github"}], "time": "2025-07-22T05:27:52+00:00"}, {"name": "cycle/entity-behavior", "version": "1.7.0", "source": {"type": "git", "url": "https://github.com/cycle/entity-behavior.git", "reference": "0c8d84fb3eaa50ec426f336a158d62ad2b4a83b6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/cycle/entity-behavior/zipball/0c8d84fb3eaa50ec426f336a158d62ad2b4a83b6", "reference": "0c8d84fb3eaa50ec426f336a158d62ad2b4a83b6", "shasum": ""}, "require": {"cycle/database": "^2.14", "cycle/orm": "^2.10", "cycle/schema-builder": "^2.8", "php": ">=8.0", "psr/container": "^1.0|^2.0", "psr/event-dispatcher": "^1", "yiisoft/injector": "^1.0"}, "require-dev": {"cycle/annotated": "^3.0", "phpunit/phpunit": "^9.5", "ramsey/uuid": "^4.5", "spiral/code-style": "^2.2", "spiral/tokenizer": "^2.8 || ^3.0", "vimeo/psalm": "^5.11 || ^6.8"}, "type": "library", "autoload": {"psr-4": {"Cycle\\ORM\\Entity\\Behavior\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON> (wolfy-j)", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON> (roxblnfk)", "email": "<EMAIL>"}, {"name": "<PERSON> (but<PERSON><PERSON>)", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON> (msmakouz)", "email": "<EMAIL>"}], "description": "Provides a collection of attributes that add behaviors to Cycle ORM entities", "homepage": "https://cycle-orm.dev", "support": {"chat": "https://discord.gg/spiralphp", "docs": "https://cycle-orm.dev/docs", "issues": "https://github.com/cycle/entity-behavior/issues", "source": "https://github.com/cycle/entity-behavior"}, "funding": [{"url": "https://github.com/sponsors/cycle", "type": "github"}], "time": "2025-07-22T05:27:05+00:00"}, {"name": "cycle/entity-behavior-uuid", "version": "1.2.0", "source": {"type": "git", "url": "https://github.com/cycle/entity-behavior-uuid.git", "reference": "04b27fcd5e0b7bce9a596889215f6a999d8cb2e9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/cycle/entity-behavior-uuid/zipball/04b27fcd5e0b7bce9a596889215f6a999d8cb2e9", "reference": "04b27fcd5e0b7bce9a596889215f6a999d8cb2e9", "shasum": ""}, "require": {"cycle/entity-behavior": "^1.3", "php": ">=8.0", "ramsey/uuid": "^4.5"}, "require-dev": {"cycle/annotated": "^3.0", "phpunit/phpunit": "^9.5", "spiral/tokenizer": "^2.8", "vimeo/psalm": "^5.11"}, "type": "library", "autoload": {"psr-4": {"Cycle\\ORM\\Entity\\Behavior\\Uuid\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON> (wolfy-j)", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON> (roxblnfk)", "email": "<EMAIL>"}, {"name": "<PERSON> (but<PERSON><PERSON>)", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON> (msmakouz)", "email": "<EMAIL>"}], "description": "Provides an ability to use ramsey/uuid as a Cycle ORM entity column type", "homepage": "https://cycle-orm.dev", "support": {"chat": "https://discord.gg/spiralphp", "docs": "https://cycle-orm.dev/docs", "issues": "https://github.com/cycle/entity-behavior-uuid/issues", "source": "https://github.com/cycle/entity-behavior-uuid"}, "funding": [{"url": "https://github.com/sponsors/cycle", "type": "github"}], "time": "2024-02-08T19:43:45+00:00"}, {"name": "cycle/migrations", "version": "v4.2.6", "source": {"type": "git", "url": "https://github.com/cycle/migrations.git", "reference": "c1712b6703441a381f707dd710cc3880c004b92c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/cycle/migrations/zipball/c1712b6703441a381f707dd710cc3880c004b92c", "reference": "c1712b6703441a381f707dd710cc3880c004b92c", "shasum": ""}, "require": {"cycle/database": "^2.7.0", "php": ">=8.1", "spiral/core": "^3.0", "spiral/files": "^3.0", "spiral/reactor": "^3.0", "spiral/tokenizer": "^3.0"}, "require-dev": {"buggregator/trap": "^1.11", "mockery/mockery": "^1.5", "phpunit/phpunit": "^9.5", "spiral/code-style": "^2.2.0", "vimeo/psalm": "^6.4"}, "type": "library", "autoload": {"psr-4": {"Cycle\\Migrations\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Database migrations, migration scaffolding", "support": {"issues": "https://github.com/cycle/migrations/issues", "source": "https://github.com/cycle/migrations/tree/v4.2.6"}, "time": "2025-07-13T07:22:37+00:00"}, {"name": "cycle/orm", "version": "v2.11.0", "source": {"type": "git", "url": "https://github.com/cycle/orm.git", "reference": "d712c79eab82a2393863c67c15e37b89fd64b555"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/cycle/orm/zipball/d712c79eab82a2393863c67c15e37b89fd64b555", "reference": "d712c79eab82a2393863c67c15e37b89fd64b555", "shasum": ""}, "require": {"cycle/database": "^2.8.1", "doctrine/instantiator": "^1.3.1 || ^2.0", "ext-pdo": "*", "php": ">=8.0", "spiral/core": "^2.8 || ^3.0"}, "require-dev": {"doctrine/collections": "^1.6 || ^2.0", "illuminate/collections": "9 - 11", "loophp/collection": "^6.0 || ^7.0", "mockery/mockery": "^1.1", "phpunit/phpunit": "^9.5", "ramsey/uuid": "^4.0", "spiral/code-style": "~2.2.0", "spiral/tokenizer": "^2.8 || ^3.0", "symfony/var-dumper": "^5.2 || ^6.0 || ^7.0", "vimeo/psalm": "5.21 || ^6.8"}, "type": "library", "autoload": {"psr-4": {"Cycle\\ORM\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON> (wolfy-j)", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON> (roxblnfk)", "email": "<EMAIL>"}, {"name": "<PERSON> (but<PERSON><PERSON>)", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON> (msmakouz)", "email": "<EMAIL>"}], "description": "PHP DataMapper ORM and Data Modelling Engine", "homepage": "https://cycle-orm.dev", "keywords": ["data-mapper", "mssql", "mysql", "orm", "postgresql", "query-builder", "sql", "sqlite"], "support": {"chat": "https://discord.gg/spiralphp", "docs": "https://cycle-orm.dev/docs", "issues": "https://github.com/cycle/orm/issues", "source": "https://github.com/cycle/orm"}, "funding": [{"url": "https://github.com/sponsors/cycle", "type": "github"}], "time": "2025-09-09T09:42:43+00:00"}, {"name": "cycle/schema-builder", "version": "v2.11.2", "source": {"type": "git", "url": "https://github.com/cycle/schema-builder.git", "reference": "c59071a22dc9368a599253f541ff5338a61a1511"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/cycle/schema-builder/zipball/c59071a22dc9368a599253f541ff5338a61a1511", "reference": "c59071a22dc9368a599253f541ff5338a61a1511", "shasum": ""}, "require": {"cycle/database": "^2.7.1", "cycle/orm": "^2.7", "php": ">=8.0", "yiisoft/friendly-exception": "^1.1"}, "require-dev": {"phpunit/phpunit": "^9.5", "spiral/code-style": "^2.2", "spiral/tokenizer": "^2.8", "symfony/console": "^6.0 || ^7.0", "vimeo/psalm": "^5.12 || ^6.12"}, "type": "library", "autoload": {"psr-4": {"Cycle\\Schema\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON> (wolfy-j)", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON> (roxblnfk)", "email": "<EMAIL>"}, {"name": "<PERSON> (but<PERSON><PERSON>)", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON> (msmakouz)", "email": "<EMAIL>"}], "description": "Cycle ORM Schema Builder", "support": {"issues": "https://github.com/cycle/schema-builder/issues", "source": "https://github.com/cycle/schema-builder/tree/v2.11.2"}, "funding": [{"url": "https://github.com/sponsors/cycle", "type": "github"}], "time": "2025-07-10T03:45:14+00:00"}, {"name": "cycle/schema-migrations-generator", "version": "2.3.0", "source": {"type": "git", "url": "https://github.com/cycle/schema-migrations-generator.git", "reference": "766c68dc1c89aa20128a4cecf1995f101c05e5f0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/cycle/schema-migrations-generator/zipball/766c68dc1c89aa20128a4cecf1995f101c05e5f0", "reference": "766c68dc1c89aa20128a4cecf1995f101c05e5f0", "shasum": ""}, "require": {"cycle/database": "^2.11.3", "cycle/migrations": "^4.2.4", "cycle/schema-builder": "^2.11.1", "php": ">=8.1"}, "require-dev": {"cycle/annotated": "^3.5", "cycle/orm": "^2.9.1", "phpunit/phpunit": "^9.6.22", "spiral/code-style": "^2.2.0", "spiral/dumper": "^3.3.1", "spiral/framework": "^3.14.8", "vimeo/psalm": "^5.26.1"}, "type": "library", "autoload": {"psr-4": {"Cycle\\Schema\\Generator\\Migrations\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Cycle ORM Migration generation", "support": {"chat": "https://discord.gg/spiralphp", "docs": "https://cycle-orm.dev/docs", "issues": "https://github.com/cycle/schema-migrations-generator/issues", "source": "https://github.com/cycle/schema-migrations-generator"}, "funding": [{"url": "https://github.com/sponsors/cycle", "type": "github"}], "time": "2024-12-23T11:27:31+00:00"}, {"name": "cycle/schema-renderer", "version": "1.3.0", "source": {"type": "git", "url": "https://github.com/cycle/schema-renderer.git", "reference": "75afcb552432eb58dffda15d63f4451601c60c82"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/cycle/schema-renderer/zipball/75afcb552432eb58dffda15d63f4451601c60c82", "reference": "75afcb552432eb58dffda15d63f4451601c60c82", "shasum": ""}, "require": {"cycle/orm": "^2.0", "php": ">=8.1", "symfony/polyfill-php83": "^1.31.0"}, "require-dev": {"phpunit/phpunit": "^10.5", "spiral/code-style": "^2.2.2", "spiral/dumper": "^3.3", "vimeo/psalm": "^5.26.1 || ^6.8.9"}, "type": "library", "autoload": {"psr-4": {"Cycle\\Schema\\Renderer\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Utils for Cycle ORM Schema rendering", "support": {"issues": "https://github.com/cycle/schema-renderer/issues", "source": "https://github.com/cycle/schema-renderer/tree/1.3.0"}, "time": "2025-05-08T08:51:06+00:00"}, {"name": "defuse/php-encryption", "version": "v2.4.0", "source": {"type": "git", "url": "https://github.com/defuse/php-encryption.git", "reference": "f53396c2d34225064647a05ca76c1da9d99e5828"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/defuse/php-encryption/zipball/f53396c2d34225064647a05ca76c1da9d99e5828", "reference": "f53396c2d34225064647a05ca76c1da9d99e5828", "shasum": ""}, "require": {"ext-openssl": "*", "paragonie/random_compat": ">= 2", "php": ">=5.6.0"}, "require-dev": {"phpunit/phpunit": "^5|^6|^7|^8|^9|^10", "yoast/phpunit-polyfills": "^2.0.0"}, "bin": ["bin/generate-defuse-key"], "type": "library", "autoload": {"psr-4": {"Defuse\\Crypto\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://defuse.ca/"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://paragonie.com"}], "description": "Secure PHP Encryption Library", "keywords": ["aes", "authenticated encryption", "cipher", "crypto", "cryptography", "encrypt", "encryption", "openssl", "security", "symmetric key cryptography"], "support": {"issues": "https://github.com/defuse/php-encryption/issues", "source": "https://github.com/defuse/php-encryption/tree/v2.4.0"}, "time": "2023-06-19T06:10:36+00:00"}, {"name": "doctrine/collections", "version": "2.3.0", "source": {"type": "git", "url": "https://github.com/doctrine/collections.git", "reference": "2eb07e5953eed811ce1b309a7478a3b236f2273d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/collections/zipball/2eb07e5953eed811ce1b309a7478a3b236f2273d", "reference": "2eb07e5953eed811ce1b309a7478a3b236f2273d", "shasum": ""}, "require": {"doctrine/deprecations": "^1", "php": "^8.1", "symfony/polyfill-php84": "^1.30"}, "require-dev": {"doctrine/coding-standard": "^12", "ext-json": "*", "phpstan/phpstan": "^1.8", "phpstan/phpstan-phpunit": "^1.0", "phpunit/phpunit": "^10.5"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Common\\Collections\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "PHP Doctrine Collections library that adds additional functionality on top of PHP arrays.", "homepage": "https://www.doctrine-project.org/projects/collections.html", "keywords": ["array", "collections", "iterators", "php"], "support": {"issues": "https://github.com/doctrine/collections/issues", "source": "https://github.com/doctrine/collections/tree/2.3.0"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Fcollections", "type": "tidelift"}], "time": "2025-03-22T10:17:19+00:00"}, {"name": "doctrine/deprecations", "version": "1.1.5", "source": {"type": "git", "url": "https://github.com/doctrine/deprecations.git", "reference": "459c2f5dd3d6a4633d3b5f46ee2b1c40f57d3f38"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/deprecations/zipball/459c2f5dd3d6a4633d3b5f46ee2b1c40f57d3f38", "reference": "459c2f5dd3d6a4633d3b5f46ee2b1c40f57d3f38", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "conflict": {"phpunit/phpunit": "<=7.5 || >=13"}, "require-dev": {"doctrine/coding-standard": "^9 || ^12 || ^13", "phpstan/phpstan": "1.4.10 || 2.1.11", "phpstan/phpstan-phpunit": "^1.0 || ^2", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.6 || ^10.5 || ^11.5 || ^12", "psr/log": "^1 || ^2 || ^3"}, "suggest": {"psr/log": "Allows logging deprecations via PSR-3 logger implementation"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Deprecations\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "A small layer on top of trigger_error(E_USER_DEPRECATED) or PSR-3 logging with options to disable all deprecations or selectively for packages.", "homepage": "https://www.doctrine-project.org/", "support": {"issues": "https://github.com/doctrine/deprecations/issues", "source": "https://github.com/doctrine/deprecations/tree/1.1.5"}, "time": "2025-04-07T20:06:18+00:00"}, {"name": "doctrine/inflector", "version": "2.1.0", "source": {"type": "git", "url": "https://github.com/doctrine/inflector.git", "reference": "6d6c96277ea252fc1304627204c3d5e6e15faa3b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/inflector/zipball/6d6c96277ea252fc1304627204c3d5e6e15faa3b", "reference": "6d6c96277ea252fc1304627204c3d5e6e15faa3b", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "require-dev": {"doctrine/coding-standard": "^12.0 || ^13.0", "phpstan/phpstan": "^1.12 || ^2.0", "phpstan/phpstan-phpunit": "^1.4 || ^2.0", "phpstan/phpstan-strict-rules": "^1.6 || ^2.0", "phpunit/phpunit": "^8.5 || ^12.2"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Inflector\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "PHP Doctrine Inflector is a small library that can perform string manipulations with regard to upper/lowercase and singular/plural forms of words.", "homepage": "https://www.doctrine-project.org/projects/inflector.html", "keywords": ["inflection", "inflector", "lowercase", "manipulation", "php", "plural", "singular", "strings", "uppercase", "words"], "support": {"issues": "https://github.com/doctrine/inflector/issues", "source": "https://github.com/doctrine/inflector/tree/2.1.0"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Finflector", "type": "tidelift"}], "time": "2025-08-10T19:31:58+00:00"}, {"name": "doctrine/instantiator", "version": "2.0.0", "source": {"type": "git", "url": "https://github.com/doctrine/instantiator.git", "reference": "c6222283fa3f4ac679f8b9ced9a4e23f163e80d0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/instantiator/zipball/c6222283fa3f4ac679f8b9ced9a4e23f163e80d0", "reference": "c6222283fa3f4ac679f8b9ced9a4e23f163e80d0", "shasum": ""}, "require": {"php": "^8.1"}, "require-dev": {"doctrine/coding-standard": "^11", "ext-pdo": "*", "ext-phar": "*", "phpbench/phpbench": "^1.2", "phpstan/phpstan": "^1.9.4", "phpstan/phpstan-phpunit": "^1.3", "phpunit/phpunit": "^9.5.27", "vimeo/psalm": "^5.4"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Instantiator\\": "src/Doctrine/Instantiator/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://ocramius.github.io/"}], "description": "A small, lightweight utility to instantiate objects in PHP without invoking their constructors", "homepage": "https://www.doctrine-project.org/projects/instantiator.html", "keywords": ["constructor", "instantiate"], "support": {"issues": "https://github.com/doctrine/instantiator/issues", "source": "https://github.com/doctrine/instantiator/tree/2.0.0"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Finstantiator", "type": "tidelift"}], "time": "2022-12-30T00:23:10+00:00"}, {"name": "doctrine/lexer", "version": "3.0.1", "source": {"type": "git", "url": "https://github.com/doctrine/lexer.git", "reference": "31ad66abc0fc9e1a1f2d9bc6a42668d2fbbcd6dd"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/lexer/zipball/31ad66abc0fc9e1a1f2d9bc6a42668d2fbbcd6dd", "reference": "31ad66abc0fc9e1a1f2d9bc6a42668d2fbbcd6dd", "shasum": ""}, "require": {"php": "^8.1"}, "require-dev": {"doctrine/coding-standard": "^12", "phpstan/phpstan": "^1.10", "phpunit/phpunit": "^10.5", "psalm/plugin-phpunit": "^0.18.3", "vimeo/psalm": "^5.21"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Common\\Lexer\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "PHP Doctrine Lexer parser library that can be used in Top-Down, Recursive Descent Parsers.", "homepage": "https://www.doctrine-project.org/projects/lexer.html", "keywords": ["annotations", "doc<PERSON>", "lexer", "parser", "php"], "support": {"issues": "https://github.com/doctrine/lexer/issues", "source": "https://github.com/doctrine/lexer/tree/3.0.1"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Flexer", "type": "tidelift"}], "time": "2024-02-05T11:56:58+00:00"}, {"name": "dragonmantank/cron-expression", "version": "v3.4.0", "source": {"type": "git", "url": "https://github.com/dragonmantank/cron-expression.git", "reference": "8c784d071debd117328803d86b2097615b457500"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/dragonmantank/cron-expression/zipball/8c784d071debd117328803d86b2097615b457500", "reference": "8c784d071debd117328803d86b2097615b457500", "shasum": ""}, "require": {"php": "^7.2|^8.0", "webmozart/assert": "^1.0"}, "replace": {"mtdowling/cron-expression": "^1.0"}, "require-dev": {"phpstan/extension-installer": "^1.0", "phpstan/phpstan": "^1.0", "phpunit/phpunit": "^7.0|^8.0|^9.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.x-dev"}}, "autoload": {"psr-4": {"Cron\\": "src/Cron/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/dragonmantank"}], "description": "CRON for PHP: Calculate the next or previous run date and determine if a CRON expression is due", "keywords": ["cron", "schedule"], "support": {"issues": "https://github.com/dragonmantank/cron-expression/issues", "source": "https://github.com/dragonmantank/cron-expression/tree/v3.4.0"}, "funding": [{"url": "https://github.com/dragonmantank", "type": "github"}], "time": "2024-10-09T13:47:03+00:00"}, {"name": "egulias/email-validator", "version": "4.0.4", "source": {"type": "git", "url": "https://github.com/egulias/EmailValidator.git", "reference": "d42c8731f0624ad6bdc8d3e5e9a4524f68801cfa"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/egulias/EmailValidator/zipball/d42c8731f0624ad6bdc8d3e5e9a4524f68801cfa", "reference": "d42c8731f0624ad6bdc8d3e5e9a4524f68801cfa", "shasum": ""}, "require": {"doctrine/lexer": "^2.0 || ^3.0", "php": ">=8.1", "symfony/polyfill-intl-idn": "^1.26"}, "require-dev": {"phpunit/phpunit": "^10.2", "vimeo/psalm": "^5.12"}, "suggest": {"ext-intl": "PHP Internationalization Libraries are required to use the SpoofChecking validation"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.0.x-dev"}}, "autoload": {"psr-4": {"Egulias\\EmailValidator\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>"}], "description": "A library for validating emails against several RFCs", "homepage": "https://github.com/egulias/EmailValidator", "keywords": ["email", "emailvalidation", "emailvalidator", "validation", "validator"], "support": {"issues": "https://github.com/egulias/EmailValidator/issues", "source": "https://github.com/egulias/EmailValidator/tree/4.0.4"}, "funding": [{"url": "https://github.com/egulias", "type": "github"}], "time": "2025-03-06T22:45:56+00:00"}, {"name": "firebase/php-jwt", "version": "v6.11.1", "source": {"type": "git", "url": "https://github.com/firebase/php-jwt.git", "reference": "d1e91ecf8c598d073d0995afa8cd5c75c6e19e66"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/firebase/php-jwt/zipball/d1e91ecf8c598d073d0995afa8cd5c75c6e19e66", "reference": "d1e91ecf8c598d073d0995afa8cd5c75c6e19e66", "shasum": ""}, "require": {"php": "^8.0"}, "require-dev": {"guzzlehttp/guzzle": "^7.4", "phpspec/prophecy-phpunit": "^2.0", "phpunit/phpunit": "^9.5", "psr/cache": "^2.0||^3.0", "psr/http-client": "^1.0", "psr/http-factory": "^1.0"}, "suggest": {"ext-sodium": "Support EdDSA (Ed25519) signatures", "paragonie/sodium_compat": "Support EdDSA (Ed25519) signatures when libsodium is not present"}, "type": "library", "autoload": {"psr-4": {"Firebase\\JWT\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "A simple library to encode and decode JSON Web Tokens (JWT) in PHP. Should conform to the current spec.", "homepage": "https://github.com/firebase/php-jwt", "keywords": ["jwt", "php"], "support": {"issues": "https://github.com/firebase/php-jwt/issues", "source": "https://github.com/firebase/php-jwt/tree/v6.11.1"}, "time": "2025-04-09T20:32:01+00:00"}, {"name": "google/common-protos", "version": "4.12.3", "source": {"type": "git", "url": "https://github.com/googleapis/common-protos-php.git", "reference": "64f73256492585461ab291aee90d07175d0c79b9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/googleapis/common-protos-php/zipball/64f73256492585461ab291aee90d07175d0c79b9", "reference": "64f73256492585461ab291aee90d07175d0c79b9", "shasum": ""}, "require": {"google/protobuf": "^4.26.1", "php": "^8.1"}, "require-dev": {"phpunit/phpunit": "^9.6"}, "type": "library", "extra": {"component": {"id": "common-protos", "path": "CommonProtos", "entry": "README.md", "target": "googleapis/common-protos-php.git"}}, "autoload": {"psr-4": {"Google\\Api\\": "src/Api", "Google\\Iam\\": "src/Iam", "Google\\Rpc\\": "src/Rpc", "Google\\Type\\": "src/Type", "Google\\Cloud\\": "src/Cloud", "GPBMetadata\\Google\\Api\\": "metadata/Api", "GPBMetadata\\Google\\Iam\\": "metadata/Iam", "GPBMetadata\\Google\\Rpc\\": "metadata/Rpc", "GPBMetadata\\Google\\Type\\": "metadata/Type", "GPBMetadata\\Google\\Cloud\\": "metadata/Cloud", "GPBMetadata\\Google\\Logging\\": "metadata/Logging"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "description": "Google API Common Protos for PHP", "homepage": "https://github.com/googleapis/common-protos-php", "keywords": ["google"], "support": {"source": "https://github.com/googleapis/common-protos-php/tree/v4.12.3"}, "time": "2025-08-18T18:45:43+00:00"}, {"name": "google/protobuf", "version": "v4.32.0", "source": {"type": "git", "url": "https://github.com/protocolbuffers/protobuf-php.git", "reference": "9a9a92ecbe9c671dc1863f6d4a91ea3ea12c8646"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/protocolbuffers/protobuf-php/zipball/9a9a92ecbe9c671dc1863f6d4a91ea3ea12c8646", "reference": "9a9a92ecbe9c671dc1863f6d4a91ea3ea12c8646", "shasum": ""}, "require": {"php": ">=8.1.0"}, "provide": {"ext-protobuf": "*"}, "require-dev": {"phpunit/phpunit": ">=5.0.0 <8.5.27"}, "suggest": {"ext-bcmath": "Need to support JSON deserialization"}, "type": "library", "autoload": {"psr-4": {"Google\\Protobuf\\": "src/Google/Protobuf", "GPBMetadata\\Google\\Protobuf\\": "src/GPBMetadata/Google/Protobuf"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "description": "proto library for PHP", "homepage": "https://developers.google.com/protocol-buffers/", "keywords": ["proto"], "support": {"source": "https://github.com/protocolbuffers/protobuf-php/tree/v4.32.0"}, "time": "2025-08-14T20:00:33+00:00"}, {"name": "graham-campbell/result-type", "version": "v1.1.3", "source": {"type": "git", "url": "https://github.com/GrahamCampbell/Result-Type.git", "reference": "3ba905c11371512af9d9bdd27d99b782216b6945"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/GrahamCampbell/Result-Type/zipball/3ba905c11371512af9d9bdd27d99b782216b6945", "reference": "3ba905c11371512af9d9bdd27d99b782216b6945", "shasum": ""}, "require": {"php": "^7.2.5 || ^8.0", "phpoption/phpoption": "^1.9.3"}, "require-dev": {"phpunit/phpunit": "^8.5.39 || ^9.6.20 || ^10.5.28"}, "type": "library", "autoload": {"psr-4": {"GrahamCampbell\\ResultType\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}], "description": "An Implementation Of The Result Type", "keywords": ["<PERSON>", "Graham<PERSON><PERSON><PERSON>", "Result Type", "Result-Type", "result"], "support": {"issues": "https://github.com/GrahamCampbell/Result-Type/issues", "source": "https://github.com/GrahamCampbell/Result-Type/tree/v1.1.3"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/graham-campbell/result-type", "type": "tidelift"}], "time": "2024-07-20T21:45:45+00:00"}, {"name": "grpc/grpc", "version": "1.74.0", "source": {"type": "git", "url": "https://github.com/grpc/grpc-php.git", "reference": "32bf4dba256d60d395582fb6e4e8d3936bcdb713"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/grpc/grpc-php/zipball/32bf4dba256d60d395582fb6e4e8d3936bcdb713", "reference": "32bf4dba256d60d395582fb6e4e8d3936bcdb713", "shasum": ""}, "require": {"php": ">=7.0.0"}, "require-dev": {"google/auth": "^v1.3.0"}, "suggest": {"ext-protobuf": "For better performance, install the protobuf C extension.", "google/protobuf": "To get started using grpc quickly, install the native protobuf library."}, "type": "library", "autoload": {"psr-4": {"Grpc\\": "src/lib/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "description": "gRPC library for PHP", "homepage": "https://grpc.io", "keywords": ["rpc"], "support": {"source": "https://github.com/grpc/grpc-php/tree/v1.74.0"}, "time": "2025-07-24T20:02:16+00:00"}, {"name": "league/event", "version": "3.0.3", "source": {"type": "git", "url": "https://github.com/thephpleague/event.git", "reference": "ec38ff7ea10cad7d99a79ac937fbcffb9334c210"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/thephpleague/event/zipball/ec38ff7ea10cad7d99a79ac937fbcffb9334c210", "reference": "ec38ff7ea10cad7d99a79ac937fbcffb9334c210", "shasum": ""}, "require": {"php": ">=7.2.0", "psr/event-dispatcher": "^1.0"}, "provide": {"psr/event-dispatcher-implementation": "1.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "^2.16", "phpstan/phpstan": "^0.12.45", "phpunit/phpunit": "^8.5"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0-dev"}}, "autoload": {"psr-4": {"League\\Event\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Event package", "keywords": ["emitter", "event", "listener"], "support": {"issues": "https://github.com/thephpleague/event/issues", "source": "https://github.com/thephpleague/event/tree/3.0.3"}, "time": "2024-09-04T16:06:53+00:00"}, {"name": "league/flysystem", "version": "3.30.0", "source": {"type": "git", "url": "https://github.com/thephpleague/flysystem.git", "reference": "2203e3151755d874bb2943649dae1eb8533ac93e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/thephpleague/flysystem/zipball/2203e3151755d874bb2943649dae1eb8533ac93e", "reference": "2203e3151755d874bb2943649dae1eb8533ac93e", "shasum": ""}, "require": {"league/flysystem-local": "^3.0.0", "league/mime-type-detection": "^1.0.0", "php": "^8.0.2"}, "conflict": {"async-aws/core": "<1.19.0", "async-aws/s3": "<1.14.0", "aws/aws-sdk-php": "3.209.31 || 3.210.0", "guzzlehttp/guzzle": "<7.0", "guzzlehttp/ringphp": "<1.1.1", "phpseclib/phpseclib": "3.0.15", "symfony/http-client": "<5.2"}, "require-dev": {"async-aws/s3": "^1.5 || ^2.0", "async-aws/simple-s3": "^1.1 || ^2.0", "aws/aws-sdk-php": "^3.295.10", "composer/semver": "^3.0", "ext-fileinfo": "*", "ext-ftp": "*", "ext-mongodb": "^1.3|^2", "ext-zip": "*", "friendsofphp/php-cs-fixer": "^3.5", "google/cloud-storage": "^1.23", "guzzlehttp/psr7": "^2.6", "microsoft/azure-storage-blob": "^1.1", "mongodb/mongodb": "^1.2|^2", "phpseclib/phpseclib": "^3.0.36", "phpstan/phpstan": "^1.10", "phpunit/phpunit": "^9.5.11|^10.0", "sabre/dav": "^4.6.0"}, "type": "library", "autoload": {"psr-4": {"League\\Flysystem\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "File storage abstraction for PHP", "keywords": ["WebDAV", "aws", "cloud", "file", "files", "filesystem", "filesystems", "ftp", "s3", "sftp", "storage"], "support": {"issues": "https://github.com/thephpleague/flysystem/issues", "source": "https://github.com/thephpleague/flysystem/tree/3.30.0"}, "time": "2025-06-25T13:29:59+00:00"}, {"name": "league/flysystem-local", "version": "3.30.0", "source": {"type": "git", "url": "https://github.com/thephpleague/flysystem-local.git", "reference": "6691915f77c7fb69adfb87dcd550052dc184ee10"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/thephpleague/flysystem-local/zipball/6691915f77c7fb69adfb87dcd550052dc184ee10", "reference": "6691915f77c7fb69adfb87dcd550052dc184ee10", "shasum": ""}, "require": {"ext-fileinfo": "*", "league/flysystem": "^3.0.0", "league/mime-type-detection": "^1.0.0", "php": "^8.0.2"}, "type": "library", "autoload": {"psr-4": {"League\\Flysystem\\Local\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Local filesystem adapter for Flysystem.", "keywords": ["Flysystem", "file", "files", "filesystem", "local"], "support": {"source": "https://github.com/thephpleague/flysystem-local/tree/3.30.0"}, "time": "2025-05-21T10:34:19+00:00"}, {"name": "league/mime-type-detection", "version": "1.16.0", "source": {"type": "git", "url": "https://github.com/thephpleague/mime-type-detection.git", "reference": "2d6702ff215bf922936ccc1ad31007edc76451b9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/thephpleague/mime-type-detection/zipball/2d6702ff215bf922936ccc1ad31007edc76451b9", "reference": "2d6702ff215bf922936ccc1ad31007edc76451b9", "shasum": ""}, "require": {"ext-fileinfo": "*", "php": "^7.4 || ^8.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "^3.2", "phpstan/phpstan": "^0.12.68", "phpunit/phpunit": "^8.5.8 || ^9.3 || ^10.0"}, "type": "library", "autoload": {"psr-4": {"League\\MimeTypeDetection\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Mime-type detection for Flysystem", "support": {"issues": "https://github.com/thephpleague/mime-type-detection/issues", "source": "https://github.com/thephpleague/mime-type-detection/tree/1.16.0"}, "funding": [{"url": "https://github.com/frankdejonge", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/league/flysystem", "type": "tidelift"}], "time": "2024-09-21T08:32:55+00:00"}, {"name": "monolog/monolog", "version": "3.9.0", "source": {"type": "git", "url": "https://github.com/Seldaek/monolog.git", "reference": "10d85740180ecba7896c87e06a166e0c95a0e3b6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Seldaek/monolog/zipball/10d85740180ecba7896c87e06a166e0c95a0e3b6", "reference": "10d85740180ecba7896c87e06a166e0c95a0e3b6", "shasum": ""}, "require": {"php": ">=8.1", "psr/log": "^2.0 || ^3.0"}, "provide": {"psr/log-implementation": "3.0.0"}, "require-dev": {"aws/aws-sdk-php": "^3.0", "doctrine/couchdb": "~1.0@dev", "elasticsearch/elasticsearch": "^7 || ^8", "ext-json": "*", "graylog2/gelf-php": "^1.4.2 || ^2.0", "guzzlehttp/guzzle": "^7.4.5", "guzzlehttp/psr7": "^2.2", "mongodb/mongodb": "^1.8", "php-amqplib/php-amqplib": "~2.4 || ^3", "php-console/php-console": "^3.1.8", "phpstan/phpstan": "^2", "phpstan/phpstan-deprecation-rules": "^2", "phpstan/phpstan-strict-rules": "^2", "phpunit/phpunit": "^10.5.17 || ^11.0.7", "predis/predis": "^1.1 || ^2", "rollbar/rollbar": "^4.0", "ruflin/elastica": "^7 || ^8", "symfony/mailer": "^5.4 || ^6", "symfony/mime": "^5.4 || ^6"}, "suggest": {"aws/aws-sdk-php": "Allow sending log messages to AWS services like DynamoDB", "doctrine/couchdb": "Allow sending log messages to a CouchDB server", "elasticsearch/elasticsearch": "Allow sending log messages to an Elasticsearch server via official client", "ext-amqp": "Allow sending log messages to an AMQP server (1.0+ required)", "ext-curl": "Required to send log messages using the IFTTTHandler, the LogglyHandler, the SendGridHandler, the SlackWebhookHandler or the TelegramBotHandler", "ext-mbstring": "Allow to work properly with unicode symbols", "ext-mongodb": "Allow sending log messages to a MongoDB server (via driver)", "ext-openssl": "Required to send log messages using SSL", "ext-sockets": "Allow sending log messages to a Syslog server (via UDP driver)", "graylog2/gelf-php": "Allow sending log messages to a GrayLog2 server", "mongodb/mongodb": "Allow sending log messages to a MongoDB server (via library)", "php-amqplib/php-amqplib": "Allow sending log messages to an AMQP server using php-amqplib", "rollbar/rollbar": "Allow sending log messages to Rollbar", "ruflin/elastica": "Allow sending log messages to an Elastic Search server"}, "type": "library", "extra": {"branch-alias": {"dev-main": "3.x-dev"}}, "autoload": {"psr-4": {"Monolog\\": "src/Monolog"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "https://seld.be"}], "description": "Sends your logs to files, sockets, inboxes, databases and various web services", "homepage": "https://github.com/Seldaek/monolog", "keywords": ["log", "logging", "psr-3"], "support": {"issues": "https://github.com/Seldaek/monolog/issues", "source": "https://github.com/Seldaek/monolog/tree/3.9.0"}, "funding": [{"url": "https://github.com/Seldaek", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/monolog/monolog", "type": "tidelift"}], "time": "2025-03-24T10:02:05+00:00"}, {"name": "myclabs/deep-copy", "version": "1.13.4", "source": {"type": "git", "url": "https://github.com/myclabs/DeepCopy.git", "reference": "07d290f0c47959fd5eed98c95ee5602db07e0b6a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/myclabs/DeepCopy/zipball/07d290f0c47959fd5eed98c95ee5602db07e0b6a", "reference": "07d290f0c47959fd5eed98c95ee5602db07e0b6a", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "conflict": {"doctrine/collections": "<1.6.8", "doctrine/common": "<2.13.3 || >=3 <3.2.2"}, "require-dev": {"doctrine/collections": "^1.6.8", "doctrine/common": "^2.13.3 || ^3.2.2", "phpspec/prophecy": "^1.10", "phpunit/phpunit": "^7.5.20 || ^8.5.23 || ^9.5.13"}, "type": "library", "autoload": {"files": ["src/DeepCopy/deep_copy.php"], "psr-4": {"DeepCopy\\": "src/DeepCopy/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Create deep copies (clones) of your objects", "keywords": ["clone", "copy", "duplicate", "object", "object graph"], "support": {"issues": "https://github.com/myclabs/DeepCopy/issues", "source": "https://github.com/myclabs/DeepCopy/tree/1.13.4"}, "funding": [{"url": "https://tidelift.com/funding/github/packagist/myclabs/deep-copy", "type": "tidelift"}], "time": "2025-08-01T08:46:24+00:00"}, {"name": "nesbot/carbon", "version": "3.10.3", "source": {"type": "git", "url": "https://github.com/CarbonPHP/carbon.git", "reference": "8e3643dcd149ae0fe1d2ff4f2c8e4bbfad7c165f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/8e3643dcd149ae0fe1d2ff4f2c8e4bbfad7c165f", "reference": "8e3643dcd149ae0fe1d2ff4f2c8e4bbfad7c165f", "shasum": ""}, "require": {"carbonphp/carbon-doctrine-types": "<100.0", "ext-json": "*", "php": "^8.1", "psr/clock": "^1.0", "symfony/clock": "^6.3.12 || ^7.0", "symfony/polyfill-mbstring": "^1.0", "symfony/translation": "^4.4.18 || ^5.2.1 || ^6.0 || ^7.0"}, "provide": {"psr/clock-implementation": "1.0"}, "require-dev": {"doctrine/dbal": "^3.6.3 || ^4.0", "doctrine/orm": "^2.15.2 || ^3.0", "friendsofphp/php-cs-fixer": "^v3.87.1", "kylekatarnls/multi-tester": "^2.5.3", "phpmd/phpmd": "^2.15.0", "phpstan/extension-installer": "^1.4.3", "phpstan/phpstan": "^2.1.22", "phpunit/phpunit": "^10.5.53", "squizlabs/php_codesniffer": "^3.13.4"}, "bin": ["bin/carbon"], "type": "library", "extra": {"laravel": {"providers": ["Carbon\\Laravel\\ServiceProvider"]}, "phpstan": {"includes": ["extension.neon"]}, "branch-alias": {"dev-2.x": "2.x-dev", "dev-master": "3.x-dev"}}, "autoload": {"psr-4": {"Carbon\\": "src/Carbon/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://markido.com"}, {"name": "kylekatarnls", "homepage": "https://github.com/kylekatarnls"}], "description": "An API extension for DateTime that supports 281 different languages.", "homepage": "https://carbon.nesbot.com", "keywords": ["date", "datetime", "time"], "support": {"docs": "https://carbon.nesbot.com/docs", "issues": "https://github.com/CarbonPHP/carbon/issues", "source": "https://github.com/CarbonPHP/carbon"}, "funding": [{"url": "https://github.com/sponsors/kylekatarnls", "type": "github"}, {"url": "https://opencollective.com/Carbon#sponsor", "type": "opencollective"}, {"url": "https://tidelift.com/subscription/pkg/packagist-nesbot-carbon?utm_source=packagist-nesbot-carbon&utm_medium=referral&utm_campaign=readme", "type": "tidelift"}], "time": "2025-09-06T13:39:36+00:00"}, {"name": "nette/php-generator", "version": "v4.2.0", "source": {"type": "git", "url": "https://github.com/nette/php-generator.git", "reference": "4707546a1f11badd72f5d82af4f8a6bc64bd56ac"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/php-generator/zipball/4707546a1f11badd72f5d82af4f8a6bc64bd56ac", "reference": "4707546a1f11badd72f5d82af4f8a6bc64bd56ac", "shasum": ""}, "require": {"nette/utils": "^4.0.6", "php": "8.1 - 8.5"}, "require-dev": {"jetbrains/phpstorm-attributes": "^1.2", "nette/tester": "^2.4", "nikic/php-parser": "^5.0", "phpstan/phpstan-nette": "^2.0@stable", "tracy/tracy": "^2.8"}, "suggest": {"nikic/php-parser": "to use ClassType::from(withBodies: true) & ClassType::fromCode()"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.2-dev"}}, "autoload": {"psr-4": {"Nette\\": "src"}, "classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0-only", "GPL-3.0-only"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "🐘 Nette PHP Generator: generates neat PHP code for you. Supports new PHP 8.5 features.", "homepage": "https://nette.org", "keywords": ["code", "nette", "php", "scaffolding"], "support": {"issues": "https://github.com/nette/php-generator/issues", "source": "https://github.com/nette/php-generator/tree/v4.2.0"}, "time": "2025-08-06T18:24:31+00:00"}, {"name": "nette/utils", "version": "v4.0.8", "source": {"type": "git", "url": "https://github.com/nette/utils.git", "reference": "c930ca4e3cf4f17dcfb03037703679d2396d2ede"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/utils/zipball/c930ca4e3cf4f17dcfb03037703679d2396d2ede", "reference": "c930ca4e3cf4f17dcfb03037703679d2396d2ede", "shasum": ""}, "require": {"php": "8.0 - 8.5"}, "conflict": {"nette/finder": "<3", "nette/schema": "<1.2.2"}, "require-dev": {"jetbrains/phpstorm-attributes": "^1.2", "nette/tester": "^2.5", "phpstan/phpstan-nette": "^2.0@stable", "tracy/tracy": "^2.9"}, "suggest": {"ext-gd": "to use Image", "ext-iconv": "to use Strings::webalize(), to<PERSON>cii(), chr() and reverse()", "ext-intl": "to use Strings::webalize(), toAscii(), normalize() and compare()", "ext-json": "to use Nette\\Utils\\Json", "ext-mbstring": "to use Strings::lower() etc...", "ext-tokenizer": "to use Nette\\Utils\\Reflection::getUseStatements()"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.0-dev"}}, "autoload": {"psr-4": {"Nette\\": "src"}, "classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0-only", "GPL-3.0-only"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "🛠  Nette Utils: lightweight utilities for string & array manipulation, image handling, safe JSON encoding/decoding, validation, slug or strong password generating etc.", "homepage": "https://nette.org", "keywords": ["array", "core", "datetime", "images", "json", "nette", "paginator", "password", "slugify", "string", "unicode", "utf-8", "utility", "validation"], "support": {"issues": "https://github.com/nette/utils/issues", "source": "https://github.com/nette/utils/tree/v4.0.8"}, "time": "2025-08-06T21:43:34+00:00"}, {"name": "nikic/php-parser", "version": "v5.6.1", "source": {"type": "git", "url": "https://github.com/nikic/PHP-Parser.git", "reference": "f103601b29efebd7ff4a1ca7b3eeea9e3336a2a2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nikic/PHP-Parser/zipball/f103601b29efebd7ff4a1ca7b3eeea9e3336a2a2", "reference": "f103601b29efebd7ff4a1ca7b3eeea9e3336a2a2", "shasum": ""}, "require": {"ext-ctype": "*", "ext-json": "*", "ext-tokenizer": "*", "php": ">=7.4"}, "require-dev": {"ircmaxell/php-yacc": "^0.0.7", "phpunit/phpunit": "^9.0"}, "bin": ["bin/php-parse"], "type": "library", "extra": {"branch-alias": {"dev-master": "5.x-dev"}}, "autoload": {"psr-4": {"PhpParser\\": "lib/Php<PERSON><PERSON>er"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>"}], "description": "A PHP parser written in PHP", "keywords": ["parser", "php"], "support": {"issues": "https://github.com/nikic/PHP-Parser/issues", "source": "https://github.com/nikic/PHP-Parser/tree/v5.6.1"}, "time": "2025-08-13T20:13:15+00:00"}, {"name": "nyholm/psr7", "version": "1.8.2", "source": {"type": "git", "url": "https://github.com/Nyholm/psr7.git", "reference": "a71f2b11690f4b24d099d6b16690a90ae14fc6f3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Nyholm/psr7/zipball/a71f2b11690f4b24d099d6b16690a90ae14fc6f3", "reference": "a71f2b11690f4b24d099d6b16690a90ae14fc6f3", "shasum": ""}, "require": {"php": ">=7.2", "psr/http-factory": "^1.0", "psr/http-message": "^1.1 || ^2.0"}, "provide": {"php-http/message-factory-implementation": "1.0", "psr/http-factory-implementation": "1.0", "psr/http-message-implementation": "1.0"}, "require-dev": {"http-interop/http-factory-tests": "^0.9", "php-http/message-factory": "^1.0", "php-http/psr7-integration-tests": "^1.0", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.4", "symfony/error-handler": "^4.4"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.8-dev"}}, "autoload": {"psr-4": {"Nyholm\\Psr7\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "marti<PERSON>@vanderven.se"}], "description": "A fast PHP7 implementation of PSR-7", "homepage": "https://tnyholm.se", "keywords": ["psr-17", "psr-7"], "support": {"issues": "https://github.com/Nyholm/psr7/issues", "source": "https://github.com/Nyholm/psr7/tree/1.8.2"}, "funding": [{"url": "https://github.com/Zegnat", "type": "github"}, {"url": "https://github.com/nyholm", "type": "github"}], "time": "2024-09-09T07:06:30+00:00"}, {"name": "paragonie/random_compat", "version": "v9.99.100", "source": {"type": "git", "url": "https://github.com/paragonie/random_compat.git", "reference": "996434e5492cb4c3edcb9168db6fbb1359ef965a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/paragonie/random_compat/zipball/996434e5492cb4c3edcb9168db6fbb1359ef965a", "reference": "996434e5492cb4c3edcb9168db6fbb1359ef965a", "shasum": ""}, "require": {"php": ">= 7"}, "require-dev": {"phpunit/phpunit": "4.*|5.*", "vimeo/psalm": "^1"}, "suggest": {"ext-libsodium": "Provides a modern crypto API that can be used to generate random bytes."}, "type": "library", "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Paragon Initiative Enterprises", "email": "<EMAIL>", "homepage": "https://paragonie.com"}], "description": "PHP 5.x polyfill for random_bytes() and random_int() from PHP 7", "keywords": ["csprng", "polyfill", "pseudorandom", "random"], "support": {"email": "<EMAIL>", "issues": "https://github.com/paragonie/random_compat/issues", "source": "https://github.com/paragonie/random_compat"}, "time": "2020-10-15T08:29:30+00:00"}, {"name": "phpoption/phpoption", "version": "1.9.4", "source": {"type": "git", "url": "https://github.com/schmittjoh/php-option.git", "reference": "638a154f8d4ee6a5cfa96d6a34dfbe0cffa9566d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/schmittjoh/php-option/zipball/638a154f8d4ee6a5cfa96d6a34dfbe0cffa9566d", "reference": "638a154f8d4ee6a5cfa96d6a34dfbe0cffa9566d", "shasum": ""}, "require": {"php": "^7.2.5 || ^8.0"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.8.2", "phpunit/phpunit": "^8.5.44 || ^9.6.25 || ^10.5.53 || ^11.5.34"}, "type": "library", "extra": {"bamarni-bin": {"bin-links": true, "forward-command": false}, "branch-alias": {"dev-master": "1.9-dev"}}, "autoload": {"psr-4": {"PhpOption\\": "src/PhpOption/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>", "homepage": "https://github.com/schmitt<PERSON>h"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}], "description": "Option Type for PHP", "keywords": ["language", "option", "php", "type"], "support": {"issues": "https://github.com/schmittjoh/php-option/issues", "source": "https://github.com/schmittjoh/php-option/tree/1.9.4"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/phpoption/phpoption", "type": "tidelift"}], "time": "2025-08-21T11:53:16+00:00"}, {"name": "psr/cache", "version": "3.0.0", "source": {"type": "git", "url": "https://github.com/php-fig/cache.git", "reference": "aa5030cfa5405eccfdcb1083ce040c2cb8d253bf"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/cache/zipball/aa5030cfa5405eccfdcb1083ce040c2cb8d253bf", "reference": "aa5030cfa5405eccfdcb1083ce040c2cb8d253bf", "shasum": ""}, "require": {"php": ">=8.0.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Cache\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for caching libraries", "keywords": ["cache", "psr", "psr-6"], "support": {"source": "https://github.com/php-fig/cache/tree/3.0.0"}, "time": "2021-02-03T23:26:27+00:00"}, {"name": "psr/clock", "version": "1.0.0", "source": {"type": "git", "url": "https://github.com/php-fig/clock.git", "reference": "e41a24703d4560fd0acb709162f73b8adfc3aa0d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/clock/zipball/e41a24703d4560fd0acb709162f73b8adfc3aa0d", "reference": "e41a24703d4560fd0acb709162f73b8adfc3aa0d", "shasum": ""}, "require": {"php": "^7.0 || ^8.0"}, "type": "library", "autoload": {"psr-4": {"Psr\\Clock\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for reading the clock.", "homepage": "https://github.com/php-fig/clock", "keywords": ["clock", "now", "psr", "psr-20", "time"], "support": {"issues": "https://github.com/php-fig/clock/issues", "source": "https://github.com/php-fig/clock/tree/1.0.0"}, "time": "2022-11-25T14:36:26+00:00"}, {"name": "psr/container", "version": "2.0.2", "source": {"type": "git", "url": "https://github.com/php-fig/container.git", "reference": "c71ecc56dfe541dbd90c5360474fbc405f8d5963"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/container/zipball/c71ecc56dfe541dbd90c5360474fbc405f8d5963", "reference": "c71ecc56dfe541dbd90c5360474fbc405f8d5963", "shasum": ""}, "require": {"php": ">=7.4.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Container\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common Container Interface (PHP FIG PSR-11)", "homepage": "https://github.com/php-fig/container", "keywords": ["PSR-11", "container", "container-interface", "container-interop", "psr"], "support": {"issues": "https://github.com/php-fig/container/issues", "source": "https://github.com/php-fig/container/tree/2.0.2"}, "time": "2021-11-05T16:47:00+00:00"}, {"name": "psr/event-dispatcher", "version": "1.0.0", "source": {"type": "git", "url": "https://github.com/php-fig/event-dispatcher.git", "reference": "dbefd12671e8a14ec7f180cab83036ed26714bb0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/event-dispatcher/zipball/dbefd12671e8a14ec7f180cab83036ed26714bb0", "reference": "dbefd12671e8a14ec7f180cab83036ed26714bb0", "shasum": ""}, "require": {"php": ">=7.2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\EventDispatcher\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Standard interfaces for event handling.", "keywords": ["events", "psr", "psr-14"], "support": {"issues": "https://github.com/php-fig/event-dispatcher/issues", "source": "https://github.com/php-fig/event-dispatcher/tree/1.0.0"}, "time": "2019-01-08T18:20:26+00:00"}, {"name": "psr/http-factory", "version": "1.1.0", "source": {"type": "git", "url": "https://github.com/php-fig/http-factory.git", "reference": "2b4765fddfe3b508ac62f829e852b1501d3f6e8a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-factory/zipball/2b4765fddfe3b508ac62f829e852b1501d3f6e8a", "reference": "2b4765fddfe3b508ac62f829e852b1501d3f6e8a", "shasum": ""}, "require": {"php": ">=7.1", "psr/http-message": "^1.0 || ^2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "PSR-17: Common interfaces for PSR-7 HTTP message factories", "keywords": ["factory", "http", "message", "psr", "psr-17", "psr-7", "request", "response"], "support": {"source": "https://github.com/php-fig/http-factory"}, "time": "2024-04-15T12:06:14+00:00"}, {"name": "psr/http-message", "version": "2.0", "source": {"type": "git", "url": "https://github.com/php-fig/http-message.git", "reference": "402d35bcb92c70c026d1a6a9883f06b2ead23d71"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-message/zipball/402d35bcb92c70c026d1a6a9883f06b2ead23d71", "reference": "402d35bcb92c70c026d1a6a9883f06b2ead23d71", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for HTTP messages", "homepage": "https://github.com/php-fig/http-message", "keywords": ["http", "http-message", "psr", "psr-7", "request", "response"], "support": {"source": "https://github.com/php-fig/http-message/tree/2.0"}, "time": "2023-04-04T09:54:51+00:00"}, {"name": "psr/http-server-handler", "version": "1.0.2", "source": {"type": "git", "url": "https://github.com/php-fig/http-server-handler.git", "reference": "84c4fb66179be4caaf8e97bd239203245302e7d4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-server-handler/zipball/84c4fb66179be4caaf8e97bd239203245302e7d4", "reference": "84c4fb66179be4caaf8e97bd239203245302e7d4", "shasum": ""}, "require": {"php": ">=7.0", "psr/http-message": "^1.0 || ^2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Server\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for HTTP server-side request handler", "keywords": ["handler", "http", "http-interop", "psr", "psr-15", "psr-7", "request", "response", "server"], "support": {"source": "https://github.com/php-fig/http-server-handler/tree/1.0.2"}, "time": "2023-04-10T20:06:20+00:00"}, {"name": "psr/http-server-middleware", "version": "1.0.2", "source": {"type": "git", "url": "https://github.com/php-fig/http-server-middleware.git", "reference": "c1481f747daaa6a0782775cd6a8c26a1bf4a3829"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-server-middleware/zipball/c1481f747daaa6a0782775cd6a8c26a1bf4a3829", "reference": "c1481f747daaa6a0782775cd6a8c26a1bf4a3829", "shasum": ""}, "require": {"php": ">=7.0", "psr/http-message": "^1.0 || ^2.0", "psr/http-server-handler": "^1.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Server\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for HTTP server-side middleware", "keywords": ["http", "http-interop", "middleware", "psr", "psr-15", "psr-7", "request", "response"], "support": {"issues": "https://github.com/php-fig/http-server-middleware/issues", "source": "https://github.com/php-fig/http-server-middleware/tree/1.0.2"}, "time": "2023-04-11T06:14:47+00:00"}, {"name": "psr/log", "version": "3.0.2", "source": {"type": "git", "url": "https://github.com/php-fig/log.git", "reference": "f16e1d5863e37f8d8c2a01719f5b34baa2b714d3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/log/zipball/f16e1d5863e37f8d8c2a01719f5b34baa2b714d3", "reference": "f16e1d5863e37f8d8c2a01719f5b34baa2b714d3", "shasum": ""}, "require": {"php": ">=8.0.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.x-dev"}}, "autoload": {"psr-4": {"Psr\\Log\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for logging libraries", "homepage": "https://github.com/php-fig/log", "keywords": ["log", "psr", "psr-3"], "support": {"source": "https://github.com/php-fig/log/tree/3.0.2"}, "time": "2024-09-11T13:17:53+00:00"}, {"name": "psr/simple-cache", "version": "3.0.0", "source": {"type": "git", "url": "https://github.com/php-fig/simple-cache.git", "reference": "764e0b3939f5ca87cb904f570ef9be2d78a07865"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/simple-cache/zipball/764e0b3939f5ca87cb904f570ef9be2d78a07865", "reference": "764e0b3939f5ca87cb904f570ef9be2d78a07865", "shasum": ""}, "require": {"php": ">=8.0.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\SimpleCache\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interfaces for simple caching", "keywords": ["cache", "caching", "psr", "psr-16", "simple-cache"], "support": {"source": "https://github.com/php-fig/simple-cache/tree/3.0.0"}, "time": "2021-10-29T13:26:27+00:00"}, {"name": "ramsey/collection", "version": "2.1.1", "source": {"type": "git", "url": "https://github.com/ramsey/collection.git", "reference": "344572933ad0181accbf4ba763e85a0306a8c5e2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ramsey/collection/zipball/344572933ad0181accbf4ba763e85a0306a8c5e2", "reference": "344572933ad0181accbf4ba763e85a0306a8c5e2", "shasum": ""}, "require": {"php": "^8.1"}, "require-dev": {"captainhook/plugin-composer": "^5.3", "ergebnis/composer-normalize": "^2.45", "fakerphp/faker": "^1.24", "hamcrest/hamcrest-php": "^2.0", "jangregor/phpstan-prophecy": "^2.1", "mockery/mockery": "^1.6", "php-parallel-lint/php-console-highlighter": "^1.0", "php-parallel-lint/php-parallel-lint": "^1.4", "phpspec/prophecy-phpunit": "^2.3", "phpstan/extension-installer": "^1.4", "phpstan/phpstan": "^2.1", "phpstan/phpstan-mockery": "^2.0", "phpstan/phpstan-phpunit": "^2.0", "phpunit/phpunit": "^10.5", "ramsey/coding-standard": "^2.3", "ramsey/conventional-commits": "^1.6", "roave/security-advisories": "dev-latest"}, "type": "library", "extra": {"captainhook": {"force-install": true}, "ramsey/conventional-commits": {"configFile": "conventional-commits.json"}}, "autoload": {"psr-4": {"Ramsey\\Collection\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://benramsey.com"}], "description": "A PHP library for representing and manipulating collections.", "keywords": ["array", "collection", "hash", "map", "queue", "set"], "support": {"issues": "https://github.com/ramsey/collection/issues", "source": "https://github.com/ramsey/collection/tree/2.1.1"}, "time": "2025-03-22T05:38:12+00:00"}, {"name": "ramsey/uuid", "version": "4.9.1", "source": {"type": "git", "url": "https://github.com/ramsey/uuid.git", "reference": "81f941f6f729b1e3ceea61d9d014f8b6c6800440"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ramsey/uuid/zipball/81f941f6f729b1e3ceea61d9d014f8b6c6800440", "reference": "81f941f6f729b1e3ceea61d9d014f8b6c6800440", "shasum": ""}, "require": {"brick/math": "^0.8.8 || ^0.9 || ^0.10 || ^0.11 || ^0.12 || ^0.13 || ^0.14", "php": "^8.0", "ramsey/collection": "^1.2 || ^2.0"}, "replace": {"rhumsaa/uuid": "self.version"}, "require-dev": {"captainhook/captainhook": "^5.25", "captainhook/plugin-composer": "^5.3", "dealerdirect/phpcodesniffer-composer-installer": "^1.0", "ergebnis/composer-normalize": "^2.47", "mockery/mockery": "^1.6", "paragonie/random-lib": "^2", "php-mock/php-mock": "^2.6", "php-mock/php-mock-mockery": "^1.5", "php-parallel-lint/php-parallel-lint": "^1.4.0", "phpbench/phpbench": "^1.2.14", "phpstan/extension-installer": "^1.4", "phpstan/phpstan": "^2.1", "phpstan/phpstan-mockery": "^2.0", "phpstan/phpstan-phpunit": "^2.0", "phpunit/phpunit": "^9.6", "slevomat/coding-standard": "^8.18", "squizlabs/php_codesniffer": "^3.13"}, "suggest": {"ext-bcmath": "Enables faster math with arbitrary-precision integers using BCMath.", "ext-gmp": "Enables faster math with arbitrary-precision integers using GMP.", "ext-uuid": "Enables the use of PeclUuidTimeGenerator and PeclUuidRandomGenerator.", "paragonie/random-lib": "Provides RandomLib for use with the RandomLibAdapter", "ramsey/uuid-doctrine": "Allows the use of Ramsey\\Uuid\\Uuid as Doctrine field type."}, "type": "library", "extra": {"captainhook": {"force-install": true}}, "autoload": {"files": ["src/functions.php"], "psr-4": {"Ramsey\\Uuid\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "A PHP library for generating and working with universally unique identifiers (UUIDs).", "keywords": ["guid", "identifier", "uuid"], "support": {"issues": "https://github.com/ramsey/uuid/issues", "source": "https://github.com/ramsey/uuid/tree/4.9.1"}, "time": "2025-09-04T20:59:21+00:00"}, {"name": "react/promise", "version": "v2.11.0", "source": {"type": "git", "url": "https://github.com/reactphp/promise.git", "reference": "1a8460931ea36dc5c76838fec5734d55c88c6831"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/reactphp/promise/zipball/1a8460931ea36dc5c76838fec5734d55c88c6831", "reference": "1a8460931ea36dc5c76838fec5734d55c88c6831", "shasum": ""}, "require": {"php": ">=5.4.0"}, "require-dev": {"phpunit/phpunit": "^9.6 || ^5.7 || ^4.8.36"}, "type": "library", "autoload": {"files": ["src/functions_include.php"], "psr-4": {"React\\Promise\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://sorgalla.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://clue.engineering/"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://wyrihaximus.net/"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://cboden.dev/"}], "description": "A lightweight implementation of CommonJS Promises/A for PHP", "keywords": ["promise", "promises"], "support": {"issues": "https://github.com/reactphp/promise/issues", "source": "https://github.com/reactphp/promise/tree/v2.11.0"}, "funding": [{"url": "https://opencollective.com/reactphp", "type": "open_collective"}], "time": "2023-11-16T16:16:50+00:00"}, {"name": "roadrunner-php/app-logger", "version": "1.2.0", "source": {"type": "git", "url": "https://github.com/roadrunner-php/app-logger.git", "reference": "555a31933c7797cfb5749a5c7176d39c2b368183"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/roadrunner-php/app-logger/zipball/555a31933c7797cfb5749a5c7176d39c2b368183", "reference": "555a31933c7797cfb5749a5c7176d39c2b368183", "shasum": ""}, "require": {"ext-json": "*", "php": ">=8.1", "roadrunner-php/roadrunner-api-dto": "^1.4", "spiral/goridge": "^3.1 || ^4.0"}, "require-dev": {"mockery/mockery": "^1.5", "phpunit/phpunit": "^10.0", "vimeo/psalm": ">=5.8"}, "type": "library", "autoload": {"psr-4": {"RoadRunner\\Logger\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON> (kastahov)", "email": "<EMAIL>"}, {"name": "RoadRunner Community", "homepage": "https://github.com/spiral/roadrunner/graphs/contributors"}], "description": "Send log messages to RoadRunner", "support": {"source": "https://github.com/roadrunner-php/app-logger/tree/1.2.0"}, "funding": [{"url": "https://github.com/roadrunner-server", "type": "github"}], "time": "2023-12-22T06:01:40+00:00"}, {"name": "roadrunner-php/centrifugo", "version": "2.2.1", "source": {"type": "git", "url": "https://github.com/roadrunner-php/centrifugo.git", "reference": "fb639ed0d8524ee5940c655f8c1b4a3d4d3a1b97"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/roadrunner-php/centrifugo/zipball/fb639ed0d8524ee5940c655f8c1b4a3d4d3a1b97", "reference": "fb639ed0d8524ee5940c655f8c1b4a3d4d3a1b97", "shasum": ""}, "require": {"ext-json": "*", "google/protobuf": "^3.7 || ^4.0", "php": ">=8.1", "roadrunner-php/roadrunner-api-dto": "^1.0", "spiral/goridge": "^4.0", "spiral/roadrunner": "^2023.1 || ^2024.1 || ^2025.1", "spiral/roadrunner-worker": "^3.0"}, "require-dev": {"mockery/mockery": "^1.5", "phpunit/phpunit": "^10.0", "vimeo/psalm": ">= 5.8"}, "type": "library", "autoload": {"psr-4": {"RoadRunner\\Centrifugo\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON> (wolfy-j)", "email": "<EMAIL>"}, {"name": "<PERSON> (but<PERSON><PERSON>)", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON> (roxblnfk)", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON> (msmakouz)", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON> (SerafimArts)", "email": "<EMAIL>"}, {"name": "RoadRunner Community", "homepage": "https://github.com/spiral/roadrunner/graphs/contributors"}], "description": "RoadRunner: Centrifugo bridge", "homepage": "https://roadrunner.dev/", "support": {"chat": "https://discord.gg/V6EK4he", "docs": "https://docs.roadrunner.dev", "forum": "https://forum.roadrunner.dev/", "issues": "https://github.com/roadrunner-server/roadrunner/issues", "source": "https://github.com/roadrunner-php/centrifugo/tree/2.2.1"}, "funding": [{"url": "https://github.com/sponsors/roadrunner-server", "type": "github"}], "time": "2025-06-23T08:24:54+00:00"}, {"name": "roadrunner-php/lock", "version": "1.0.0", "source": {"type": "git", "url": "https://github.com/roadrunner-php/lock.git", "reference": "bea3761b2cabce86cdca3b6013d024135fdf9138"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/roadrunner-php/lock/zipball/bea3761b2cabce86cdca3b6013d024135fdf9138", "reference": "bea3761b2cabce86cdca3b6013d024135fdf9138", "shasum": ""}, "require": {"php": "^8.1", "ramsey/uuid": "^4.7", "roadrunner-php/roadrunner-api-dto": "^1.0", "spiral/goridge": "^4.0"}, "require-dev": {"mockery/mockery": "^1.5", "phpunit/phpunit": "^10.0", "vimeo/psalm": "^5.9"}, "type": "library", "autoload": {"psr-4": {"RoadRunner\\Lock\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON> (wolfy-j)", "email": "<EMAIL>"}, {"name": "<PERSON> (but<PERSON><PERSON>)", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON> (roxblnfk)", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON> (msmakouz)", "email": "<EMAIL>"}, {"name": "RoadRunner Community", "homepage": "https://github.com/spiral/roadrunner/graphs/contributors"}], "description": "This package provides a PHP integration package for the RoadRunner Lock plugin, which allows for easy management of distributed locks in PHP applications. The plugin provides a fast, lightweight, and reliable way to acquire, release, and manage locks in a distributed environment, making it ideal for use in high-traffic web applications and microservices.", "homepage": "https://roadrunner.dev/", "keywords": ["lock", "roadrunner-php", "spiral"], "support": {"chat": "https://discord.gg/V6EK4he", "docs": "https://roadrunner.dev/docs", "forum": "https://forum.roadrunner.dev/", "issues": "https://github.com/roadrunner-server/roadrunner/issues", "source": "https://github.com/roadrunner-php/lock/tree/1.0.0"}, "funding": [{"url": "https://github.com/sponsors/roadrunner-server", "type": "github"}], "time": "2023-11-10T10:09:31+00:00"}, {"name": "roadrunner-php/roadrunner-api-dto", "version": "v1.13.0", "source": {"type": "git", "url": "https://github.com/roadrunner-php/roadrunner-api-dto.git", "reference": "8a683f5057005bef742916847c0befbf9a00c543"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/roadrunner-php/roadrunner-api-dto/zipball/8a683f5057005bef742916847c0befbf9a00c543", "reference": "8a683f5057005bef742916847c0befbf9a00c543", "shasum": ""}, "require": {"google/protobuf": "^4.31.1", "php": "^8.1"}, "conflict": {"temporal/sdk": "<2.9.0"}, "suggest": {"google/common-protos": "Required for Temporal API"}, "type": "library", "autoload": {"psr-4": {"Temporal\\": "generated/Temporal", "RoadRunner\\": "generated/RoadRunner", "GPBMetadata\\": "generated/GPBMetadata"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON> (but<PERSON><PERSON>)", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON> (roxblnfk)", "email": "<EMAIL>"}, {"name": "RoadRunner Community", "homepage": "https://github.com/roadrunner-server/roadrunner/graphs/contributors"}], "description": "RoadRunner PHP API", "homepage": "https://roadrunner.dev", "support": {"chat": "https://discord.gg/V6EK4he", "docs": "https://docs.roadrunner.dev", "forum": "https://forum.roadrunner.dev", "issues": "https://github.com/roadrunner-server/roadrunner/issues", "source": "https://github.com/roadrunner-php/roadrunner-api-dto/tree/v1.13.0"}, "funding": [{"url": "https://github.com/sponsors/roadrunner-server", "type": "github"}], "time": "2025-08-12T14:04:38+00:00"}, {"name": "roadrunner-php/version-checker", "version": "v1.0.2", "source": {"type": "git", "url": "https://github.com/roadrunner-php/version-checker.git", "reference": "a7994f700586265a54a2989b97f7d7f25ed5890b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/roadrunner-php/version-checker/zipball/a7994f700586265a54a2989b97f7d7f25ed5890b", "reference": "a7994f700586265a54a2989b97f7d7f25ed5890b", "shasum": ""}, "require": {"composer-runtime-api": "^2.0", "composer/semver": "^3.3", "php": "^8.0", "symfony/process": "^5.4 || ^6.0 || ^7.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "^3.8", "phpunit/phpunit": "^9.6 || ^10.0", "vimeo/psalm": "^5.9"}, "type": "library", "autoload": {"psr-4": {"RoadRunner\\VersionChecker\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "The package for checking the version of the RoadRunner", "homepage": "https://github.com/roadrunner-php/version-checker", "keywords": ["roadrunner", "roadrunner-php", "version-checker"], "support": {"source": "https://github.com/roadrunner-php/version-checker/tree/v1.0.2"}, "funding": [{"url": "https://github.com/roadrunner-server", "type": "github"}], "time": "2025-05-20T08:45:05+00:00"}, {"name": "spiral-packages/league-event", "version": "1.0.1", "source": {"type": "git", "url": "https://github.com/spiral-packages/league-event.git", "reference": "ef6e87e2e5a2d12ecfc92e99a6e6f0aec72f7aaf"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/spiral-packages/league-event/zipball/ef6e87e2e5a2d12ecfc92e99a6e6f0aec72f7aaf", "reference": "ef6e87e2e5a2d12ecfc92e99a6e6f0aec72f7aaf", "shasum": ""}, "require": {"league/event": "^3.0", "php": "^8.1", "spiral/events": "^3.0"}, "require-dev": {"mockery/mockery": "^1.5", "phpunit/phpunit": "^9.5", "roave/security-advisories": "dev-latest", "spiral/testing": "^2.0", "vimeo/psalm": "^4.22"}, "type": "library", "extra": {"spiral": {"bootloaders": ["Spiral\\League\\Event\\Bootloader\\EventBootloader"]}}, "autoload": {"psr-4": {"Spiral\\League\\Event\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "The League Event bridge for Spiral Framework", "homepage": "https://github.com/spiral-packages/symfony-event-dispatcher", "keywords": ["event-dispatcher", "spiral", "spiral-packages"], "support": {"issues": "https://github.com/spiral-packages/league-event/issues", "source": "https://github.com/spiral-packages/league-event/tree/1.0.1"}, "time": "2022-09-14T08:02:26+00:00"}, {"name": "spiral-packages/scheduler", "version": "2.6.0", "source": {"type": "git", "url": "https://github.com/spiral-packages/scheduler.git", "reference": "d3643e0569431b1bcaa151b9bef7f87d1117d36e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/spiral-packages/scheduler/zipball/d3643e0569431b1bcaa151b9bef7f87d1117d36e", "reference": "d3643e0569431b1bcaa151b9bef7f87d1117d36e", "shasum": ""}, "require": {"butschster/cron-expression-generator": "^1.10", "nesbot/carbon": "^2.52 || ^3.0", "php": ">=8.1", "psr/event-dispatcher": "^1", "spiral/attributes": "^2.8 || ^3.0", "spiral/boot": "^3.14", "spiral/cache": "^3.14", "spiral/core": "^3.14", "spiral/queue": "^3.14", "spiral/snapshots": "^3.14", "symfony/process": "^6.0 || ^7.0"}, "require-dev": {"spiral/code-style": "^2.2", "spiral/dumper": "^3.3", "spiral/framework": "^3.14", "spiral/testing": "^2.8.3", "vimeo/psalm": "^4.9 || ^5.26"}, "type": "library", "extra": {"spiral": {"bootloaders": ["Spiral\\Scheduler\\Bootloader\\SchedulerBootloader"]}}, "autoload": {"psr-4": {"Spiral\\Scheduler\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "but<PERSON><PERSON>", "email": "<EMAIL>", "role": "PHP web developer"}, {"name": "roxblnfk", "email": "<EMAIL>"}], "description": "The scheduler is a package for spiral framework. It will help to managing scheduled tasks on your server.", "homepage": "https://github.com/spiral-packages/scheduler", "keywords": ["scheduler", "spiral", "spiral-packages"], "support": {"issues": "https://github.com/spiral-packages/scheduler/issues", "source": "https://github.com/spiral-packages/scheduler/tree/2.6.0"}, "time": "2025-06-05T12:19:14+00:00"}, {"name": "spiral-packages/yii-error-handler-bridge", "version": "1.1.3", "source": {"type": "git", "url": "https://github.com/spiral-packages/yii-error-handler-bridge.git", "reference": "f2c223d79f7fd4d5cc3cc6c1bef261547153a1c1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/spiral-packages/yii-error-handler-bridge/zipball/f2c223d79f7fd4d5cc3cc6c1bef261547153a1c1", "reference": "f2c223d79f7fd4d5cc3cc6c1bef261547153a1c1", "shasum": ""}, "require": {"php": "^8.1", "spiral/boot": "^3.0", "spiral/exceptions": "^3.0", "yiisoft/error-handler": "4.1.0"}, "require-dev": {"buggregator/trap": "^1.13", "mockery/mockery": "^1.5", "phpunit/phpunit": "^9.5.28", "spiral/code-style": "^2.2.2", "spiral/http": "^3.0", "spiral/testing": "^2.9", "vimeo/psalm": "^6.10"}, "type": "library", "extra": {"spiral": {"bootloaders": ["Spiral\\YiiErrorHandler\\Bootloader\\YiiErrorHandlerBootloader"]}}, "autoload": {"psr-4": {"Spiral\\YiiErrorHandler\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON><PERSON><PERSON> (roxblnfk)", "homepage": "https://github.com/roxblnfk"}], "description": "Yii Error Handler integration package for Spiral Framework", "homepage": "https://github.com/spiral-packages/yii-error-handler-bridge", "keywords": ["error-handling", "spiral", "spiral-framework"], "support": {"issues": "https://github.com/spiral-packages/yii-error-handler-bridge/issues", "source": "https://github.com/spiral-packages/yii-error-handler-bridge/tree/1.1.3"}, "time": "2025-04-18T13:18:26+00:00"}, {"name": "spiral/attributes", "version": "v3.1.8", "source": {"type": "git", "url": "https://github.com/spiral/attributes.git", "reference": "a7e368a42b079f56c16d7fc513b68190b96842c3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/spiral/attributes/zipball/a7e368a42b079f56c16d7fc513b68190b96842c3", "reference": "a7e368a42b079f56c16d7fc513b68190b96842c3", "shasum": ""}, "require": {"php": ">=8.1", "psr/cache": "1 - 3", "psr/simple-cache": "1 - 3"}, "require-dev": {"doctrine/annotations": "^1.14 || ^2.0", "jetbrains/phpstorm-attributes": "^1.0", "phpunit/phpunit": "^9.5.20", "spiral/code-style": "^2.2", "vimeo/psalm": "^5.17"}, "type": "library", "autoload": {"files": ["src/polyfill.php"], "psr-4": {"Spiral\\Attributes\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON> (wolfy-j)", "email": "<EMAIL>"}, {"name": "<PERSON> (but<PERSON><PERSON>)", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON> (roxblnfk)", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON> (msmakouz)", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON> (SerafimArts)", "email": "<EMAIL>"}], "description": "PHP attributes reader", "homepage": "https://spiral.dev", "keywords": ["annotations", "attributes", "metadata"], "support": {"chat": "https://discord.gg/V6EK4he", "docs": "https://spiral.dev/docs", "issues": "https://github.com/spiral/attributes/issues", "source": "https://github.com/spiral/attributes"}, "funding": [{"url": "https://github.com/sponsors/spiral", "type": "github"}], "time": "2024-12-09T15:33:18+00:00"}, {"name": "spiral/composer-publish-plugin", "version": "v1.1.2", "source": {"type": "git", "url": "https://github.com/spiral/composer-publish-plugin.git", "reference": "8d25c228389fcc0d4315a83913b8a5eb26c4e45b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/spiral/composer-publish-plugin/zipball/8d25c228389fcc0d4315a83913b8a5eb26c4e45b", "reference": "8d25c228389fcc0d4315a83913b8a5eb26c4e45b", "shasum": ""}, "require": {"composer-plugin-api": "^1.1|^2.0", "php": ">=7.1"}, "require-dev": {"composer/composer": "^1.7", "phpunit/phpunit": "~7.0", "spiral/code-style": "^1.0"}, "type": "composer-plugin", "extra": {"class": "Spiral\\Composer\\PublishPlugin"}, "autoload": {"psr-4": {"Spiral\\Composer\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "support": {"issues": "https://github.com/spiral/composer-publish-plugin/issues", "source": "https://github.com/spiral/composer-publish-plugin/tree/v1.1.2"}, "time": "2020-11-12T23:10:18+00:00"}, {"name": "spiral/cycle-bridge", "version": "v2.11.1", "source": {"type": "git", "url": "https://github.com/spiral/cycle-bridge.git", "reference": "0150830ad2002a89b7fc56a711265547287d15c2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/spiral/cycle-bridge/zipball/0150830ad2002a89b7fc56a711265547287d15c2", "reference": "0150830ad2002a89b7fc56a711265547287d15c2", "shasum": ""}, "require": {"cycle/annotated": "^4.2.3", "cycle/migrations": "^4.2.4", "cycle/orm": "^2.9.3", "cycle/schema-builder": "^2.11.1", "cycle/schema-migrations-generator": "^2.3", "cycle/schema-renderer": "^1.2", "doctrine/inflector": "^1.4 || ^2.0.10", "php": ">=8.1", "psr/container": "^1.1 || ^2.0.2", "spiral/attributes": "^2.10 || ^3.1.8", "spiral/auth": "^3.15", "spiral/boot": "^3.15", "spiral/config": "^3.15", "spiral/console": "^3.15", "spiral/core": "^3.15", "spiral/data-grid-bridge": "^3.0.1", "spiral/filters": "^3.15", "spiral/prototype": "^3.15", "spiral/reactor": "^3.15", "spiral/scaffolder": "^3.15", "spiral/tokenizer": "^3.15"}, "require-dev": {"cycle/entity-behavior": "^1.3.1", "doctrine/collections": "^2.2.2", "illuminate/collections": "^10.48", "infection/infection": "^0.29.9", "mockery/mockery": "^1.6.12", "phpunit/phpunit": "^9.6.22", "spiral-packages/database-seeder": "^3.3", "spiral/code-style": "^2.2.2", "spiral/framework": "^3.15.1", "spiral/nyholm-bridge": "^1.3", "spiral/testing": "^2.9", "spiral/validator": "^1.5.4", "vimeo/psalm": "^6.4"}, "type": "library", "autoload": {"psr-4": {"Spiral\\Cycle\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON> (wolfy-j)", "email": "<EMAIL>"}, {"name": "<PERSON> (but<PERSON><PERSON>)", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON> (roxblnfk)", "email": "<EMAIL>"}], "description": "Cycle ORM integration package", "homepage": "https://spiral.dev", "support": {"issues": "https://github.com/spiral/framework/issues", "source": "https://github.com/spiral/cycle-bridge"}, "funding": [{"url": "https://github.com/sponsors/spiral", "type": "github"}], "time": "2025-02-07T09:15:21+00:00"}, {"name": "spiral/data-grid", "version": "v3.0.1", "source": {"type": "git", "url": "https://github.com/spiral/data-grid.git", "reference": "6718350c8f8d49483442f04f50f293c069f5fc78"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/spiral/data-grid/zipball/6718350c8f8d49483442f04f50f293c069f5fc78", "reference": "6718350c8f8d49483442f04f50f293c069f5fc78", "shasum": ""}, "require": {"php": ">=8.1", "spiral/attributes": "^3.0"}, "require-dev": {"phpunit/phpunit": "^9.5.20", "ramsey/uuid": "^4.2.3", "rector/rector": "^2.1.2", "spiral/code-style": "^2.2.2", "vimeo/psalm": "^4.27"}, "type": "library", "extra": {"branch-alias": {"dev-3.0": "3.0.x-dev"}}, "autoload": {"files": ["src/helpers.php"], "psr-4": {"Spiral\\DataGrid\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON> (vvval)", "email": "<EMAIL>"}, {"name": "<PERSON> (wolfy-j)", "email": "<EMAIL>"}], "description": "Data Grid specification builder", "homepage": "https://spiral.dev", "support": {"issues": "https://github.com/spiral/framework/issues", "source": "https://github.com/spiral/data-grid"}, "time": "2025-07-27T09:23:44+00:00"}, {"name": "spiral/data-grid-bridge", "version": "v3.0.1", "source": {"type": "git", "url": "https://github.com/spiral/data-grid-bridge.git", "reference": "29e8b7c9faf486d57bad8a3d14d53c2a7ccdf584"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/spiral/data-grid-bridge/zipball/29e8b7c9faf486d57bad8a3d14d53c2a7ccdf584", "reference": "29e8b7c9faf486d57bad8a3d14d53c2a7ccdf584", "shasum": ""}, "require": {"ext-json": "*", "php": ">=8.1", "spiral/attributes": "^2.10 || ^3.0", "spiral/boot": "^3.0", "spiral/data-grid": "^3.0", "spiral/http": "^3.0"}, "require-dev": {"phpunit/phpunit": "^9.5.20", "spiral/hmvc": "^3.0", "vimeo/psalm": "^4.27"}, "type": "library", "extra": {"branch-alias": {"dev-3.0": "3.0.x-dev"}}, "autoload": {"psr-4": {"Spiral\\DataGrid\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON> (wolfy-j)", "email": "<EMAIL>"}], "description": "Data Grid specification builder adapter for Spiral Framework", "homepage": "https://spiral.dev", "support": {"issues": "https://github.com/spiral/framework/issues", "source": "https://github.com/spiral/data-grid-bridge"}, "time": "2022-09-14T18:48:30+00:00"}, {"name": "spiral/framework", "version": "3.15.8", "source": {"type": "git", "url": "https://github.com/spiral/framework.git", "reference": "a3107e0f848e66974363684e260b3fedc10a17c4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/spiral/framework/zipball/a3107e0f848e66974363684e260b3fedc10a17c4", "reference": "a3107e0f848e66974363684e260b3fedc10a17c4", "shasum": ""}, "require": {"cocur/slugify": "^3.2 || ^4.0", "codedungeon/php-cli-colors": "^1.11", "defuse/php-encryption": "^2.2", "doctrine/inflector": "^1.4|^2.0", "ext-json": "*", "ext-mbstring": "*", "ext-tokenizer": "*", "league/flysystem": "^2.3.1 || ^3.0", "monolog/monolog": "^2.9.2 || ^3.5", "myclabs/deep-copy": "^1.9", "nette/php-generator": "^4.1.7", "nikic/php-parser": "^5.4", "php": ">=8.1", "psr/container": "^1.1|^2.0", "psr/event-dispatcher": "^1.0", "psr/http-factory": "^1.0", "psr/http-factory-implementation": "^1.0", "psr/http-message": "^1.0|^2.0", "psr/http-server-middleware": "^1.0", "psr/log": "1 - 3", "psr/simple-cache": "2 - 3", "ramsey/uuid": "^4.7", "spiral/attributes": "^3.1.8", "spiral/composer-publish-plugin": "^1.0", "symfony/console": "^6.4.17 || ^7.2", "symfony/finder": "^5.4.45 || ^6.4.17 || ^7.2", "symfony/mailer": "^5.4.45 || ^6.4.17 || ^7.2", "symfony/translation": "^5.4.45 || ^6.4.17 || ^7.2", "vlucas/phpdotenv": "^5.4"}, "conflict": {"spiral/roadrunner-bridge": "<3.7", "spiral/sapi-bridge": "<1.1"}, "replace": {"spiral/annotated-routes": "self.version", "spiral/auth": "self.version", "spiral/auth-http": "self.version", "spiral/boot": "self.version", "spiral/broadcasting": "self.version", "spiral/cache": "self.version", "spiral/config": "self.version", "spiral/console": "self.version", "spiral/cookies": "self.version", "spiral/core": "self.version", "spiral/csrf": "self.version", "spiral/debug": "self.version", "spiral/distribution": "self.version", "spiral/dotenv-bridge": "self.version", "spiral/encrypter": "self.version", "spiral/events": "self.version", "spiral/exceptions": "self.version", "spiral/files": "self.version", "spiral/filters": "self.version", "spiral/hmvc": "self.version", "spiral/http": "self.version", "spiral/interceptors": "self.version", "spiral/logger": "self.version", "spiral/mailer": "self.version", "spiral/models": "self.version", "spiral/monolog-bridge": "self.version", "spiral/pagination": "self.version", "spiral/prototype": "self.version", "spiral/queue": "self.version", "spiral/reactor": "self.version", "spiral/router": "self.version", "spiral/scaffolder": "self.version", "spiral/security": "self.version", "spiral/sendit": "self.version", "spiral/serializer": "self.version", "spiral/session": "self.version", "spiral/snapshots": "self.version", "spiral/stempler": "self.version", "spiral/stempler-bridge": "self.version", "spiral/storage": "self.version", "spiral/streams": "self.version", "spiral/telemetry": "self.version", "spiral/tokenizer": "self.version", "spiral/translator": "self.version", "spiral/validation": "self.version", "spiral/views": "self.version"}, "require-dev": {"aws/aws-sdk-php": "^3.338", "buggregator/trap": "^1.13.3", "doctrine/annotations": "^2.0.2", "google/protobuf": "^3.25|^4.29", "guzzlehttp/psr7": "^1.7|^2.7", "jetbrains/phpstorm-attributes": "^1.2", "league/flysystem-async-aws-s3": "^2.5 || ^3.29", "league/flysystem-aws-s3-v3": "^2.5 || ^3.29", "mikey179/vfsstream": "^1.6.12", "mockery/mockery": "^1.6.12", "phpunit/phpunit": "^10.5.41", "ramsey/collection": "^1.3", "rector/rector": "~2.0.9", "spiral/code-style": "^2.2.2", "spiral/nyholm-bridge": "^1.3", "spiral/testing": "^2.8.3", "spiral/validator": "^1.5.4", "symplify/monorepo-builder": "^10.3.3", "vimeo/psalm": "^6.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.15.x-dev"}}, "autoload": {"files": ["src/Boot/src/helpers.php", "src/Framework/helpers.php", "src/Scaffolder/src/helpers.php", "src/Stempler/src/helpers.php", "src/Translator/src/helpers.php"], "psr-4": {"Spiral\\": "src/Framework", "Spiral\\Auth\\": ["src/Auth/src", "src/AuthHttp/src"], "Spiral\\Boot\\": "src/Boot/src", "Spiral\\Core\\": ["src/Core/src", "src/Hmvc/src"], "Spiral\\Csrf\\": "src/Csrf/src", "Spiral\\Http\\": "src/Http/src", "Spiral\\Cache\\": "src/Cache/src", "Spiral\\Debug\\": "src/Debug/src", "Spiral\\Files\\": "src/Files/src", "Spiral\\Queue\\": "src/Queue/src", "Spiral\\Views\\": "src/Views/src", "Spiral\\Config\\": "src/Config/src", "Spiral\\DotEnv\\": "src/Bridge/Dotenv/src", "Spiral\\Events\\": "src/Events/src", "Spiral\\Logger\\": "src/Logger/src", "Spiral\\Mailer\\": "src/Mailer/src", "Spiral\\Models\\": "src/Models/src", "Spiral\\Router\\": ["src/AnnotatedRoutes/src", "src/Router/src"], "Spiral\\SendIt\\": "src/SendIt/src", "Spiral\\Console\\": "src/Console/src", "Spiral\\Cookies\\": "src/Cookies/src", "Spiral\\Filters\\": "src/Filters/src", "Spiral\\Monolog\\": "src/Bridge/Monolog/src", "Spiral\\Reactor\\": "src/Reactor/src", "Spiral\\Session\\": "src/Session/src", "Spiral\\Storage\\": "src/Storage/src", "Spiral\\Streams\\": "src/Streams/src", "Spiral\\Security\\": "src/Security/src", "Spiral\\Stempler\\": ["src/Bridge/Stempler/src", "src/<PERSON>empler/src"], "Spiral\\Encrypter\\": "src/Encrypter/src", "Spiral\\Prototype\\": "src/Prototype/src", "Spiral\\Snapshots\\": "src/Snapshots/src", "Spiral\\Telemetry\\": "src/Telemetry/src", "Spiral\\Tokenizer\\": "src/Tokenizer/src", "Spiral\\Exceptions\\": "src/Exceptions/src", "Spiral\\Pagination\\": "src/Pagination/src", "Spiral\\Scaffolder\\": "src/Scaffolder/src", "Spiral\\Serializer\\": "src/Serializer/src", "Spiral\\Translator\\": "src/Translator/src", "Spiral\\Validation\\": "src/Validation/src", "Spiral\\Broadcasting\\": "src/Broadcasting/src", "Spiral\\Distribution\\": "src/Distribution/src", "Spiral\\Interceptors\\": "src/Interceptors/src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON> (wolfy-j)", "email": "<EMAIL>"}, {"name": "<PERSON> (but<PERSON><PERSON>)", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON> (roxblnfk)", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON> (msmakouz)", "email": "<EMAIL>"}], "description": "Spiral, High-Performance PHP/Go Framework", "homepage": "https://spiral.dev", "support": {"issues": "https://github.com/spiral/framework/issues", "source": "https://github.com/spiral/framework"}, "funding": [{"url": "https://github.com/sponsors/spiral", "type": "github"}], "time": "2025-04-22T13:51:37+00:00"}, {"name": "spiral/goridge", "version": "4.2.1", "source": {"type": "git", "url": "https://github.com/roadrunner-php/goridge.git", "reference": "2a372118dac1f0c0511e2862f963ce649fefd9fa"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/roadrunner-php/goridge/zipball/2a372118dac1f0c0511e2862f963ce649fefd9fa", "reference": "2a372118dac1f0c0511e2862f963ce649fefd9fa", "shasum": ""}, "require": {"ext-json": "*", "ext-sockets": "*", "php": ">=8.1", "spiral/roadrunner": "^2023 || ^2024.1 || ^2025.1"}, "require-dev": {"google/protobuf": "^3.22 || ^4.0", "infection/infection": "^0.29.0", "jetbrains/phpstorm-attributes": "^1.0", "phpunit/phpunit": "^10.5.45", "rybakit/msgpack": "^0.7", "spiral/code-style": "*", "vimeo/psalm": "^6.0"}, "suggest": {"ext-msgpack": "MessagePack codec support", "ext-protobuf": "Protobuf codec support", "google/protobuf": "(^3.0) Protobuf codec support", "rybakit/msgpack": "(^0.7) MessagePack codec support"}, "type": "go<PERSON>", "autoload": {"psr-4": {"Spiral\\Goridge\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON> (wolfy-j)", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "homepage": "https://github.com/rustatian"}, {"name": "<PERSON><PERSON><PERSON> (roxblnfk)", "homepage": "https://github.com/roxblnfk"}, {"name": "<PERSON> (but<PERSON><PERSON>)", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON> (msmakouz)", "email": "<EMAIL>"}, {"name": "RoadRunner Community", "homepage": "https://github.com/roadrunner-server/roadrunner/graphs/contributors"}], "description": "High-performance PHP-to-Golang RPC bridge", "homepage": "https://spiral.dev/", "support": {"chat": "https://discord.gg/V6EK4he", "docs": "https://docs.roadrunner.dev", "issues": "https://github.com/roadrunner-server/roadrunner/issues", "source": "https://github.com/roadrunner-php/goridge/tree/4.2.1"}, "funding": [{"url": "https://github.com/sponsors/roadrunner-server", "type": "github"}], "time": "2025-05-05T13:55:33+00:00"}, {"name": "spiral/grpc-client", "version": "1.0.0-rc1", "source": {"type": "git", "url": "https://github.com/spiral/grpc-client.git", "reference": "1525a0987f7f62919de73e09f38476f4366ef129"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/spiral/grpc-client/zipball/1525a0987f7f62919de73e09f38476f4366ef129", "reference": "1525a0987f7f62919de73e09f38476f4366ef129", "shasum": ""}, "require": {"google/common-protos": "^1.3 || ^2.0 || ^3.0 || ^4.0", "google/protobuf": "^3 || ^4", "grpc/grpc": "^1.57", "php": ">=8.1", "psr/container": "^2.0", "spiral/core": "^3.14", "spiral/hmvc": "^3.14", "spiral/tokenizer": "^3.13"}, "require-dev": {"buggregator/trap": "^1.11.1", "ergebnis/phpunit-slow-test-detector": "^2.17", "friendsofphp/php-cs-fixer": "^3.65", "internal/dload": "^1.0.0", "pestphp/pest": "^2.36", "phpunit/phpunit": "^10.5.36", "spiral/boot": "^3.14.7", "spiral/code-style": "^2.2.1", "vimeo/psalm": "^5.26.1"}, "suggest": {"ext-grpc": "Required to execute gRPC calls"}, "type": "library", "autoload": {"files": ["src/stub.php"], "psr-4": {"Spiral\\Grpc\\Client\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON> (wolfy-j)", "email": "<EMAIL>"}, {"name": "<PERSON> (but<PERSON><PERSON>)", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON> (roxblnfk)", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON> (msmakouz)", "email": "<EMAIL>"}], "description": "gRPC client", "homepage": "https://spiral.dev", "support": {"chat": "https://discord.gg/V6EK4he", "docs": "https://spiral.dev/docs", "issues": "https://github.com/spiral/grpc-client/issues", "source": "https://github.com/spiral/grpc-client"}, "funding": [{"url": "https://github.com/sponsors/spiral", "type": "github"}], "time": "2024-12-11T08:47:48+00:00"}, {"name": "spiral/nyholm-bridge", "version": "v1.3.0", "source": {"type": "git", "url": "https://github.com/spiral/nyholm-bridge.git", "reference": "e3d99a09a56450fa42d652bdcc3b96434f92448e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/spiral/nyholm-bridge/zipball/e3d99a09a56450fa42d652bdcc3b96434f92448e", "reference": "e3d99a09a56450fa42d652bdcc3b96434f92448e", "shasum": ""}, "require": {"nyholm/psr7": "^1.4", "php": ">=8.1", "spiral/boot": "^3.0", "spiral/http": "^3.0"}, "require-dev": {"phpunit/phpunit": "^9.5.20", "vimeo/psalm": ">=4.4"}, "type": "library", "autoload": {"psr-4": {"Spiral\\Nyholm\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Spiral Framework: Nyholm PSR-7/PSR-17 bridge", "support": {"issues": "https://github.com/spiral/nyholm-bridge/issues", "source": "https://github.com/spiral/nyholm-bridge/tree/v1.3.0"}, "time": "2022-09-19T07:50:08+00:00"}, {"name": "spiral/roadrunner", "version": "v2025.1.2", "source": {"type": "git", "url": "https://github.com/roadrunner-server/roadrunner.git", "reference": "885c7087efa77380d5109901cf0a4888f611294b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/roadrunner-server/roadrunner/zipball/885c7087efa77380d5109901cf0a4888f611294b", "reference": "885c7087efa77380d5109901cf0a4888f611294b", "shasum": ""}, "type": "metapackage", "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON> / Wolfy-<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "homepage": "https://github.com/rustatian"}, {"name": "RoadRunner Community", "homepage": "https://github.com/roadrunner-server/roadrunner/graphs/contributors"}], "description": "RoadRunner: High-performance PHP application server and process manager written in Go and powered with plugins", "homepage": "https://roadrunner.dev/", "support": {"chat": "https://discord.gg/V6EK4he", "docs": "https://roadrunner.dev/docs", "forum": "https://forum.roadrunner.dev/", "issues": "https://github.com/roadrunner-server/roadrunner/issues", "source": "https://github.com/roadrunner-server/roadrunner/tree/v2025.1.2"}, "funding": [{"url": "https://github.com/sponsors/roadrunner-server", "type": "github"}], "time": "2025-06-14T22:21:13+00:00"}, {"name": "spiral/roadrunner-bridge", "version": "v4.0.0-RC6", "source": {"type": "git", "url": "https://github.com/spiral/roadrunner-bridge.git", "reference": "005e5a0ce21b9face0e767f2f3d6b1ad0d1643a0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/spiral/roadrunner-bridge/zipball/005e5a0ce21b9face0e767f2f3d6b1ad0d1643a0", "reference": "005e5a0ce21b9face0e767f2f3d6b1ad0d1643a0", "shasum": ""}, "require": {"grpc/grpc": "^1.57", "php": ">=8.1", "psr/http-factory": "^1.1", "psr/simple-cache": "^3.0", "roadrunner-php/app-logger": "^1.0", "roadrunner-php/centrifugo": "^2.0", "roadrunner-php/lock": "^1.0", "spiral/boot": "^3.14", "spiral/grpc-client": "^1.0", "spiral/hmvc": "^3.14", "spiral/roadrunner-grpc": "^3.3", "spiral/roadrunner-http": "^3.5", "spiral/roadrunner-jobs": "^4.4", "spiral/roadrunner-kv": "^4.0", "spiral/roadrunner-metrics": "^3.0", "spiral/roadrunner-tcp": "^3.1 || ^4.0", "spiral/scaffolder": "^3.13", "spiral/serializer": "^3.13"}, "require-dev": {"buggregator/trap": "^1.10", "internal/dload": "^1.0.0", "phpunit/phpunit": "^10.5", "spiral/code-style": "^2.2", "spiral/framework": "^3.15.6", "spiral/nyholm-bridge": "^1.3", "spiral/roadrunner-cli": "^2.6", "spiral/testing": "^2.9.0", "vimeo/psalm": "^6.0"}, "suggest": {"ext-protobuf": "For better performance, install the protobuf C extension."}, "type": "library", "autoload": {"psr-4": {"Spiral\\RoadRunnerBridge\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON> (wolfy-j)", "email": "<EMAIL>"}, {"name": "<PERSON> (but<PERSON><PERSON>)", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON> (roxblnfk)", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON> (msmakouz)", "email": "<EMAIL>"}], "description": "RoadRunner integration package", "homepage": "https://spiral.dev", "support": {"chat": "https://discord.gg/V6EK4he", "docs": "https://spiral.dev/docs", "issues": "https://github.com/spiral/roadrunner-bridge/issues", "source": "https://github.com/spiral/roadrunner-bridge"}, "funding": [{"url": "https://github.com/sponsors/spiral", "type": "github"}], "time": "2025-06-07T09:39:54+00:00"}, {"name": "spiral/roadrunner-cli", "version": "v2.7.1", "source": {"type": "git", "url": "https://github.com/roadrunner-php/cli.git", "reference": "a51ff873654744821437e76406df7b6a0d4dbfe1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/roadrunner-php/cli/zipball/a51ff873654744821437e76406df7b6a0d4dbfe1", "reference": "a51ff873654744821437e76406df7b6a0d4dbfe1", "shasum": ""}, "require": {"composer/semver": "^3.4", "ext-json": "*", "php": ">=8.1", "spiral/roadrunner-worker": "^2 || ^3", "spiral/tokenizer": "^2.13 || ^3.15", "symfony/console": "^5.3 || ^6.0 || ^7.0", "symfony/http-client": "^4.4.51 || ^5.4.49 || ^6.4.17 || ^7.2", "symfony/yaml": "^5.4.49 || ^6.4.17 || ^7.2"}, "require-dev": {"jetbrains/phpstorm-attributes": "^1.2", "spiral/code-style": "^2.2.2", "spiral/dumper": "^3.3", "vimeo/psalm": "^6.0"}, "bin": ["bin/rr"], "type": "library", "autoload": {"psr-4": {"Spiral\\RoadRunner\\Console\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON> (wolfy-j)", "email": "<EMAIL>"}, {"name": "RoadRunner Community", "homepage": "https://github.com/spiral/roadrunner/graphs/contributors"}], "description": "RoadRunner: Command Line Interface", "homepage": "https://roadrunner.dev", "support": {"chat": "https://discord.gg/V6EK4he", "docs": "https://docs.roadrunner.dev", "issues": "https://github.com/roadrunner-server/roadrunner/issues", "source": "https://github.com/roadrunner-php/cli/tree/v2.7.1"}, "funding": [{"url": "https://github.com/sponsors/roadrunner-server", "type": "github"}], "time": "2025-02-18T12:24:20+00:00"}, {"name": "spiral/roadrunner-grpc", "version": "v3.5.2", "source": {"type": "git", "url": "https://github.com/roadrunner-php/grpc.git", "reference": "916c061de160d6b2f3efc82dcffac0360d84fab8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/roadrunner-php/grpc/zipball/916c061de160d6b2f3efc82dcffac0360d84fab8", "reference": "916c061de160d6b2f3efc82dcffac0360d84fab8", "shasum": ""}, "require": {"ext-json": "*", "google/common-protos": "^3.1|^4.0", "google/protobuf": "^3.7 || ^4.0", "php": ">=8.1", "spiral/goridge": "^4.0", "spiral/roadrunner": "^2024.3 || ^2025.1", "spiral/roadrunner-worker": "^3.0", "symfony/polyfill-php83": "*"}, "require-dev": {"jetbrains/phpstorm-attributes": "^1.0", "mockery/mockery": "^1.4", "phpunit/phpunit": "^10.0", "spiral/code-style": "^2.2", "spiral/dumper": "^3.3", "vimeo/psalm": ">=5.8"}, "type": "library", "autoload": {"psr-4": {"Spiral\\RoadRunner\\GRPC\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON> (wolfy-j)", "email": "<EMAIL>"}, {"name": "<PERSON> (but<PERSON><PERSON>)", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON> (roxblnfk)", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON> (msmakouz)", "email": "<EMAIL>"}, {"name": "RoadRunner Community", "homepage": "https://github.com/spiral/roadrunner/graphs/contributors"}], "description": "High-Performance GRPC server for PHP applications", "homepage": "https://roadrunner.dev/", "support": {"chat": "https://discord.gg/V6EK4he", "docs": "https://docs.roadrunner.dev", "forum": "https://forum.roadrunner.dev/", "issues": "https://github.com/roadrunner-server/roadrunner/issues", "source": "https://github.com/roadrunner-php/grpc/tree/v3.5.2"}, "funding": [{"url": "https://github.com/sponsors/roadrunner-server", "type": "github"}], "time": "2025-05-18T13:54:33+00:00"}, {"name": "spiral/roadrunner-http", "version": "v3.6.0", "source": {"type": "git", "url": "https://github.com/roadrunner-php/http.git", "reference": "a44a5f7d54d4ee8a14fe99cd22dcd128db270c88"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/roadrunner-php/http/zipball/a44a5f7d54d4ee8a14fe99cd22dcd128db270c88", "reference": "a44a5f7d54d4ee8a14fe99cd22dcd128db270c88", "shasum": ""}, "require": {"ext-json": "*", "php": ">=8.1", "psr/http-factory": "^1.0.1", "psr/http-message": "^1.0.1 || ^2.0", "roadrunner-php/roadrunner-api-dto": "^1.6", "spiral/roadrunner": "^2023.3 || ^2024.1 || ^2025.1", "spiral/roadrunner-worker": "^3.5", "symfony/polyfill-php83": "^1.29"}, "require-dev": {"jetbrains/phpstorm-attributes": "^1.0", "nyholm/psr7": "^1.3", "phpunit/phpunit": "^10.5", "spiral/code-style": "^2.3", "spiral/dumper": "^3.3", "symfony/process": "^6.2 || ^7.0", "vimeo/psalm": "^6.13"}, "suggest": {"ext-protobuf": "Provides Protocol Buffers support. Without it, performance will be lower.", "spiral/roadrunner-cli": "Provides RoadRunner installation and management CLI tools"}, "type": "library", "autoload": {"psr-4": {"Spiral\\RoadRunner\\Http\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON> (wolfy-j)", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "homepage": "https://github.com/rustatian"}, {"name": "<PERSON><PERSON><PERSON> (roxblnfk)", "homepage": "https://github.com/roxblnfk"}, {"name": "<PERSON> (but<PERSON><PERSON>)", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON> (msmakouz)", "email": "<EMAIL>"}, {"name": "RoadRunner Community", "homepage": "https://github.com/roadrunner-server/roadrunner/graphs/contributors"}], "description": "RoadRunner: HTTP and PSR-7 worker", "homepage": "https://spiral.dev/", "support": {"chat": "https://discord.gg/V6EK4he", "docs": "https://docs.roadrunner.dev", "forum": "https://forum.roadrunner.dev/", "issues": "https://github.com/roadrunner-server/roadrunner/issues", "source": "https://github.com/roadrunner-php/http/tree/v3.6.0"}, "funding": [{"url": "https://github.com/sponsors/roadrunner-server", "type": "github"}], "time": "2025-08-31T12:42:23+00:00"}, {"name": "spiral/roadrunner-jobs", "version": "v4.6.3", "source": {"type": "git", "url": "https://github.com/roadrunner-php/jobs.git", "reference": "20cfcd551f106de11c9cc7d0b167dc6b8832dc4e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/roadrunner-php/jobs/zipball/20cfcd551f106de11c9cc7d0b167dc6b8832dc4e", "reference": "20cfcd551f106de11c9cc7d0b167dc6b8832dc4e", "shasum": ""}, "require": {"ext-json": "*", "php": ">=8.1", "ramsey/uuid": "^3 || ^4", "roadrunner-php/roadrunner-api-dto": "^1.0", "spiral/goridge": "^4.0", "spiral/roadrunner": "^2024.2 || ^2025.1", "spiral/roadrunner-worker": "^3.0"}, "require-dev": {"google/protobuf": "^3.17 || ^4.0", "phpunit/phpunit": "^10.0", "roave/security-advisories": "dev-master", "vimeo/psalm": ">=5.8"}, "type": "library", "autoload": {"psr-4": {"Spiral\\RoadRunner\\Jobs\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON> (wolfy-j)", "email": "<EMAIL>"}, {"name": "<PERSON> (but<PERSON><PERSON>)", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON> (roxblnfk)", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON> (msmakouz)", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON> (SerafimArts)", "email": "<EMAIL>"}, {"name": "RoadRunner Community", "homepage": "https://github.com/spiral/roadrunner/graphs/contributors"}], "description": "RoadRunner Queues (Jobs) plugin API library", "homepage": "https://roadrunner.dev/", "support": {"chat": "https://discord.gg/V6EK4he", "docs": "https://docs.roadrunner.dev", "forum": "https://forum.roadrunner.dev/", "issues": "https://github.com/roadrunner-server/roadrunner/issues", "source": "https://github.com/roadrunner-php/jobs/tree/v4.6.3"}, "funding": [{"url": "https://github.com/sponsors/roadrunner-server", "type": "github"}], "time": "2025-05-27T08:35:56+00:00"}, {"name": "spiral/roadrunner-kv", "version": "v4.3.1", "source": {"type": "git", "url": "https://github.com/roadrunner-php/kv.git", "reference": "0db13f212c64463bd26cf71e28d0a8bdf997a4a0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/roadrunner-php/kv/zipball/0db13f212c64463bd26cf71e28d0a8bdf997a4a0", "reference": "0db13f212c64463bd26cf71e28d0a8bdf997a4a0", "shasum": ""}, "require": {"ext-json": "*", "php": ">=8.1", "psr/simple-cache": "2 - 3", "roadrunner-php/roadrunner-api-dto": "^1.0", "spiral/goridge": "^4.2", "spiral/roadrunner": "^2023.1 || ^2024.1 || ^2025.1"}, "require-dev": {"phpunit/phpunit": "^10.5.45", "roave/security-advisories": "dev-master", "spiral/code-style": "^2.2", "vimeo/psalm": ">=5.8"}, "suggest": {"ext-igbinary": "(>3.1.6) Igbinary serailizer support", "ext-sodium": "Sodium serailizer support"}, "type": "library", "autoload": {"psr-4": {"Spiral\\RoadRunner\\KeyValue\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON> (but<PERSON><PERSON>)", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON> (roxblnfk)", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON> (msmakouz)", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON> (SerafimArts)", "email": "<EMAIL>"}, {"name": "RoadRunner Community", "homepage": "https://github.com/spiral/roadrunner/graphs/contributors"}], "description": "RoadRunner kv plugin bridge", "homepage": "https://roadrunner.dev/", "support": {"chat": "https://discord.gg/V6EK4he", "docs": "https://docs.roadrunner.dev", "issues": "https://github.com/roadrunner-server/roadrunner/issues", "source": "https://github.com/roadrunner-php/kv/tree/v4.3.1"}, "funding": [{"url": "https://github.com/sponsors/roadrunner-server", "type": "github"}], "time": "2025-05-05T13:08:45+00:00"}, {"name": "spiral/roadrunner-metrics", "version": "3.3.0", "source": {"type": "git", "url": "https://github.com/roadrunner-php/metrics.git", "reference": "701c5c0beda29d42c7fdd5afda5d89aac6358938"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/roadrunner-php/metrics/zipball/701c5c0beda29d42c7fdd5afda5d89aac6358938", "reference": "701c5c0beda29d42c7fdd5afda5d89aac6358938", "shasum": ""}, "require": {"php": ">=8.1", "psr/log": ">=2.0", "spiral/goridge": "^4.2", "spiral/roadrunner": "^2023.1 || ^2024.1 || ^2025.1"}, "require-dev": {"jetbrains/phpstorm-attributes": "^1.0", "phpunit/phpunit": "^10.0", "vimeo/psalm": ">=5.8"}, "suggest": {"spiral/roadrunner-cli": "Provides RoadRunner installation and management CLI tools"}, "type": "library", "autoload": {"psr-4": {"Spiral\\RoadRunner\\Metrics\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON> (wolfy-j)", "email": "<EMAIL>"}, {"name": "<PERSON> (but<PERSON><PERSON>)", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON> (roxblnfk)", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON> (msmakouz)", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON> (SerafimArts)", "email": "<EMAIL>"}, {"name": "RoadRunner Community", "homepage": "https://github.com/spiral/roadrunner/graphs/contributors"}], "description": "RoadRunner: Prometheus metrics RPC", "homepage": "https://roadrunner.dev/", "support": {"chat": "https://discord.gg/V6EK4he", "docs": "https://docs.roadrunner.dev", "forum": "https://forum.roadrunner.dev/", "issues": "https://github.com/roadrunner-server/roadrunner/issues", "source": "https://github.com/roadrunner-php/metrics/tree/3.3.0"}, "funding": [{"url": "https://github.com/sponsors/roadrunner-server", "type": "github"}], "time": "2025-05-13T11:43:47+00:00"}, {"name": "spiral/roadrunner-tcp", "version": "v4.1.1", "source": {"type": "git", "url": "https://github.com/roadrunner-php/tcp.git", "reference": "b00582d3485577e4919a33cad02e70e1429b5408"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/roadrunner-php/tcp/zipball/b00582d3485577e4919a33cad02e70e1429b5408", "reference": "b00582d3485577e4919a33cad02e70e1429b5408", "shasum": ""}, "require": {"ext-json": "*", "php": ">=8.1", "spiral/roadrunner": "^2023.1 || ^2024.1 || ^2025.1", "spiral/roadrunner-worker": "^3.0"}, "require-dev": {"jetbrains/phpstorm-attributes": "^1.0", "phpunit/phpunit": "^10.5", "vimeo/psalm": ">=5.8"}, "suggest": {"spiral/roadrunner-cli": "Provides RoadRunner installation and management CLI tools"}, "type": "library", "autoload": {"psr-4": {"Spiral\\RoadRunner\\Tcp\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON> (wolfy-j)", "email": "<EMAIL>"}, {"name": "<PERSON> (but<PERSON><PERSON>)", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON> (roxblnfk)", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON> (msmakouz)", "email": "<EMAIL>"}, {"name": "RoadRunner Community", "homepage": "https://github.com/spiral/roadrunner/graphs/contributors"}], "description": "RoadRunner: TCP worker", "homepage": "https://roadrunner.dev/", "support": {"chat": "https://discord.gg/V6EK4he", "docs": "https://docs.roadrunner.dev", "forum": "https://forum.roadrunner.dev/", "issues": "https://github.com/roadrunner-server/roadrunner/issues", "source": "https://github.com/roadrunner-php/tcp/tree/v4.1.1"}, "funding": [{"url": "https://github.com/sponsors/roadrunner-server", "type": "github"}], "time": "2025-06-23T08:28:39+00:00"}, {"name": "spiral/roadrunner-worker", "version": "v3.6.2", "source": {"type": "git", "url": "https://github.com/roadrunner-php/worker.git", "reference": "8d9905b1e6677f34ff8623893f35b5e2fa828e37"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/roadrunner-php/worker/zipball/8d9905b1e6677f34ff8623893f35b5e2fa828e37", "reference": "8d9905b1e6677f34ff8623893f35b5e2fa828e37", "shasum": ""}, "require": {"composer-runtime-api": "^2.0", "ext-json": "*", "ext-sockets": "*", "php": ">=8.1", "psr/log": "^2.0 || ^3.0", "spiral/goridge": "^4.1.0", "spiral/roadrunner": "^2023.1 || ^2024.1 || ^2025.1"}, "require-dev": {"buggregator/trap": "^1.13", "jetbrains/phpstorm-attributes": "^1.0", "phpunit/phpunit": "^10.5.45", "spiral/code-style": "^2.2", "vimeo/psalm": "^6.0"}, "suggest": {"spiral/roadrunner-cli": "Provides RoadRunner installation and management CLI tools"}, "type": "library", "autoload": {"psr-4": {"Spiral\\RoadRunner\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON> (wolfy-j)", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "homepage": "https://github.com/rustatian"}, {"name": "<PERSON><PERSON><PERSON> (roxblnfk)", "homepage": "https://github.com/roxblnfk"}, {"name": "<PERSON> (but<PERSON><PERSON>)", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON> (msmakouz)", "email": "<EMAIL>"}, {"name": "RoadRunner Community", "homepage": "https://github.com/roadrunner-server/roadrunner/graphs/contributors"}], "description": "RoadRunner: PHP worker", "homepage": "https://spiral.dev/", "support": {"chat": "https://discord.gg/V6EK4he", "docs": "https://docs.roadrunner.dev", "issues": "https://github.com/roadrunner-server/roadrunner/issues", "source": "https://github.com/roadrunner-php/worker/tree/v3.6.2"}, "funding": [{"url": "https://github.com/sponsors/roadrunner-server", "type": "github"}], "time": "2025-05-05T12:34:50+00:00"}, {"name": "spiral/temporal-bridge", "version": "3.3.0", "source": {"type": "git", "url": "https://github.com/spiral/temporal-bridge.git", "reference": "a0c81dfe4c5cfabebd78e0d0a5509db11358535b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/spiral/temporal-bridge/zipball/a0c81dfe4c5cfabebd78e0d0a5509db11358535b", "reference": "a0c81dfe4c5cfabebd78e0d0a5509db11358535b", "shasum": ""}, "require": {"php": ">=8.1", "spiral/attributes": "^2.8 || ^3.1.5", "spiral/boot": "^3.14", "spiral/core": "^3.14", "spiral/roadrunner-bridge": "^3.7 || ^4.0", "spiral/scaffolder": "^3.14", "spiral/tokenizer": "^3.14", "temporal/sdk": "^2.13"}, "require-dev": {"spiral/code-style": "^2.2.0", "spiral/dumper": "^3.3", "spiral/framework": "^3.14", "spiral/testing": "^2.8", "vimeo/psalm": "^6.8"}, "type": "library", "extra": {"spiral": {"bootloaders": ["Spiral\\TemporalBridge\\Bootloader\\TemporalBridgeBootloader"]}}, "autoload": {"psr-4": {"Spiral\\TemporalBridge\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON> (wolfy-j)", "email": "<EMAIL>"}, {"name": "<PERSON> (but<PERSON><PERSON>)", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON> (roxblnfk)", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON> (msmakouz)", "email": "<EMAIL>"}], "description": "Temporal integration package for Spiral Framework", "homepage": "https://spiral.dev", "keywords": ["php", "spiral", "spiral-framework", "temporal", "workflow"], "support": {"chat": "https://discord.gg/V6EK4he", "docs": "https://spiral.dev/docs", "issues": "https://github.com/spiral/temporal-bridge/issues", "source": "https://github.com/spiral/temporal-bridge"}, "funding": [{"url": "https://github.com/sponsors/spiral", "type": "github"}], "time": "2025-02-28T16:32:09+00:00"}, {"name": "spiral/validator", "version": "1.5.4", "source": {"type": "git", "url": "https://github.com/spiral/validator.git", "reference": "1588b3d005d2529af21f308f885d8a5a717c8813"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/spiral/validator/zipball/1588b3d005d2529af21f308f885d8a5a717c8813", "reference": "1588b3d005d2529af21f308f885d8a5a717c8813", "shasum": ""}, "require": {"ext-json": "*", "php": ">=8.1", "spiral/core": "^3.6", "spiral/files": "^3.1", "spiral/filters": "^3.1", "spiral/streams": "^3.1", "spiral/translator": "^3.1", "spiral/validation": "^3.1"}, "require-dev": {"phpunit/phpunit": "^10.5", "spiral/code-style": "^2.2", "spiral/hmvc": "^3.1", "spiral/testing": "^2.0", "vimeo/psalm": "^5.26"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Spiral\\Validator\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Nested validation, Checkers, Conditional Validation", "homepage": "https://spiral.dev", "support": {"issues": "https://github.com/spiral/validator/issues", "source": "https://github.com/spiral/validator"}, "funding": [{"url": "https://github.com/sponsors/spiral", "type": "github"}], "time": "2025-01-24T10:27:42+00:00"}, {"name": "symfony/clock", "version": "v7.3.0", "source": {"type": "git", "url": "https://github.com/symfony/clock.git", "reference": "b81435fbd6648ea425d1ee96a2d8e68f4ceacd24"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/clock/zipball/b81435fbd6648ea425d1ee96a2d8e68f4ceacd24", "reference": "b81435fbd6648ea425d1ee96a2d8e68f4ceacd24", "shasum": ""}, "require": {"php": ">=8.2", "psr/clock": "^1.0", "symfony/polyfill-php83": "^1.28"}, "provide": {"psr/clock-implementation": "1.0"}, "type": "library", "autoload": {"files": ["Resources/now.php"], "psr-4": {"Symfony\\Component\\Clock\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Decouples applications from the system clock", "homepage": "https://symfony.com", "keywords": ["clock", "psr20", "time"], "support": {"source": "https://github.com/symfony/clock/tree/v7.3.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-25T14:21:43+00:00"}, {"name": "symfony/console", "version": "v7.3.3", "source": {"type": "git", "url": "https://github.com/symfony/console.git", "reference": "cb0102a1c5ac3807cf3fdf8bea96007df7fdbea7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/console/zipball/cb0102a1c5ac3807cf3fdf8bea96007df7fdbea7", "reference": "cb0102a1c5ac3807cf3fdf8bea96007df7fdbea7", "shasum": ""}, "require": {"php": ">=8.2", "symfony/deprecation-contracts": "^2.5|^3", "symfony/polyfill-mbstring": "~1.0", "symfony/service-contracts": "^2.5|^3", "symfony/string": "^7.2"}, "conflict": {"symfony/dependency-injection": "<6.4", "symfony/dotenv": "<6.4", "symfony/event-dispatcher": "<6.4", "symfony/lock": "<6.4", "symfony/process": "<6.4"}, "provide": {"psr/log-implementation": "1.0|2.0|3.0"}, "require-dev": {"psr/log": "^1|^2|^3", "symfony/config": "^6.4|^7.0", "symfony/dependency-injection": "^6.4|^7.0", "symfony/event-dispatcher": "^6.4|^7.0", "symfony/http-foundation": "^6.4|^7.0", "symfony/http-kernel": "^6.4|^7.0", "symfony/lock": "^6.4|^7.0", "symfony/messenger": "^6.4|^7.0", "symfony/process": "^6.4|^7.0", "symfony/stopwatch": "^6.4|^7.0", "symfony/var-dumper": "^6.4|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Console\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Eases the creation of beautiful and testable command line interfaces", "homepage": "https://symfony.com", "keywords": ["cli", "command-line", "console", "terminal"], "support": {"source": "https://github.com/symfony/console/tree/v7.3.3"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://github.com/nicolas-grekas", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-08-25T06:35:40+00:00"}, {"name": "symfony/deprecation-contracts", "version": "v3.6.0", "source": {"type": "git", "url": "https://github.com/symfony/deprecation-contracts.git", "reference": "63afe740e99a13ba87ec199bb07bbdee937a5b62"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/deprecation-contracts/zipball/63afe740e99a13ba87ec199bb07bbdee937a5b62", "reference": "63afe740e99a13ba87ec199bb07bbdee937a5b62", "shasum": ""}, "require": {"php": ">=8.1"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/contracts", "name": "symfony/contracts"}, "branch-alias": {"dev-main": "3.6-dev"}}, "autoload": {"files": ["function.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "A generic function and convention to trigger deprecation notices", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/deprecation-contracts/tree/v3.6.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-25T14:21:43+00:00"}, {"name": "symfony/event-dispatcher", "version": "v7.3.3", "source": {"type": "git", "url": "https://github.com/symfony/event-dispatcher.git", "reference": "b7dc69e71de420ac04bc9ab830cf3ffebba48191"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/b7dc69e71de420ac04bc9ab830cf3ffebba48191", "reference": "b7dc69e71de420ac04bc9ab830cf3ffebba48191", "shasum": ""}, "require": {"php": ">=8.2", "symfony/event-dispatcher-contracts": "^2.5|^3"}, "conflict": {"symfony/dependency-injection": "<6.4", "symfony/service-contracts": "<2.5"}, "provide": {"psr/event-dispatcher-implementation": "1.0", "symfony/event-dispatcher-implementation": "2.0|3.0"}, "require-dev": {"psr/log": "^1|^2|^3", "symfony/config": "^6.4|^7.0", "symfony/dependency-injection": "^6.4|^7.0", "symfony/error-handler": "^6.4|^7.0", "symfony/expression-language": "^6.4|^7.0", "symfony/http-foundation": "^6.4|^7.0", "symfony/service-contracts": "^2.5|^3", "symfony/stopwatch": "^6.4|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\EventDispatcher\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides tools that allow your application components to communicate with each other by dispatching events and listening to them", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v7.3.3"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://github.com/nicolas-grekas", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-08-13T11:49:31+00:00"}, {"name": "symfony/event-dispatcher-contracts", "version": "v3.6.0", "source": {"type": "git", "url": "https://github.com/symfony/event-dispatcher-contracts.git", "reference": "59eb412e93815df44f05f342958efa9f46b1e586"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/event-dispatcher-contracts/zipball/59eb412e93815df44f05f342958efa9f46b1e586", "reference": "59eb412e93815df44f05f342958efa9f46b1e586", "shasum": ""}, "require": {"php": ">=8.1", "psr/event-dispatcher": "^1"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/contracts", "name": "symfony/contracts"}, "branch-alias": {"dev-main": "3.6-dev"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\EventDispatcher\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to dispatching event", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "support": {"source": "https://github.com/symfony/event-dispatcher-contracts/tree/v3.6.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-25T14:21:43+00:00"}, {"name": "symfony/filesystem", "version": "v7.3.2", "source": {"type": "git", "url": "https://github.com/symfony/filesystem.git", "reference": "edcbb768a186b5c3f25d0643159a787d3e63b7fd"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/filesystem/zipball/edcbb768a186b5c3f25d0643159a787d3e63b7fd", "reference": "edcbb768a186b5c3f25d0643159a787d3e63b7fd", "shasum": ""}, "require": {"php": ">=8.2", "symfony/polyfill-ctype": "~1.8", "symfony/polyfill-mbstring": "~1.8"}, "require-dev": {"symfony/process": "^6.4|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Filesystem\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides basic utilities for the filesystem", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/filesystem/tree/v7.3.2"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://github.com/nicolas-grekas", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-07-07T08:17:47+00:00"}, {"name": "symfony/finder", "version": "v7.3.2", "source": {"type": "git", "url": "https://github.com/symfony/finder.git", "reference": "2a6614966ba1074fa93dae0bc804227422df4dfe"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/finder/zipball/2a6614966ba1074fa93dae0bc804227422df4dfe", "reference": "2a6614966ba1074fa93dae0bc804227422df4dfe", "shasum": ""}, "require": {"php": ">=8.2"}, "require-dev": {"symfony/filesystem": "^6.4|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Finder\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Finds files and directories via an intuitive fluent interface", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/finder/tree/v7.3.2"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://github.com/nicolas-grekas", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-07-15T13:41:35+00:00"}, {"name": "symfony/http-client", "version": "v7.3.3", "source": {"type": "git", "url": "https://github.com/symfony/http-client.git", "reference": "333b9bd7639cbdaecd25a3a48a9d2dcfaa86e019"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/http-client/zipball/333b9bd7639cbdaecd25a3a48a9d2dcfaa86e019", "reference": "333b9bd7639cbdaecd25a3a48a9d2dcfaa86e019", "shasum": ""}, "require": {"php": ">=8.2", "psr/log": "^1|^2|^3", "symfony/deprecation-contracts": "^2.5|^3", "symfony/http-client-contracts": "~3.4.4|^3.5.2", "symfony/polyfill-php83": "^1.29", "symfony/service-contracts": "^2.5|^3"}, "conflict": {"amphp/amp": "<2.5", "amphp/socket": "<1.1", "php-http/discovery": "<1.15", "symfony/http-foundation": "<6.4"}, "provide": {"php-http/async-client-implementation": "*", "php-http/client-implementation": "*", "psr/http-client-implementation": "1.0", "symfony/http-client-implementation": "3.0"}, "require-dev": {"amphp/http-client": "^4.2.1|^5.0", "amphp/http-tunnel": "^1.0|^2.0", "guzzlehttp/promises": "^1.4|^2.0", "nyholm/psr7": "^1.0", "php-http/httplug": "^1.0|^2.0", "psr/http-client": "^1.0", "symfony/amphp-http-client-meta": "^1.0|^2.0", "symfony/dependency-injection": "^6.4|^7.0", "symfony/http-kernel": "^6.4|^7.0", "symfony/messenger": "^6.4|^7.0", "symfony/process": "^6.4|^7.0", "symfony/rate-limiter": "^6.4|^7.0", "symfony/stopwatch": "^6.4|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\HttpClient\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides powerful methods to fetch HTTP resources synchronously or asynchronously", "homepage": "https://symfony.com", "keywords": ["http"], "support": {"source": "https://github.com/symfony/http-client/tree/v7.3.3"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://github.com/nicolas-grekas", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-08-27T07:45:05+00:00"}, {"name": "symfony/http-client-contracts", "version": "v3.6.0", "source": {"type": "git", "url": "https://github.com/symfony/http-client-contracts.git", "reference": "75d7043853a42837e68111812f4d964b01e5101c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/http-client-contracts/zipball/75d7043853a42837e68111812f4d964b01e5101c", "reference": "75d7043853a42837e68111812f4d964b01e5101c", "shasum": ""}, "require": {"php": ">=8.1"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/contracts", "name": "symfony/contracts"}, "branch-alias": {"dev-main": "3.6-dev"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\HttpClient\\": ""}, "exclude-from-classmap": ["/Test/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to HTTP clients", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "support": {"source": "https://github.com/symfony/http-client-contracts/tree/v3.6.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-04-29T11:18:49+00:00"}, {"name": "symfony/mailer", "version": "v7.3.3", "source": {"type": "git", "url": "https://github.com/symfony/mailer.git", "reference": "a32f3f45f1990db8c4341d5122a7d3a381c7e575"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/mailer/zipball/a32f3f45f1990db8c4341d5122a7d3a381c7e575", "reference": "a32f3f45f1990db8c4341d5122a7d3a381c7e575", "shasum": ""}, "require": {"egulias/email-validator": "^2.1.10|^3|^4", "php": ">=8.2", "psr/event-dispatcher": "^1", "psr/log": "^1|^2|^3", "symfony/event-dispatcher": "^6.4|^7.0", "symfony/mime": "^7.2", "symfony/service-contracts": "^2.5|^3"}, "conflict": {"symfony/http-client-contracts": "<2.5", "symfony/http-kernel": "<6.4", "symfony/messenger": "<6.4", "symfony/mime": "<6.4", "symfony/twig-bridge": "<6.4"}, "require-dev": {"symfony/console": "^6.4|^7.0", "symfony/http-client": "^6.4|^7.0", "symfony/messenger": "^6.4|^7.0", "symfony/twig-bridge": "^6.4|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Mailer\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Helps sending emails", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/mailer/tree/v7.3.3"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://github.com/nicolas-grekas", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-08-13T11:49:31+00:00"}, {"name": "symfony/mime", "version": "v7.3.2", "source": {"type": "git", "url": "https://github.com/symfony/mime.git", "reference": "e0a0f859148daf1edf6c60b398eb40bfc96697d1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/mime/zipball/e0a0f859148daf1edf6c60b398eb40bfc96697d1", "reference": "e0a0f859148daf1edf6c60b398eb40bfc96697d1", "shasum": ""}, "require": {"php": ">=8.2", "symfony/polyfill-intl-idn": "^1.10", "symfony/polyfill-mbstring": "^1.0"}, "conflict": {"egulias/email-validator": "~3.0.0", "phpdocumentor/reflection-docblock": "<3.2.2", "phpdocumentor/type-resolver": "<1.4.0", "symfony/mailer": "<6.4", "symfony/serializer": "<6.4.3|>7.0,<7.0.3"}, "require-dev": {"egulias/email-validator": "^2.1.10|^3.1|^4", "league/html-to-markdown": "^5.0", "phpdocumentor/reflection-docblock": "^3.0|^4.0|^5.0", "symfony/dependency-injection": "^6.4|^7.0", "symfony/process": "^6.4|^7.0", "symfony/property-access": "^6.4|^7.0", "symfony/property-info": "^6.4|^7.0", "symfony/serializer": "^6.4.3|^7.0.3"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Mime\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Allows manipulating MIME messages", "homepage": "https://symfony.com", "keywords": ["mime", "mime-type"], "support": {"source": "https://github.com/symfony/mime/tree/v7.3.2"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://github.com/nicolas-grekas", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-07-15T13:41:35+00:00"}, {"name": "symfony/polyfill-ctype", "version": "v1.33.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-ctype.git", "reference": "a3cc8b044a6ea513310cbd48ef7333b384945638"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-ctype/zipball/a3cc8b044a6ea513310cbd48ef7333b384945638", "reference": "a3cc8b044a6ea513310cbd48ef7333b384945638", "shasum": ""}, "require": {"php": ">=7.2"}, "provide": {"ext-ctype": "*"}, "suggest": {"ext-ctype": "For best performance"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Ctype\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for ctype functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "ctype", "polyfill", "portable"], "support": {"source": "https://github.com/symfony/polyfill-ctype/tree/v1.33.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://github.com/nicolas-grekas", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-09T11:45:10+00:00"}, {"name": "symfony/polyfill-intl-grapheme", "version": "v1.33.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-intl-grapheme.git", "reference": "380872130d3a5dd3ace2f4010d95125fde5d5c70"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-intl-grapheme/zipball/380872130d3a5dd3ace2f4010d95125fde5d5c70", "reference": "380872130d3a5dd3ace2f4010d95125fde5d5c70", "shasum": ""}, "require": {"php": ">=7.2"}, "suggest": {"ext-intl": "For best performance"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Intl\\Grapheme\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's grapheme_* functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "grapheme", "intl", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-intl-grapheme/tree/v1.33.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://github.com/nicolas-grekas", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-06-27T09:58:17+00:00"}, {"name": "symfony/polyfill-intl-idn", "version": "v1.33.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-intl-idn.git", "reference": "9614ac4d8061dc257ecc64cba1b140873dce8ad3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-intl-idn/zipball/9614ac4d8061dc257ecc64cba1b140873dce8ad3", "reference": "9614ac4d8061dc257ecc64cba1b140873dce8ad3", "shasum": ""}, "require": {"php": ">=7.2", "symfony/polyfill-intl-normalizer": "^1.10"}, "suggest": {"ext-intl": "For best performance"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Intl\\Idn\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's idn_to_ascii and idn_to_utf8 functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "idn", "intl", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-intl-idn/tree/v1.33.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://github.com/nicolas-grekas", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-10T14:38:51+00:00"}, {"name": "symfony/polyfill-intl-normalizer", "version": "v1.33.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-intl-normalizer.git", "reference": "3833d7255cc303546435cb650316bff708a1c75c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-intl-normalizer/zipball/3833d7255cc303546435cb650316bff708a1c75c", "reference": "3833d7255cc303546435cb650316bff708a1c75c", "shasum": ""}, "require": {"php": ">=7.2"}, "suggest": {"ext-intl": "For best performance"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Intl\\Normalizer\\": ""}, "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's Normalizer class and related functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "intl", "normalizer", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-intl-normalizer/tree/v1.33.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://github.com/nicolas-grekas", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-09T11:45:10+00:00"}, {"name": "symfony/polyfill-mbstring", "version": "v1.33.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-mbstring.git", "reference": "6d857f4d76bd4b343eac26d6b539585d2bc56493"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-mbstring/zipball/6d857f4d76bd4b343eac26d6b539585d2bc56493", "reference": "6d857f4d76bd4b343eac26d6b539585d2bc56493", "shasum": ""}, "require": {"ext-iconv": "*", "php": ">=7.2"}, "provide": {"ext-mbstring": "*"}, "suggest": {"ext-mbstring": "For best performance"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Mbstring\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for the Mbstring extension", "homepage": "https://symfony.com", "keywords": ["compatibility", "mbstring", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-mbstring/tree/v1.33.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://github.com/nicolas-grekas", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-12-23T08:48:59+00:00"}, {"name": "symfony/polyfill-php80", "version": "v1.33.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php80.git", "reference": "0cc9dd0f17f61d8131e7df6b84bd344899fe2608"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php80/zipball/0cc9dd0f17f61d8131e7df6b84bd344899fe2608", "reference": "0cc9dd0f17f61d8131e7df6b84bd344899fe2608", "shasum": ""}, "require": {"php": ">=7.2"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Php80\\": ""}, "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 8.0+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-php80/tree/v1.33.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://github.com/nicolas-grekas", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-01-02T08:10:11+00:00"}, {"name": "symfony/polyfill-php83", "version": "v1.33.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php83.git", "reference": "17f6f9a6b1735c0f163024d959f700cfbc5155e5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php83/zipball/17f6f9a6b1735c0f163024d959f700cfbc5155e5", "reference": "17f6f9a6b1735c0f163024d959f700cfbc5155e5", "shasum": ""}, "require": {"php": ">=7.2"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Php83\\": ""}, "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 8.3+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-php83/tree/v1.33.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://github.com/nicolas-grekas", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-07-08T02:45:35+00:00"}, {"name": "symfony/polyfill-php84", "version": "v1.33.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php84.git", "reference": "d8ced4d875142b6a7426000426b8abc631d6b191"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php84/zipball/d8ced4d875142b6a7426000426b8abc631d6b191", "reference": "d8ced4d875142b6a7426000426b8abc631d6b191", "shasum": ""}, "require": {"php": ">=7.2"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Php84\\": ""}, "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 8.4+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-php84/tree/v1.33.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://github.com/nicolas-grekas", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-06-24T13:30:11+00:00"}, {"name": "symfony/process", "version": "v7.3.3", "source": {"type": "git", "url": "https://github.com/symfony/process.git", "reference": "32241012d521e2e8a9d713adb0812bb773b907f1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/process/zipball/32241012d521e2e8a9d713adb0812bb773b907f1", "reference": "32241012d521e2e8a9d713adb0812bb773b907f1", "shasum": ""}, "require": {"php": ">=8.2"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Process\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Executes commands in sub-processes", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/process/tree/v7.3.3"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://github.com/nicolas-grekas", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-08-18T09:42:54+00:00"}, {"name": "symfony/service-contracts", "version": "v3.6.0", "source": {"type": "git", "url": "https://github.com/symfony/service-contracts.git", "reference": "f021b05a130d35510bd6b25fe9053c2a8a15d5d4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/service-contracts/zipball/f021b05a130d35510bd6b25fe9053c2a8a15d5d4", "reference": "f021b05a130d35510bd6b25fe9053c2a8a15d5d4", "shasum": ""}, "require": {"php": ">=8.1", "psr/container": "^1.1|^2.0", "symfony/deprecation-contracts": "^2.5|^3"}, "conflict": {"ext-psr": "<1.1|>=2"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/contracts", "name": "symfony/contracts"}, "branch-alias": {"dev-main": "3.6-dev"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\Service\\": ""}, "exclude-from-classmap": ["/Test/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to writing services", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "support": {"source": "https://github.com/symfony/service-contracts/tree/v3.6.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-04-25T09:37:31+00:00"}, {"name": "symfony/string", "version": "v7.3.3", "source": {"type": "git", "url": "https://github.com/symfony/string.git", "reference": "17a426cce5fd1f0901fefa9b2a490d0038fd3c9c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/string/zipball/17a426cce5fd1f0901fefa9b2a490d0038fd3c9c", "reference": "17a426cce5fd1f0901fefa9b2a490d0038fd3c9c", "shasum": ""}, "require": {"php": ">=8.2", "symfony/polyfill-ctype": "~1.8", "symfony/polyfill-intl-grapheme": "~1.0", "symfony/polyfill-intl-normalizer": "~1.0", "symfony/polyfill-mbstring": "~1.0"}, "conflict": {"symfony/translation-contracts": "<2.5"}, "require-dev": {"symfony/emoji": "^7.1", "symfony/error-handler": "^6.4|^7.0", "symfony/http-client": "^6.4|^7.0", "symfony/intl": "^6.4|^7.0", "symfony/translation-contracts": "^2.5|^3.0", "symfony/var-exporter": "^6.4|^7.0"}, "type": "library", "autoload": {"files": ["Resources/functions.php"], "psr-4": {"Symfony\\Component\\String\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides an object-oriented API to strings and deals with bytes, UTF-8 code points and grapheme clusters in a unified way", "homepage": "https://symfony.com", "keywords": ["grapheme", "i18n", "string", "unicode", "utf-8", "utf8"], "support": {"source": "https://github.com/symfony/string/tree/v7.3.3"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://github.com/nicolas-grekas", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-08-25T06:35:40+00:00"}, {"name": "symfony/translation", "version": "v7.3.3", "source": {"type": "git", "url": "https://github.com/symfony/translation.git", "reference": "e0837b4cbcef63c754d89a4806575cada743a38d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/translation/zipball/e0837b4cbcef63c754d89a4806575cada743a38d", "reference": "e0837b4cbcef63c754d89a4806575cada743a38d", "shasum": ""}, "require": {"php": ">=8.2", "symfony/deprecation-contracts": "^2.5|^3", "symfony/polyfill-mbstring": "~1.0", "symfony/translation-contracts": "^2.5|^3.0"}, "conflict": {"nikic/php-parser": "<5.0", "symfony/config": "<6.4", "symfony/console": "<6.4", "symfony/dependency-injection": "<6.4", "symfony/http-client-contracts": "<2.5", "symfony/http-kernel": "<6.4", "symfony/service-contracts": "<2.5", "symfony/twig-bundle": "<6.4", "symfony/yaml": "<6.4"}, "provide": {"symfony/translation-implementation": "2.3|3.0"}, "require-dev": {"nikic/php-parser": "^5.0", "psr/log": "^1|^2|^3", "symfony/config": "^6.4|^7.0", "symfony/console": "^6.4|^7.0", "symfony/dependency-injection": "^6.4|^7.0", "symfony/finder": "^6.4|^7.0", "symfony/http-client-contracts": "^2.5|^3.0", "symfony/http-kernel": "^6.4|^7.0", "symfony/intl": "^6.4|^7.0", "symfony/polyfill-intl-icu": "^1.21", "symfony/routing": "^6.4|^7.0", "symfony/service-contracts": "^2.5|^3", "symfony/yaml": "^6.4|^7.0"}, "type": "library", "autoload": {"files": ["Resources/functions.php"], "psr-4": {"Symfony\\Component\\Translation\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides tools to internationalize your application", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/translation/tree/v7.3.3"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://github.com/nicolas-grekas", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-08-01T21:02:37+00:00"}, {"name": "symfony/translation-contracts", "version": "v3.6.0", "source": {"type": "git", "url": "https://github.com/symfony/translation-contracts.git", "reference": "df210c7a2573f1913b2d17cc95f90f53a73d8f7d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/translation-contracts/zipball/df210c7a2573f1913b2d17cc95f90f53a73d8f7d", "reference": "df210c7a2573f1913b2d17cc95f90f53a73d8f7d", "shasum": ""}, "require": {"php": ">=8.1"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/contracts", "name": "symfony/contracts"}, "branch-alias": {"dev-main": "3.6-dev"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\Translation\\": ""}, "exclude-from-classmap": ["/Test/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to translation", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "support": {"source": "https://github.com/symfony/translation-contracts/tree/v3.6.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-27T08:32:26+00:00"}, {"name": "symfony/yaml", "version": "v7.3.3", "source": {"type": "git", "url": "https://github.com/symfony/yaml.git", "reference": "d4f4a66866fe2451f61296924767280ab5732d9d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/yaml/zipball/d4f4a66866fe2451f61296924767280ab5732d9d", "reference": "d4f4a66866fe2451f61296924767280ab5732d9d", "shasum": ""}, "require": {"php": ">=8.2", "symfony/deprecation-contracts": "^2.5|^3.0", "symfony/polyfill-ctype": "^1.8"}, "conflict": {"symfony/console": "<6.4"}, "require-dev": {"symfony/console": "^6.4|^7.0"}, "bin": ["Resources/bin/yaml-lint"], "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Yaml\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Loads and dumps YAML files", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/yaml/tree/v7.3.3"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://github.com/nicolas-grekas", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-08-27T11:34:33+00:00"}, {"name": "temporal/sdk", "version": "v2.15.1", "source": {"type": "git", "url": "https://github.com/temporalio/sdk-php.git", "reference": "eb956d60abea64ad40e8a1052be88fb4fa4e7cf0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/temporalio/sdk-php/zipball/eb956d60abea64ad40e8a1052be88fb4fa4e7cf0", "reference": "eb956d60abea64ad40e8a1052be88fb4fa4e7cf0", "shasum": ""}, "require": {"ext-curl": "*", "ext-json": "*", "google/common-protos": "^1.4 || ^2.2 || ^3.2 || ^4.9", "google/protobuf": "^3.25.5 || ^4.29.3", "grpc/grpc": "^1.57", "nesbot/carbon": "^2.72.6 || ^3.8.4", "php": ">=8.1", "psr/log": "^2.0 || ^3.0.2", "ramsey/uuid": "^4.7.6", "react/promise": "^2.11", "roadrunner-php/roadrunner-api-dto": "^1.12.0", "roadrunner-php/version-checker": "^1.0.1", "spiral/attributes": "^3.1.8", "spiral/roadrunner": "^2025.1.2", "spiral/roadrunner-cli": "^2.6", "spiral/roadrunner-kv": "^4.3.1", "spiral/roadrunner-worker": "^3.6.2", "symfony/filesystem": "^5.4.45 || ^6.4.13 || ^7.0", "symfony/http-client": "^5.4.49 || ^6.4.17 || ^7.0", "symfony/polyfill-php83": "^1.31.0", "symfony/process": "^5.4.47 || ^6.4.15 || ^7.0"}, "require-dev": {"buggregator/trap": "^1.13.0", "composer/composer": "^2.8.4", "dereuromark/composer-prefer-lowest": "^0.1.10", "doctrine/annotations": "^1.14.4 || ^2.0.2", "internal/dload": "^1.2.0", "jetbrains/phpstorm-attributes": "dev-master", "laminas/laminas-code": "^4.16", "phpunit/phpunit": "10.5.45", "spiral/code-style": "~2.2.2", "spiral/core": "^3.14.9", "ta-tikoma/phpunit-architecture-test": "^0.8.5", "vimeo/psalm": "^5.26.1 || ^6.2"}, "suggest": {"buggregator/trap": "For better debugging", "ext-grpc": "For Client calls", "ext-protobuf": "For better performance"}, "type": "library", "autoload": {"psr-4": {"Temporal\\": "src", "Temporal\\Testing\\": "testing/src", "Temporal\\Api\\Testservice\\": "testing/api/testservice/Temporal/Api/Testservice", "GPBMetadata\\Temporal\\Api\\Testservice\\": "testing/api/testservice/GPBMetadata/Temporal/Api/Testservice"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Temporal SDK", "homepage": "https://temporal.io", "keywords": ["activity", "api", "event-sourcing", "library", "sdk", "service-bus", "temporal", "workflow"], "support": {"docs": "https://docs.temporal.io", "forum": "https://community.temporal.io", "issues": "https://github.com/temporalio/sdk-php/issues", "source": "https://github.com/temporalio/sdk-php"}, "time": "2025-07-21T13:50:07+00:00"}, {"name": "vlucas/phpdotenv", "version": "v5.6.2", "source": {"type": "git", "url": "https://github.com/vlucas/phpdotenv.git", "reference": "24ac4c74f91ee2c193fa1aaa5c249cb0822809af"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/vlucas/phpdotenv/zipball/24ac4c74f91ee2c193fa1aaa5c249cb0822809af", "reference": "24ac4c74f91ee2c193fa1aaa5c249cb0822809af", "shasum": ""}, "require": {"ext-pcre": "*", "graham-campbell/result-type": "^1.1.3", "php": "^7.2.5 || ^8.0", "phpoption/phpoption": "^1.9.3", "symfony/polyfill-ctype": "^1.24", "symfony/polyfill-mbstring": "^1.24", "symfony/polyfill-php80": "^1.24"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.8.2", "ext-filter": "*", "phpunit/phpunit": "^8.5.34 || ^9.6.13 || ^10.4.2"}, "suggest": {"ext-filter": "Required to use the boolean validator."}, "type": "library", "extra": {"bamarni-bin": {"bin-links": true, "forward-command": false}, "branch-alias": {"dev-master": "5.6-dev"}}, "autoload": {"psr-4": {"Dotenv\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/vlucas"}], "description": "Loads environment variables from `.env` to `getenv()`, `$_ENV` and `$_SERVER` automagically.", "keywords": ["dotenv", "env", "environment"], "support": {"issues": "https://github.com/vlucas/phpdotenv/issues", "source": "https://github.com/vlucas/phpdotenv/tree/v5.6.2"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/vlucas/phpdotenv", "type": "tidelift"}], "time": "2025-04-30T23:37:27+00:00"}, {"name": "webmozart/assert", "version": "1.11.0", "source": {"type": "git", "url": "https://github.com/webmozarts/assert.git", "reference": "11cb2199493b2f8a3b53e7f19068fc6aac760991"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/webmozarts/assert/zipball/11cb2199493b2f8a3b53e7f19068fc6aac760991", "reference": "11cb2199493b2f8a3b53e7f19068fc6aac760991", "shasum": ""}, "require": {"ext-ctype": "*", "php": "^7.2 || ^8.0"}, "conflict": {"phpstan/phpstan": "<0.12.20", "vimeo/psalm": "<4.6.1 || 4.6.2"}, "require-dev": {"phpunit/phpunit": "^8.5.13"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.10-dev"}}, "autoload": {"psr-4": {"Webmozart\\Assert\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "b<PERSON><PERSON><PERSON>@gmail.com"}], "description": "Assertions to validate method input/output with nice error messages.", "keywords": ["assert", "check", "validate"], "support": {"issues": "https://github.com/webmozarts/assert/issues", "source": "https://github.com/webmozarts/assert/tree/1.11.0"}, "time": "2022-06-03T18:03:27+00:00"}, {"name": "yiisoft/error-handler", "version": "4.1.0", "source": {"type": "git", "url": "https://github.com/yiisoft/error-handler.git", "reference": "c99e42f8be647740ac99964f11684aace2b96c30"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/yiisoft/error-handler/zipball/c99e42f8be647740ac99964f11684aace2b96c30", "reference": "c99e42f8be647740ac99964f11684aace2b96c30", "shasum": ""}, "require": {"alexkart/curl-builder": "^1.0", "cebe/markdown": "^1.2", "ext-dom": "*", "ext-mbstring": "*", "php": "~8.1.0 || ~8.2.0 || ~8.3.0 || ~8.4.0", "psr/container": "^1.0|^2.0", "psr/http-factory": "^1.0", "psr/http-message": "^1.0|^2.0", "psr/http-server-handler": "^1.0", "psr/http-server-middleware": "^1.0", "psr/log": "^1.1|^2.0|^3.0", "yiisoft/friendly-exception": "^1.0", "yiisoft/http": "^1.2", "yiisoft/injector": "^1.0"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.8.2", "httpsoft/http-message": "^1.1.6", "phpunit/phpunit": "^10.5.45", "psr/event-dispatcher": "^1.0", "rector/rector": "^2.0.11", "roave/infection-static-analysis-plugin": "^1.35", "spatie/phpunit-watcher": "^1.24", "vimeo/psalm": "^5.26.1 || ^6.9.1", "yiisoft/di": "^1.3", "yiisoft/test-support": "^3.0.2"}, "type": "library", "extra": {"bamarni-bin": {"bin-links": true, "forward-command": true, "target-directory": "tools"}, "config-plugin": {"di-web": "di-web.php"}, "config-plugin-options": {"source-directory": "config"}}, "autoload": {"psr-4": {"Yiisoft\\ErrorHandler\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "description": "<PERSON><PERSON> Handling Library", "homepage": "https://www.yiiframework.com/", "keywords": ["PSR-11", "error-handler", "psr-15", "psr-3", "psr-7", "yiisoft"], "support": {"chat": "https://t.me/yii3en", "forum": "https://www.yiiframework.com/forum/", "irc": "ircs://irc.libera.chat:6697/yii", "issues": "https://github.com/yiisoft/error-handler/issues?state=open", "source": "https://github.com/yiisoft/error-handler", "wiki": "https://www.yiiframework.com/wiki/"}, "funding": [{"url": "https://github.com/sponsors/yiisoft", "type": "github"}, {"url": "https://opencollective.com/yiisoft", "type": "opencollective"}], "time": "2025-04-18T08:13:06+00:00"}, {"name": "yiisoft/friendly-exception", "version": "1.1.0", "source": {"type": "git", "url": "https://github.com/yiisoft/friendly-exception.git", "reference": "4b4a19edff251791e3c92d4d83435d2716351ff4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/yiisoft/friendly-exception/zipball/4b4a19edff251791e3c92d4d83435d2716351ff4", "reference": "4b4a19edff251791e3c92d4d83435d2716351ff4", "shasum": ""}, "require": {"php": "^7.1|^8.0"}, "require-dev": {"phpunit/phpunit": "^9.4", "roave/infection-static-analysis-plugin": "^1.5", "spatie/phpunit-watcher": "^1.23", "vimeo/psalm": "^4.3"}, "type": "library", "autoload": {"psr-4": {"Yiisoft\\FriendlyException\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "description": "An interface for friendlier exception", "homepage": "http://www.yiiframework.com/", "keywords": ["error handling", "exception", "exceptions", "friendly"], "support": {"forum": "http://www.yiiframework.com/forum/", "irc": "irc://irc.freenode.net/yii", "issues": "https://github.com/yiisoft/friendly-exception/issues?state=open", "source": "https://github.com/yiisoft/friendly-exception", "wiki": "http://www.yiiframework.com/wiki/"}, "funding": [{"url": "https://github.com/yiisoft", "type": "github"}, {"url": "https://opencollective.com/yiisoft", "type": "open_collective"}], "time": "2021-10-26T21:43:25+00:00"}, {"name": "yiisoft/http", "version": "1.2.0", "source": {"type": "git", "url": "https://github.com/yiisoft/http.git", "reference": "7967b2c45dbf6af785dea4ea481af23da6d49c5d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/yiisoft/http/zipball/7967b2c45dbf6af785dea4ea481af23da6d49c5d", "reference": "7967b2c45dbf6af785dea4ea481af23da6d49c5d", "shasum": ""}, "require": {"php": "^7.4|^8.0", "yiisoft/strings": "^2.0"}, "require-dev": {"phpunit/phpunit": "^9.5", "roave/infection-static-analysis-plugin": "^1.10", "spatie/phpunit-watcher": "^1.23", "vimeo/psalm": "^4.12"}, "type": "library", "autoload": {"psr-4": {"Yiisoft\\Http\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "description": "Constants and PSR-7 PhpStorm meta for HTTP protocol headers, methods and statuses", "homepage": "https://www.yiiframework.com/", "keywords": ["header", "http", "method", "psr-17", "psr-7", "status"], "support": {"forum": "https://www.yiiframework.com/forum/", "irc": "irc://irc.freenode.net/yii", "issues": "https://github.com/yiisoft/http/issues?state=open", "source": "https://github.com/yiisoft/http", "wiki": "https://www.yiiframework.com/wiki/"}, "funding": [{"url": "https://github.com/yiisoft", "type": "github"}, {"url": "https://opencollective.com/yiisoft", "type": "open_collective"}], "time": "2021-11-09T09:42:11+00:00"}, {"name": "yiisoft/injector", "version": "1.2.0", "source": {"type": "git", "url": "https://github.com/yiisoft/injector.git", "reference": "0dc0127a7542341bdaabda7b85204e992938b83e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/yiisoft/injector/zipball/0dc0127a7542341bdaabda7b85204e992938b83e", "reference": "0dc0127a7542341bdaabda7b85204e992938b83e", "shasum": ""}, "require": {"php": "^7.4|^8.0"}, "require-dev": {"maglnet/composer-require-checker": "^3.8|^4.2", "phpbench/phpbench": "^1.1", "phpunit/phpunit": "^9.5", "psr/container": "^1.0|^2.0", "rector/rector": "^0.18.12", "roave/infection-static-analysis-plugin": "^1.16", "spatie/phpunit-watcher": "^1.23", "vimeo/psalm": "^4.30|^5.7", "yiisoft/test-support": "^1.2"}, "suggest": {"psr/container": "For automatic resolving of dependencies"}, "type": "library", "autoload": {"psr-4": {"Yiisoft\\Injector\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "description": "PSR-11 compatible injector. Executes a callable and makes an instances by injecting dependencies from a given DI container.", "homepage": "https://www.yiiframework.com/", "keywords": ["PSR-11", "dependency injection", "di", "injector", "reflection"], "support": {"chat": "https://t.me/yii3en", "forum": "https://www.yiiframework.com/forum/", "irc": "irc://irc.freenode.net/yii", "issues": "https://github.com/yiisoft/injector/issues?state=open", "source": "https://github.com/yiisoft/injector", "wiki": "https://www.yiiframework.com/wiki/"}, "funding": [{"url": "https://github.com/yiisoft", "type": "github"}, {"url": "https://opencollective.com/yiisoft", "type": "open_collective"}], "time": "2023-12-20T09:39:03+00:00"}, {"name": "yiisoft/strings", "version": "2.6.0", "source": {"type": "git", "url": "https://github.com/yiisoft/strings.git", "reference": "eeb087d96ab8ae35c5b17daafe5d8ad9522880ab"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/yiisoft/strings/zipball/eeb087d96ab8ae35c5b17daafe5d8ad9522880ab", "reference": "eeb087d96ab8ae35c5b17daafe5d8ad9522880ab", "shasum": ""}, "require": {"ext-mbstring": "*", "php": "~8.1.0 || ~8.2.0 || ~8.3.0 || ~8.4.0"}, "require-dev": {"maglnet/composer-require-checker": "^4.7.1", "phpunit/phpunit": "^10.5.45", "rector/rector": "^2.0.8", "roave/infection-static-analysis-plugin": "^1.35", "spatie/phpunit-watcher": "^1.24", "vimeo/psalm": "^5.26.1|^6.4.1"}, "type": "library", "autoload": {"psr-4": {"Yiisoft\\Strings\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "description": "<PERSON><PERSON>er", "homepage": "https://www.yiiframework.com/", "keywords": ["helper", "string", "yii"], "support": {"chat": "https://t.me/yii3en", "forum": "https://www.yiiframework.com/forum/", "irc": "ircs://irc.libera.chat:6697/yii", "issues": "https://github.com/yiisoft/strings/issues?state=open", "source": "https://github.com/yiisoft/strings", "wiki": "https://www.yiiframework.com/wiki/"}, "funding": [{"url": "https://github.com/sponsors/yiisoft", "type": "github"}, {"url": "https://opencollective.com/yiisoft", "type": "opencollective"}], "time": "2025-02-09T16:30:52+00:00"}], "packages-dev": [{"name": "amphp/amp", "version": "v3.1.1", "source": {"type": "git", "url": "https://github.com/amphp/amp.git", "reference": "fa0ab33a6f47a82929c38d03ca47ebb71086a93f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/amphp/amp/zipball/fa0ab33a6f47a82929c38d03ca47ebb71086a93f", "reference": "fa0ab33a6f47a82929c38d03ca47ebb71086a93f", "shasum": ""}, "require": {"php": ">=8.1", "revolt/event-loop": "^1 || ^0.2"}, "require-dev": {"amphp/php-cs-fixer-config": "^2", "phpunit/phpunit": "^9", "psalm/phar": "5.23.1"}, "type": "library", "autoload": {"files": ["src/functions.php", "src/Future/functions.php", "src/Internal/functions.php"], "psr-4": {"Amp\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A non-blocking concurrency framework for PHP applications.", "homepage": "https://amphp.org/amp", "keywords": ["async", "asynchronous", "awaitable", "concurrency", "event", "event-loop", "future", "non-blocking", "promise"], "support": {"issues": "https://github.com/amphp/amp/issues", "source": "https://github.com/amphp/amp/tree/v3.1.1"}, "funding": [{"url": "https://github.com/amphp", "type": "github"}], "time": "2025-08-27T21:42:00+00:00"}, {"name": "amphp/byte-stream", "version": "v2.1.2", "source": {"type": "git", "url": "https://github.com/amphp/byte-stream.git", "reference": "55a6bd071aec26fa2a3e002618c20c35e3df1b46"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/amphp/byte-stream/zipball/55a6bd071aec26fa2a3e002618c20c35e3df1b46", "reference": "55a6bd071aec26fa2a3e002618c20c35e3df1b46", "shasum": ""}, "require": {"amphp/amp": "^3", "amphp/parser": "^1.1", "amphp/pipeline": "^1", "amphp/serialization": "^1", "amphp/sync": "^2", "php": ">=8.1", "revolt/event-loop": "^1 || ^0.2.3"}, "require-dev": {"amphp/php-cs-fixer-config": "^2", "amphp/phpunit-util": "^3", "phpunit/phpunit": "^9", "psalm/phar": "5.22.1"}, "type": "library", "autoload": {"files": ["src/functions.php", "src/Internal/functions.php"], "psr-4": {"Amp\\ByteStream\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "A stream abstraction to make working with non-blocking I/O simple.", "homepage": "https://amphp.org/byte-stream", "keywords": ["amp", "amphp", "async", "io", "non-blocking", "stream"], "support": {"issues": "https://github.com/amphp/byte-stream/issues", "source": "https://github.com/amphp/byte-stream/tree/v2.1.2"}, "funding": [{"url": "https://github.com/amphp", "type": "github"}], "time": "2025-03-16T17:10:27+00:00"}, {"name": "amphp/cache", "version": "v2.0.1", "source": {"type": "git", "url": "https://github.com/amphp/cache.git", "reference": "46912e387e6aa94933b61ea1ead9cf7540b7797c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/amphp/cache/zipball/46912e387e6aa94933b61ea1ead9cf7540b7797c", "reference": "46912e387e6aa94933b61ea1ead9cf7540b7797c", "shasum": ""}, "require": {"amphp/amp": "^3", "amphp/serialization": "^1", "amphp/sync": "^2", "php": ">=8.1", "revolt/event-loop": "^1 || ^0.2"}, "require-dev": {"amphp/php-cs-fixer-config": "^2", "amphp/phpunit-util": "^3", "phpunit/phpunit": "^9", "psalm/phar": "^5.4"}, "type": "library", "autoload": {"psr-4": {"Amp\\Cache\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A fiber-aware cache API based on Amp and Revolt.", "homepage": "https://amphp.org/cache", "support": {"issues": "https://github.com/amphp/cache/issues", "source": "https://github.com/amphp/cache/tree/v2.0.1"}, "funding": [{"url": "https://github.com/amphp", "type": "github"}], "time": "2024-04-19T03:38:06+00:00"}, {"name": "amphp/dns", "version": "v2.4.0", "source": {"type": "git", "url": "https://github.com/amphp/dns.git", "reference": "78eb3db5fc69bf2fc0cb503c4fcba667bc223c71"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/amphp/dns/zipball/78eb3db5fc69bf2fc0cb503c4fcba667bc223c71", "reference": "78eb3db5fc69bf2fc0cb503c4fcba667bc223c71", "shasum": ""}, "require": {"amphp/amp": "^3", "amphp/byte-stream": "^2", "amphp/cache": "^2", "amphp/parser": "^1", "amphp/process": "^2", "daverandom/libdns": "^2.0.2", "ext-filter": "*", "ext-json": "*", "php": ">=8.1", "revolt/event-loop": "^1 || ^0.2"}, "require-dev": {"amphp/php-cs-fixer-config": "^2", "amphp/phpunit-util": "^3", "phpunit/phpunit": "^9", "psalm/phar": "5.20"}, "type": "library", "autoload": {"files": ["src/functions.php"], "psr-4": {"Amp\\Dns\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Async DNS resolution for Amp.", "homepage": "https://github.com/amphp/dns", "keywords": ["amp", "amphp", "async", "client", "dns", "resolve"], "support": {"issues": "https://github.com/amphp/dns/issues", "source": "https://github.com/amphp/dns/tree/v2.4.0"}, "funding": [{"url": "https://github.com/amphp", "type": "github"}], "time": "2025-01-19T15:43:40+00:00"}, {"name": "amphp/parallel", "version": "v2.3.2", "source": {"type": "git", "url": "https://github.com/amphp/parallel.git", "reference": "321b45ae771d9c33a068186b24117e3cd1c48dce"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/amphp/parallel/zipball/321b45ae771d9c33a068186b24117e3cd1c48dce", "reference": "321b45ae771d9c33a068186b24117e3cd1c48dce", "shasum": ""}, "require": {"amphp/amp": "^3", "amphp/byte-stream": "^2", "amphp/cache": "^2", "amphp/parser": "^1", "amphp/pipeline": "^1", "amphp/process": "^2", "amphp/serialization": "^1", "amphp/socket": "^2", "amphp/sync": "^2", "php": ">=8.1", "revolt/event-loop": "^1"}, "require-dev": {"amphp/php-cs-fixer-config": "^2", "amphp/phpunit-util": "^3", "phpunit/phpunit": "^9", "psalm/phar": "^5.18"}, "type": "library", "autoload": {"files": ["src/Context/functions.php", "src/Context/Internal/functions.php", "src/Ipc/functions.php", "src/Worker/functions.php"], "psr-4": {"Amp\\Parallel\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Parallel processing component for Amp.", "homepage": "https://github.com/amphp/parallel", "keywords": ["async", "asynchronous", "concurrent", "multi-processing", "multi-threading"], "support": {"issues": "https://github.com/amphp/parallel/issues", "source": "https://github.com/amphp/parallel/tree/v2.3.2"}, "funding": [{"url": "https://github.com/amphp", "type": "github"}], "time": "2025-08-27T21:55:40+00:00"}, {"name": "amphp/parser", "version": "v1.1.1", "source": {"type": "git", "url": "https://github.com/amphp/parser.git", "reference": "3cf1f8b32a0171d4b1bed93d25617637a77cded7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/amphp/parser/zipball/3cf1f8b32a0171d4b1bed93d25617637a77cded7", "reference": "3cf1f8b32a0171d4b1bed93d25617637a77cded7", "shasum": ""}, "require": {"php": ">=7.4"}, "require-dev": {"amphp/php-cs-fixer-config": "^2", "phpunit/phpunit": "^9", "psalm/phar": "^5.4"}, "type": "library", "autoload": {"psr-4": {"Amp\\Parser\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "A generator parser to make streaming parsers simple.", "homepage": "https://github.com/amphp/parser", "keywords": ["async", "non-blocking", "parser", "stream"], "support": {"issues": "https://github.com/amphp/parser/issues", "source": "https://github.com/amphp/parser/tree/v1.1.1"}, "funding": [{"url": "https://github.com/amphp", "type": "github"}], "time": "2024-03-21T19:16:53+00:00"}, {"name": "amphp/pipeline", "version": "v1.2.3", "source": {"type": "git", "url": "https://github.com/amphp/pipeline.git", "reference": "7b52598c2e9105ebcddf247fc523161581930367"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/amphp/pipeline/zipball/7b52598c2e9105ebcddf247fc523161581930367", "reference": "7b52598c2e9105ebcddf247fc523161581930367", "shasum": ""}, "require": {"amphp/amp": "^3", "php": ">=8.1", "revolt/event-loop": "^1"}, "require-dev": {"amphp/php-cs-fixer-config": "^2", "amphp/phpunit-util": "^3", "phpunit/phpunit": "^9", "psalm/phar": "^5.18"}, "type": "library", "autoload": {"psr-4": {"Amp\\Pipeline\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Asynchronous iterators and operators.", "homepage": "https://amphp.org/pipeline", "keywords": ["amp", "amphp", "async", "io", "iterator", "non-blocking"], "support": {"issues": "https://github.com/amphp/pipeline/issues", "source": "https://github.com/amphp/pipeline/tree/v1.2.3"}, "funding": [{"url": "https://github.com/amphp", "type": "github"}], "time": "2025-03-16T16:33:53+00:00"}, {"name": "amphp/process", "version": "v2.0.3", "source": {"type": "git", "url": "https://github.com/amphp/process.git", "reference": "52e08c09dec7511d5fbc1fb00d3e4e79fc77d58d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/amphp/process/zipball/52e08c09dec7511d5fbc1fb00d3e4e79fc77d58d", "reference": "52e08c09dec7511d5fbc1fb00d3e4e79fc77d58d", "shasum": ""}, "require": {"amphp/amp": "^3", "amphp/byte-stream": "^2", "amphp/sync": "^2", "php": ">=8.1", "revolt/event-loop": "^1 || ^0.2"}, "require-dev": {"amphp/php-cs-fixer-config": "^2", "amphp/phpunit-util": "^3", "phpunit/phpunit": "^9", "psalm/phar": "^5.4"}, "type": "library", "autoload": {"files": ["src/functions.php"], "psr-4": {"Amp\\Process\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "A fiber-aware process manager based on Amp and Revolt.", "homepage": "https://amphp.org/process", "support": {"issues": "https://github.com/amphp/process/issues", "source": "https://github.com/amphp/process/tree/v2.0.3"}, "funding": [{"url": "https://github.com/amphp", "type": "github"}], "time": "2024-04-19T03:13:44+00:00"}, {"name": "amphp/serialization", "version": "v1.0.0", "source": {"type": "git", "url": "https://github.com/amphp/serialization.git", "reference": "693e77b2fb0b266c3c7d622317f881de44ae94a1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/amphp/serialization/zipball/693e77b2fb0b266c3c7d622317f881de44ae94a1", "reference": "693e77b2fb0b266c3c7d622317f881de44ae94a1", "shasum": ""}, "require": {"php": ">=7.1"}, "require-dev": {"amphp/php-cs-fixer-config": "dev-master", "phpunit/phpunit": "^9 || ^8 || ^7"}, "type": "library", "autoload": {"files": ["src/functions.php"], "psr-4": {"Amp\\Serialization\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Serialization tools for IPC and data storage in PHP.", "homepage": "https://github.com/amphp/serialization", "keywords": ["async", "asynchronous", "serialization", "serialize"], "support": {"issues": "https://github.com/amphp/serialization/issues", "source": "https://github.com/amphp/serialization/tree/master"}, "time": "2020-03-25T21:39:07+00:00"}, {"name": "amphp/socket", "version": "v2.3.1", "source": {"type": "git", "url": "https://github.com/amphp/socket.git", "reference": "58e0422221825b79681b72c50c47a930be7bf1e1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/amphp/socket/zipball/58e0422221825b79681b72c50c47a930be7bf1e1", "reference": "58e0422221825b79681b72c50c47a930be7bf1e1", "shasum": ""}, "require": {"amphp/amp": "^3", "amphp/byte-stream": "^2", "amphp/dns": "^2", "ext-openssl": "*", "kelunik/certificate": "^1.1", "league/uri": "^6.5 | ^7", "league/uri-interfaces": "^2.3 | ^7", "php": ">=8.1", "revolt/event-loop": "^1 || ^0.2"}, "require-dev": {"amphp/php-cs-fixer-config": "^2", "amphp/phpunit-util": "^3", "amphp/process": "^2", "phpunit/phpunit": "^9", "psalm/phar": "5.20"}, "type": "library", "autoload": {"files": ["src/functions.php", "src/Internal/functions.php", "src/SocketAddress/functions.php"], "psr-4": {"Amp\\Socket\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Non-blocking socket connection / server implementations based on Amp and Revolt.", "homepage": "https://github.com/amphp/socket", "keywords": ["amp", "async", "encryption", "non-blocking", "sockets", "tcp", "tls"], "support": {"issues": "https://github.com/amphp/socket/issues", "source": "https://github.com/amphp/socket/tree/v2.3.1"}, "funding": [{"url": "https://github.com/amphp", "type": "github"}], "time": "2024-04-21T14:33:03+00:00"}, {"name": "amphp/sync", "version": "v2.3.0", "source": {"type": "git", "url": "https://github.com/amphp/sync.git", "reference": "217097b785130d77cfcc58ff583cf26cd1770bf1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/amphp/sync/zipball/217097b785130d77cfcc58ff583cf26cd1770bf1", "reference": "217097b785130d77cfcc58ff583cf26cd1770bf1", "shasum": ""}, "require": {"amphp/amp": "^3", "amphp/pipeline": "^1", "amphp/serialization": "^1", "php": ">=8.1", "revolt/event-loop": "^1 || ^0.2"}, "require-dev": {"amphp/php-cs-fixer-config": "^2", "amphp/phpunit-util": "^3", "phpunit/phpunit": "^9", "psalm/phar": "5.23"}, "type": "library", "autoload": {"files": ["src/functions.php"], "psr-4": {"Amp\\Sync\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Non-blocking synchronization primitives for PHP based on Amp and Revolt.", "homepage": "https://github.com/amphp/sync", "keywords": ["async", "asynchronous", "mutex", "semaphore", "synchronization"], "support": {"issues": "https://github.com/amphp/sync/issues", "source": "https://github.com/amphp/sync/tree/v2.3.0"}, "funding": [{"url": "https://github.com/amphp", "type": "github"}], "time": "2024-08-03T19:31:26+00:00"}, {"name": "buggregator/trap", "version": "1.13.18", "source": {"type": "git", "url": "https://github.com/buggregator/trap.git", "reference": "1560e7da26691baa2aace06cbd575cc9e8b06624"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/buggregator/trap/zipball/1560e7da26691baa2aace06cbd575cc9e8b06624", "reference": "1560e7da26691baa2aace06cbd575cc9e8b06624", "shasum": ""}, "require": {"clue/stream-filter": "^1.6", "ext-filter": "*", "ext-sockets": "*", "internal/destroy": "^1.0", "nyholm/psr7": "^1.8", "php": ">=8.1", "php-http/message": "^1.15", "psr/container": "^1.1 || ^2.0", "psr/http-message": "^1.1 || ^2", "symfony/console": "^6.4 || ^7", "symfony/var-dumper": "^6.3 || ^7", "yiisoft/injector": "^1.2"}, "require-dev": {"dereuromark/composer-prefer-lowest": "^0.1.10", "ergebnis/phpunit-slow-test-detector": "^2.14", "google/protobuf": "^3.25 || ^4.30", "phpunit/phpunit": "^10.5.10", "rector/rector": "^1.1", "roxblnfk/unpoly": "^1.8.1", "spiral/code-style": "^2.2.2", "ta-tikoma/phpunit-architecture-test": "^0.8.4", "vimeo/psalm": "^6.5"}, "suggest": {"ext-simplexml": "To load trap.xml", "roxblnfk/unpoly": "If you want to remove unnecessary PHP polyfills depend on PHP version."}, "bin": ["bin/trap"], "type": "library", "autoload": {"files": ["src/functions.php"], "psr-4": {"Buggregator\\Trap\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON> (roxblnfk)", "homepage": "https://github.com/roxblnfk"}, {"name": "<PERSON> (but<PERSON><PERSON>)", "homepage": "https://github.com/butschster"}], "description": "A simple and powerful tool for debugging PHP applications.", "homepage": "https://buggregator.dev/", "keywords": ["Fibers", "WebSockets", "binary dump", "cli", "console", "debug", "dev", "dump", "dumper", "helper", "sentry", "server", "smtp"], "support": {"issues": "https://github.com/buggregator/trap/issues", "source": "https://github.com/buggregator/trap/tree/1.13.18"}, "funding": [{"url": "https://boosty.to/roxblnfk", "type": "boosty"}, {"url": "https://patreon.com/roxblnfk", "type": "patreon"}], "time": "2025-09-08T19:47:24+00:00"}, {"name": "butschster/entity-faker", "version": "v2.0.0", "source": {"type": "git", "url": "https://github.com/butschster/entity-faker.git", "reference": "64349ff1e9d7f96aacc99781ea18db163ee366ab"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/butschster/entity-faker/zipball/64349ff1e9d7f96aacc99781ea18db163ee366ab", "reference": "64349ff1e9d7f96aacc99781ea18db163ee366ab", "shasum": ""}, "require-dev": {"fakerphp/faker": "^1.19", "laminas/laminas-hydrator": "^4.2", "mockery/mockery": "^1.5", "php": "^8.1", "phpunit/phpunit": "^9.5"}, "type": "library", "autoload": {"psr-4": {"Butschster\\Tests\\": "tests", "Butschster\\EntityFaker\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Create fake entities based on your classes", "keywords": ["entity-generator", "faker", "php", "seeder"], "support": {"issues": "https://github.com/butschster/entity-faker/issues", "source": "https://github.com/butschster/entity-faker/tree/v2.0.0"}, "time": "2022-07-13T20:13:15+00:00"}, {"name": "clue/stream-filter", "version": "v1.7.0", "source": {"type": "git", "url": "https://github.com/clue/stream-filter.git", "reference": "049509fef80032cb3f051595029ab75b49a3c2f7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/clue/stream-filter/zipball/049509fef80032cb3f051595029ab75b49a3c2f7", "reference": "049509fef80032cb3f051595029ab75b49a3c2f7", "shasum": ""}, "require": {"php": ">=5.3"}, "require-dev": {"phpunit/phpunit": "^9.6 || ^5.7 || ^4.8.36"}, "type": "library", "autoload": {"files": ["src/functions_include.php"], "psr-4": {"Clue\\StreamFilter\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A simple and modern approach to stream filtering in PHP", "homepage": "https://github.com/clue/stream-filter", "keywords": ["bucket brigade", "callback", "filter", "php_user_filter", "stream", "stream_filter_append", "stream_filter_register"], "support": {"issues": "https://github.com/clue/stream-filter/issues", "source": "https://github.com/clue/stream-filter/tree/v1.7.0"}, "funding": [{"url": "https://clue.engineering/support", "type": "custom"}, {"url": "https://github.com/clue", "type": "github"}], "time": "2023-12-20T15:40:13+00:00"}, {"name": "composer/pcre", "version": "3.3.2", "source": {"type": "git", "url": "https://github.com/composer/pcre.git", "reference": "b2bed4734f0cc156ee1fe9c0da2550420d99a21e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/composer/pcre/zipball/b2bed4734f0cc156ee1fe9c0da2550420d99a21e", "reference": "b2bed4734f0cc156ee1fe9c0da2550420d99a21e", "shasum": ""}, "require": {"php": "^7.4 || ^8.0"}, "conflict": {"phpstan/phpstan": "<1.11.10"}, "require-dev": {"phpstan/phpstan": "^1.12 || ^2", "phpstan/phpstan-strict-rules": "^1 || ^2", "phpunit/phpunit": "^8 || ^9"}, "type": "library", "extra": {"phpstan": {"includes": ["extension.neon"]}, "branch-alias": {"dev-main": "3.x-dev"}}, "autoload": {"psr-4": {"Composer\\Pcre\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "http://seld.be"}], "description": "PCRE wrapping library that offers type-safe preg_* replacements.", "keywords": ["PCRE", "preg", "regex", "regular expression"], "support": {"issues": "https://github.com/composer/pcre/issues", "source": "https://github.com/composer/pcre/tree/3.3.2"}, "funding": [{"url": "https://packagist.com", "type": "custom"}, {"url": "https://github.com/composer", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/composer/composer", "type": "tidelift"}], "time": "2024-11-12T16:29:46+00:00"}, {"name": "composer/xdebug-handler", "version": "3.0.5", "source": {"type": "git", "url": "https://github.com/composer/xdebug-handler.git", "reference": "6c1925561632e83d60a44492e0b344cf48ab85ef"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/composer/xdebug-handler/zipball/6c1925561632e83d60a44492e0b344cf48ab85ef", "reference": "6c1925561632e83d60a44492e0b344cf48ab85ef", "shasum": ""}, "require": {"composer/pcre": "^1 || ^2 || ^3", "php": "^7.2.5 || ^8.0", "psr/log": "^1 || ^2 || ^3"}, "require-dev": {"phpstan/phpstan": "^1.0", "phpstan/phpstan-strict-rules": "^1.1", "phpunit/phpunit": "^8.5 || ^9.6 || ^10.5"}, "type": "library", "autoload": {"psr-4": {"Composer\\XdebugHandler\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "john-s<PERSON><PERSON><PERSON>@blueyonder.co.uk"}], "description": "Restarts a process without Xdebug.", "keywords": ["Xdebug", "performance"], "support": {"irc": "ircs://irc.libera.chat:6697/composer", "issues": "https://github.com/composer/xdebug-handler/issues", "source": "https://github.com/composer/xdebug-handler/tree/3.0.5"}, "funding": [{"url": "https://packagist.com", "type": "custom"}, {"url": "https://github.com/composer", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/composer/composer", "type": "tidelift"}], "time": "2024-05-06T16:37:16+00:00"}, {"name": "danog/advanced-json-rpc", "version": "v3.2.2", "source": {"type": "git", "url": "https://github.com/danog/php-advanced-json-rpc.git", "reference": "aadb1c4068a88c3d0530cfe324b067920661efcb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/danog/php-advanced-json-rpc/zipball/aadb1c4068a88c3d0530cfe324b067920661efcb", "reference": "aadb1c4068a88c3d0530cfe324b067920661efcb", "shasum": ""}, "require": {"netresearch/jsonmapper": "^5", "php": ">=8.1", "phpdocumentor/reflection-docblock": "^4.3.4 || ^5.0.0"}, "replace": {"felixfbecker/php-advanced-json-rpc": "^3"}, "require-dev": {"phpunit/phpunit": "^9"}, "type": "library", "autoload": {"psr-4": {"AdvancedJsonRpc\\": "lib/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["ISC"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "A more advanced JSONRPC implementation", "support": {"issues": "https://github.com/danog/php-advanced-json-rpc/issues", "source": "https://github.com/danog/php-advanced-json-rpc/tree/v3.2.2"}, "time": "2025-02-14T10:55:15+00:00"}, {"name": "daverandom/libdns", "version": "v2.1.0", "source": {"type": "git", "url": "https://github.com/DaveRandom/LibDNS.git", "reference": "b84c94e8fe6b7ee4aecfe121bfe3b6177d303c8a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/DaveRandom/LibDNS/zipball/b84c94e8fe6b7ee4aecfe121bfe3b6177d303c8a", "reference": "b84c94e8fe6b7ee4aecfe121bfe3b6177d303c8a", "shasum": ""}, "require": {"ext-ctype": "*", "php": ">=7.1"}, "suggest": {"ext-intl": "Required for IDN support"}, "type": "library", "autoload": {"files": ["src/functions.php"], "psr-4": {"LibDNS\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "DNS protocol implementation written in pure PHP", "keywords": ["dns"], "support": {"issues": "https://github.com/DaveRandom/LibDNS/issues", "source": "https://github.com/DaveRandom/LibDNS/tree/v2.1.0"}, "time": "2024-04-12T12:12:48+00:00"}, {"name": "dnoegel/php-xdg-base-dir", "version": "v0.1.1", "source": {"type": "git", "url": "https://github.com/dnoegel/php-xdg-base-dir.git", "reference": "8f8a6e48c5ecb0f991c2fdcf5f154a47d85f9ffd"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/dnoegel/php-xdg-base-dir/zipball/8f8a6e48c5ecb0f991c2fdcf5f154a47d85f9ffd", "reference": "8f8a6e48c5ecb0f991c2fdcf5f154a47d85f9ffd", "shasum": ""}, "require": {"php": ">=5.3.2"}, "require-dev": {"phpunit/phpunit": "~7.0|~6.0|~5.0|~4.8.35"}, "type": "library", "autoload": {"psr-4": {"XdgBaseDir\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "implementation of xdg base directory specification for php", "support": {"issues": "https://github.com/dnoegel/php-xdg-base-dir/issues", "source": "https://github.com/dnoegel/php-xdg-base-dir/tree/v0.1.1"}, "time": "2019-12-04T15:06:13+00:00"}, {"name": "fakerphp/faker", "version": "v1.24.1", "source": {"type": "git", "url": "https://github.com/FakerPHP/Faker.git", "reference": "e0ee18eb1e6dc3cda3ce9fd97e5a0689a88a64b5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/FakerPHP/Faker/zipball/e0ee18eb1e6dc3cda3ce9fd97e5a0689a88a64b5", "reference": "e0ee18eb1e6dc3cda3ce9fd97e5a0689a88a64b5", "shasum": ""}, "require": {"php": "^7.4 || ^8.0", "psr/container": "^1.0 || ^2.0", "symfony/deprecation-contracts": "^2.2 || ^3.0"}, "conflict": {"fzaninotto/faker": "*"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.4.1", "doctrine/persistence": "^1.3 || ^2.0", "ext-intl": "*", "phpunit/phpunit": "^9.5.26", "symfony/phpunit-bridge": "^5.4.16"}, "suggest": {"doctrine/orm": "Required to use Faker\\ORM\\Doctrine", "ext-curl": "Required by Faker\\Provider\\Image to download images.", "ext-dom": "Required by Faker\\Provider\\HtmlLorem for generating random HTML.", "ext-iconv": "Required by Faker\\Provider\\ru_RU\\Text::realText() for generating real Russian text.", "ext-mbstring": "Required for multibyte Unicode string functionality."}, "type": "library", "autoload": {"psr-4": {"Faker\\": "src/Faker/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>"}], "description": "Faker is a PHP library that generates fake data for you.", "keywords": ["data", "faker", "fixtures"], "support": {"issues": "https://github.com/FakerPHP/Faker/issues", "source": "https://github.com/FakerPHP/Faker/tree/v1.24.1"}, "time": "2024-11-21T13:46:39+00:00"}, {"name": "felixfbecker/language-server-protocol", "version": "v1.5.3", "source": {"type": "git", "url": "https://github.com/felixfbecker/php-language-server-protocol.git", "reference": "a9e113dbc7d849e35b8776da39edaf4313b7b6c9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/felixfbecker/php-language-server-protocol/zipball/a9e113dbc7d849e35b8776da39edaf4313b7b6c9", "reference": "a9e113dbc7d849e35b8776da39edaf4313b7b6c9", "shasum": ""}, "require": {"php": ">=7.1"}, "require-dev": {"phpstan/phpstan": "*", "squizlabs/php_codesniffer": "^3.1", "vimeo/psalm": "^4.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.x-dev"}}, "autoload": {"psr-4": {"LanguageServerProtocol\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["ISC"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "PHP classes for the Language Server Protocol", "keywords": ["language", "microsoft", "php", "server"], "support": {"issues": "https://github.com/felixfbecker/php-language-server-protocol/issues", "source": "https://github.com/felixfbecker/php-language-server-protocol/tree/v1.5.3"}, "time": "2024-04-30T00:40:11+00:00"}, {"name": "fidry/cpu-core-counter", "version": "1.3.0", "source": {"type": "git", "url": "https://github.com/theofidry/cpu-core-counter.git", "reference": "db9508f7b1474469d9d3c53b86f817e344732678"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/theofidry/cpu-core-counter/zipball/db9508f7b1474469d9d3c53b86f817e344732678", "reference": "db9508f7b1474469d9d3c53b86f817e344732678", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "require-dev": {"fidry/makefile": "^0.2.0", "fidry/php-cs-fixer-config": "^1.1.2", "phpstan/extension-installer": "^1.2.0", "phpstan/phpstan": "^2.0", "phpstan/phpstan-deprecation-rules": "^2.0.0", "phpstan/phpstan-phpunit": "^2.0", "phpstan/phpstan-strict-rules": "^2.0", "phpunit/phpunit": "^8.5.31 || ^9.5.26", "webmozarts/strict-phpunit": "^7.5"}, "type": "library", "autoload": {"psr-4": {"Fidry\\CpuCoreCounter\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Théo FIDRY", "email": "<EMAIL>"}], "description": "Tiny utility to get the number of CPU cores.", "keywords": ["CPU", "core"], "support": {"issues": "https://github.com/theofidry/cpu-core-counter/issues", "source": "https://github.com/theofidry/cpu-core-counter/tree/1.3.0"}, "funding": [{"url": "https://github.com/theofidry", "type": "github"}], "time": "2025-08-14T07:29:31+00:00"}, {"name": "hamcrest/hamcrest-php", "version": "v2.1.1", "source": {"type": "git", "url": "https://github.com/hamcrest/hamcrest-php.git", "reference": "f8b1c0173b22fa6ec77a81fe63e5b01eba7e6487"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/hamcrest/hamcrest-php/zipball/f8b1c0173b22fa6ec77a81fe63e5b01eba7e6487", "reference": "f8b1c0173b22fa6ec77a81fe63e5b01eba7e6487", "shasum": ""}, "require": {"php": "^7.4|^8.0"}, "replace": {"cordoval/hamcrest-php": "*", "davedevelopment/hamcrest-php": "*", "kodova/hamcrest-php": "*"}, "require-dev": {"phpunit/php-file-iterator": "^1.4 || ^2.0 || ^3.0", "phpunit/phpunit": "^4.8.36 || ^5.7 || ^6.5 || ^7.0 || ^8.0 || ^9.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.1-dev"}}, "autoload": {"classmap": ["hamcrest"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "description": "This is the PHP port of Hamcrest Matchers", "keywords": ["test"], "support": {"issues": "https://github.com/hamcrest/hamcrest-php/issues", "source": "https://github.com/hamcrest/hamcrest-php/tree/v2.1.1"}, "time": "2025-04-30T06:54:44+00:00"}, {"name": "internal/destroy", "version": "1.0.0", "source": {"type": "git", "url": "https://github.com/php-internal/destroy.git", "reference": "93068c4f7da218034f5373e31407f564b74b4a06"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-internal/destroy/zipball/93068c4f7da218034f5373e31407f564b74b4a06", "reference": "93068c4f7da218034f5373e31407f564b74b4a06", "shasum": ""}, "require": {"php": ">=8.1"}, "require-dev": {"buggregator/trap": "^1.10", "phpunit/phpunit": "^10.5", "spiral/code-style": "^2.2.2", "ta-tikoma/phpunit-architecture-test": "^0.8.4", "vimeo/psalm": "^6.10"}, "suggest": {"ext-simplexml": "to support XML configs parsing"}, "type": "library", "autoload": {"psr-4": {"Internal\\Destroy\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON> (roxblnfk)", "homepage": "https://github.com/roxblnfk"}], "keywords": ["download binaries", "memory"], "support": {"issues": "https://github.com/php-internal/destroy/issues", "source": "https://github.com/php-internal/destroy/tree/1.0.0"}, "funding": [{"url": "https://patreon.com/roxblnfk", "type": "patreon"}], "time": "2025-09-08T14:29:16+00:00"}, {"name": "jzonta/faker-restaurant", "version": "2.0.1", "source": {"type": "git", "url": "https://github.com/jzonta/FakerRestaurant.git", "reference": "4e10ff08dc8ca08603a3daf7abe48d412b013cf4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/jzonta/FakerRestaurant/zipball/4e10ff08dc8ca08603a3daf7abe48d412b013cf4", "reference": "4e10ff08dc8ca08603a3daf7abe48d412b013cf4", "shasum": ""}, "require": {"fakerphp/faker": "^1.9.1", "php": ">=7.1"}, "require-dev": {"friendsofphp/php-cs-fixer": "^2.17", "phpstan/phpstan": "^0.12.66", "phpunit/phpunit": "^9"}, "type": "library", "autoload": {"psr-4": {"FakerRestaurant\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>"}], "description": "Food and Beverage names generate using fakerphp/faker", "homepage": "https://github.com/jzonta/FakerRestaurant", "support": {"issues": "https://github.com/jzonta/FakerRestaurant/issues", "source": "https://github.com/jzonta/FakerRestaurant/tree/v2.0.1"}, "time": "2021-11-15T11:21:22+00:00"}, {"name": "kelunik/certificate", "version": "v1.1.3", "source": {"type": "git", "url": "https://github.com/kelunik/certificate.git", "reference": "7e00d498c264d5eb4f78c69f41c8bd6719c0199e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kelunik/certificate/zipball/7e00d498c264d5eb4f78c69f41c8bd6719c0199e", "reference": "7e00d498c264d5eb4f78c69f41c8bd6719c0199e", "shasum": ""}, "require": {"ext-openssl": "*", "php": ">=7.0"}, "require-dev": {"amphp/php-cs-fixer-config": "^2", "phpunit/phpunit": "^6 | 7 | ^8 | ^9"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.x-dev"}}, "autoload": {"psr-4": {"Kelunik\\Certificate\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Access certificate details and transform between different formats.", "keywords": ["DER", "certificate", "certificates", "openssl", "pem", "x509"], "support": {"issues": "https://github.com/kelunik/certificate/issues", "source": "https://github.com/kelunik/certificate/tree/v1.1.3"}, "time": "2023-02-03T21:26:53+00:00"}, {"name": "laminas/laminas-hydrator", "version": "4.16.0", "source": {"type": "git", "url": "https://github.com/laminas/laminas-hydrator.git", "reference": "a162bd571924968d67ef1f43aed044b8f9c108ef"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/laminas/laminas-hydrator/zipball/a162bd571924968d67ef1f43aed044b8f9c108ef", "reference": "a162bd571924968d67ef1f43aed044b8f9c108ef", "shasum": ""}, "require": {"laminas/laminas-stdlib": "^3.20", "php": "~8.1.0 || ~8.2.0 || ~8.3.0 || ~8.4.0", "webmozart/assert": "^1.11"}, "conflict": {"laminas/laminas-servicemanager": "<3.14.0", "zendframework/zend-hydrator": "*"}, "require-dev": {"laminas/laminas-coding-standard": "~3.0", "laminas/laminas-eventmanager": "^3.13.1", "laminas/laminas-modulemanager": "^2.16.0", "laminas/laminas-serializer": "^2.17.0", "laminas/laminas-servicemanager": "^3.23.0", "phpbench/phpbench": "^1.3.1", "phpunit/phpunit": "^10.5.38", "psalm/plugin-phpunit": "^0.19.0", "vimeo/psalm": "^5.26.1"}, "suggest": {"laminas/laminas-eventmanager": "^3.13, to support aggregate hydrator usage", "laminas/laminas-serializer": "^2.17, to use the SerializableStrategy", "laminas/laminas-servicemanager": "^3.22, to support hydrator plugin manager usage"}, "type": "library", "extra": {"laminas": {"component": "Laminas\\Hydrator", "config-provider": "Laminas\\Hydrator\\ConfigProvider"}}, "autoload": {"psr-4": {"Laminas\\Hydrator\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "description": "Serialize objects to arrays, and vice versa", "homepage": "https://laminas.dev", "keywords": ["hydrator", "laminas"], "support": {"chat": "https://laminas.dev/chat", "docs": "https://docs.laminas.dev/laminas-hydrator/", "forum": "https://discourse.laminas.dev", "issues": "https://github.com/laminas/laminas-hydrator/issues", "rss": "https://github.com/laminas/laminas-hydrator/releases.atom", "source": "https://github.com/laminas/laminas-hydrator"}, "funding": [{"url": "https://funding.communitybridge.org/projects/laminas-project", "type": "community_bridge"}], "time": "2024-11-13T14:04:02+00:00"}, {"name": "laminas/laminas-stdlib", "version": "3.20.0", "source": {"type": "git", "url": "https://github.com/laminas/laminas-stdlib.git", "reference": "8974a1213be42c3e2f70b2c27b17f910291ab2f4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/laminas/laminas-stdlib/zipball/8974a1213be42c3e2f70b2c27b17f910291ab2f4", "reference": "8974a1213be42c3e2f70b2c27b17f910291ab2f4", "shasum": ""}, "require": {"php": "~8.1.0 || ~8.2.0 || ~8.3.0 || ~8.4.0"}, "conflict": {"zendframework/zend-stdlib": "*"}, "require-dev": {"laminas/laminas-coding-standard": "^3.0", "phpbench/phpbench": "^1.3.1", "phpunit/phpunit": "^10.5.38", "psalm/plugin-phpunit": "^0.19.0", "vimeo/psalm": "^5.26.1"}, "type": "library", "autoload": {"psr-4": {"Laminas\\Stdlib\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "description": "SPL extensions, array utilities, error handlers, and more", "homepage": "https://laminas.dev", "keywords": ["laminas", "stdlib"], "support": {"chat": "https://laminas.dev/chat", "docs": "https://docs.laminas.dev/laminas-stdlib/", "forum": "https://discourse.laminas.dev", "issues": "https://github.com/laminas/laminas-stdlib/issues", "rss": "https://github.com/laminas/laminas-stdlib/releases.atom", "source": "https://github.com/laminas/laminas-stdlib"}, "funding": [{"url": "https://funding.communitybridge.org/projects/laminas-project", "type": "community_bridge"}], "time": "2024-10-29T13:46:07+00:00"}, {"name": "league/uri", "version": "7.5.1", "source": {"type": "git", "url": "https://github.com/thephpleague/uri.git", "reference": "81fb5145d2644324614cc532b28efd0215bda430"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/thephpleague/uri/zipball/81fb5145d2644324614cc532b28efd0215bda430", "reference": "81fb5145d2644324614cc532b28efd0215bda430", "shasum": ""}, "require": {"league/uri-interfaces": "^7.5", "php": "^8.1"}, "conflict": {"league/uri-schemes": "^1.0"}, "suggest": {"ext-bcmath": "to improve IPV4 host parsing", "ext-fileinfo": "to create Data URI from file contennts", "ext-gmp": "to improve IPV4 host parsing", "ext-intl": "to handle IDN host with the best performance", "jeremykendall/php-domain-parser": "to resolve Public Suffix and Top Level Domain", "league/uri-components": "Needed to easily manipulate URI objects components", "php-64bit": "to improve IPV4 host parsing", "symfony/polyfill-intl-idn": "to handle IDN host via the Symfony polyfill if ext-intl is not present"}, "type": "library", "extra": {"branch-alias": {"dev-master": "7.x-dev"}}, "autoload": {"psr-4": {"League\\Uri\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://nyamsprod.com"}], "description": "URI manipulation library", "homepage": "https://uri.thephpleague.com", "keywords": ["data-uri", "file-uri", "ftp", "hostname", "http", "https", "middleware", "parse_str", "parse_url", "psr-7", "query-string", "querystring", "rfc3986", "rfc3987", "rfc6570", "uri", "uri-template", "url", "ws"], "support": {"docs": "https://uri.thephpleague.com", "forum": "https://thephpleague.slack.com", "issues": "https://github.com/thephpleague/uri-src/issues", "source": "https://github.com/thephpleague/uri/tree/7.5.1"}, "funding": [{"url": "https://github.com/sponsors/nyamsprod", "type": "github"}], "time": "2024-12-08T08:40:02+00:00"}, {"name": "league/uri-interfaces", "version": "7.5.0", "source": {"type": "git", "url": "https://github.com/thephpleague/uri-interfaces.git", "reference": "08cfc6c4f3d811584fb09c37e2849e6a7f9b0742"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/thephpleague/uri-interfaces/zipball/08cfc6c4f3d811584fb09c37e2849e6a7f9b0742", "reference": "08cfc6c4f3d811584fb09c37e2849e6a7f9b0742", "shasum": ""}, "require": {"ext-filter": "*", "php": "^8.1", "psr/http-factory": "^1", "psr/http-message": "^1.1 || ^2.0"}, "suggest": {"ext-bcmath": "to improve IPV4 host parsing", "ext-gmp": "to improve IPV4 host parsing", "ext-intl": "to handle IDN host with the best performance", "php-64bit": "to improve IPV4 host parsing", "symfony/polyfill-intl-idn": "to handle IDN host via the Symfony polyfill if ext-intl is not present"}, "type": "library", "extra": {"branch-alias": {"dev-master": "7.x-dev"}}, "autoload": {"psr-4": {"League\\Uri\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://nyamsprod.com"}], "description": "Common interfaces and classes for URI representation and interaction", "homepage": "https://uri.thephpleague.com", "keywords": ["data-uri", "file-uri", "ftp", "hostname", "http", "https", "parse_str", "parse_url", "psr-7", "query-string", "querystring", "rfc3986", "rfc3987", "rfc6570", "uri", "url", "ws"], "support": {"docs": "https://uri.thephpleague.com", "forum": "https://thephpleague.slack.com", "issues": "https://github.com/thephpleague/uri-src/issues", "source": "https://github.com/thephpleague/uri-interfaces/tree/7.5.0"}, "funding": [{"url": "https://github.com/sponsors/nyamsprod", "type": "github"}], "time": "2024-12-08T08:18:47+00:00"}, {"name": "mockery/mockery", "version": "1.6.12", "source": {"type": "git", "url": "https://github.com/mockery/mockery.git", "reference": "1f4efdd7d3beafe9807b08156dfcb176d18f1699"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/mockery/mockery/zipball/1f4efdd7d3beafe9807b08156dfcb176d18f1699", "reference": "1f4efdd7d3beafe9807b08156dfcb176d18f1699", "shasum": ""}, "require": {"hamcrest/hamcrest-php": "^2.0.1", "lib-pcre": ">=7.0", "php": ">=7.3"}, "conflict": {"phpunit/phpunit": "<8.0"}, "require-dev": {"phpunit/phpunit": "^8.5 || ^9.6.17", "symplify/easy-coding-standard": "^12.1.14"}, "type": "library", "autoload": {"files": ["library/helpers.php", "library/Mockery.php"], "psr-4": {"Mockery\\": "library/Mockery"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/padraic", "role": "Author"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://davedevelopment.co.uk", "role": "Developer"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/ghostwriter", "role": "Lead Developer"}], "description": "Mockery is a simple yet flexible PHP mock object framework", "homepage": "https://github.com/mockery/mockery", "keywords": ["BDD", "TDD", "library", "mock", "mock objects", "mockery", "stub", "test", "test double", "testing"], "support": {"docs": "https://docs.mockery.io/", "issues": "https://github.com/mockery/mockery/issues", "rss": "https://github.com/mockery/mockery/releases.atom", "security": "https://github.com/mockery/mockery/security/advisories", "source": "https://github.com/mockery/mockery"}, "time": "2024-05-16T03:13:13+00:00"}, {"name": "netresearch/jsonmapper", "version": "v5.0.0", "source": {"type": "git", "url": "https://github.com/cweiske/jsonmapper.git", "reference": "8c64d8d444a5d764c641ebe97e0e3bc72b25bf6c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/cweiske/jsonmapper/zipball/8c64d8d444a5d764c641ebe97e0e3bc72b25bf6c", "reference": "8c64d8d444a5d764c641ebe97e0e3bc72b25bf6c", "shasum": ""}, "require": {"ext-json": "*", "ext-pcre": "*", "ext-reflection": "*", "ext-spl": "*", "php": ">=7.1"}, "require-dev": {"phpunit/phpunit": "~7.5 || ~8.0 || ~9.0 || ~10.0", "squizlabs/php_codesniffer": "~3.5"}, "type": "library", "autoload": {"psr-0": {"JsonMapper": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["OSL-3.0"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://github.com/cweiske/jsonmapper/", "role": "Developer"}], "description": "Map nested JSON structures onto PHP classes", "support": {"email": "<EMAIL>", "issues": "https://github.com/cweiske/jsonmapper/issues", "source": "https://github.com/cweiske/jsonmapper/tree/v5.0.0"}, "time": "2024-09-08T10:20:00+00:00"}, {"name": "phar-io/manifest", "version": "2.0.4", "source": {"type": "git", "url": "https://github.com/phar-io/manifest.git", "reference": "54750ef60c58e43759730615a392c31c80e23176"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phar-io/manifest/zipball/54750ef60c58e43759730615a392c31c80e23176", "reference": "54750ef60c58e43759730615a392c31c80e23176", "shasum": ""}, "require": {"ext-dom": "*", "ext-libxml": "*", "ext-phar": "*", "ext-xmlwriter": "*", "phar-io/version": "^3.0.1", "php": "^7.2 || ^8.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "Component for reading phar.io manifest information from a PHP Archive (PHAR)", "support": {"issues": "https://github.com/phar-io/manifest/issues", "source": "https://github.com/phar-io/manifest/tree/2.0.4"}, "funding": [{"url": "https://github.com/theseer", "type": "github"}], "time": "2024-03-03T12:33:53+00:00"}, {"name": "phar-io/version", "version": "3.2.1", "source": {"type": "git", "url": "https://github.com/phar-io/version.git", "reference": "4f7fd7836c6f332bb2933569e566a0d6c4cbed74"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phar-io/version/zipball/4f7fd7836c6f332bb2933569e566a0d6c4cbed74", "reference": "4f7fd7836c6f332bb2933569e566a0d6c4cbed74", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "type": "library", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "Library for handling version information and constraints", "support": {"issues": "https://github.com/phar-io/version/issues", "source": "https://github.com/phar-io/version/tree/3.2.1"}, "time": "2022-02-21T01:04:05+00:00"}, {"name": "php-cs-fixer/shim", "version": "v3.87.1", "source": {"type": "git", "url": "https://github.com/PHP-CS-Fixer/shim.git", "reference": "9225ae45f794eea949198f0f8e417b078c006283"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PHP-CS-Fixer/shim/zipball/9225ae45f794eea949198f0f8e417b078c006283", "reference": "9225ae45f794eea949198f0f8e417b078c006283", "shasum": ""}, "require": {"ext-json": "*", "ext-tokenizer": "*", "php": "^7.4 || ^8.0"}, "replace": {"friendsofphp/php-cs-fixer": "self.version"}, "suggest": {"ext-dom": "For handling output formats in XML", "ext-mbstring": "For handling non-UTF8 characters."}, "bin": ["php-cs-fixer", "php-cs-fixer.phar"], "type": "application", "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "A tool to automatically fix PHP code style", "support": {"issues": "https://github.com/PHP-CS-Fixer/shim/issues", "source": "https://github.com/PHP-CS-Fixer/shim/tree/v3.87.1"}, "time": "2025-09-02T15:28:21+00:00"}, {"name": "php-http/message", "version": "1.16.2", "source": {"type": "git", "url": "https://github.com/php-http/message.git", "reference": "06dd5e8562f84e641bf929bfe699ee0f5ce8080a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-http/message/zipball/06dd5e8562f84e641bf929bfe699ee0f5ce8080a", "reference": "06dd5e8562f84e641bf929bfe699ee0f5ce8080a", "shasum": ""}, "require": {"clue/stream-filter": "^1.5", "php": "^7.2 || ^8.0", "psr/http-message": "^1.1 || ^2.0"}, "provide": {"php-http/message-factory-implementation": "1.0"}, "require-dev": {"ergebnis/composer-normalize": "^2.6", "ext-zlib": "*", "guzzlehttp/psr7": "^1.0 || ^2.0", "laminas/laminas-diactoros": "^2.0 || ^3.0", "php-http/message-factory": "^1.0.2", "phpspec/phpspec": "^5.1 || ^6.3 || ^7.1", "slim/slim": "^3.0"}, "suggest": {"ext-zlib": "Used with compressor/decompressor streams", "guzzlehttp/psr7": "Used with Guzzle PSR-7 Factories", "laminas/laminas-diactoros": "Used with Diactoros Factories", "slim/slim": "Used with Slim Framework PSR-7 implementation"}, "type": "library", "autoload": {"files": ["src/filters.php"], "psr-4": {"Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "HTTP Message related tools", "homepage": "http://php-http.org", "keywords": ["http", "message", "psr-7"], "support": {"issues": "https://github.com/php-http/message/issues", "source": "https://github.com/php-http/message/tree/1.16.2"}, "time": "2024-10-02T11:34:13+00:00"}, {"name": "phpdocumentor/reflection-common", "version": "2.2.0", "source": {"type": "git", "url": "https://github.com/phpDocumentor/ReflectionCommon.git", "reference": "1d01c49d4ed62f25aa84a747ad35d5a16924662b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpDocumentor/ReflectionCommon/zipball/1d01c49d4ed62f25aa84a747ad35d5a16924662b", "reference": "1d01c49d4ed62f25aa84a747ad35d5a16924662b", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "type": "library", "extra": {"branch-alias": {"dev-2.x": "2.x-dev"}}, "autoload": {"psr-4": {"phpDocumentor\\Reflection\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Common reflection classes used by phpdocumentor to reflect the code structure", "homepage": "http://www.phpdoc.org", "keywords": ["FQSEN", "phpDocumentor", "phpdoc", "reflection", "static analysis"], "support": {"issues": "https://github.com/phpDocumentor/ReflectionCommon/issues", "source": "https://github.com/phpDocumentor/ReflectionCommon/tree/2.x"}, "time": "2020-06-27T09:03:43+00:00"}, {"name": "phpdocumentor/reflection-docblock", "version": "5.6.3", "source": {"type": "git", "url": "https://github.com/phpDocumentor/ReflectionDocBlock.git", "reference": "94f8051919d1b0369a6bcc7931d679a511c03fe9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpDocumentor/ReflectionDocBlock/zipball/94f8051919d1b0369a6bcc7931d679a511c03fe9", "reference": "94f8051919d1b0369a6bcc7931d679a511c03fe9", "shasum": ""}, "require": {"doctrine/deprecations": "^1.1", "ext-filter": "*", "php": "^7.4 || ^8.0", "phpdocumentor/reflection-common": "^2.2", "phpdocumentor/type-resolver": "^1.7", "phpstan/phpdoc-parser": "^1.7|^2.0", "webmozart/assert": "^1.9.1"}, "require-dev": {"mockery/mockery": "~1.3.5 || ~1.6.0", "phpstan/extension-installer": "^1.1", "phpstan/phpstan": "^1.8", "phpstan/phpstan-mockery": "^1.1", "phpstan/phpstan-webmozart-assert": "^1.2", "phpunit/phpunit": "^9.5", "psalm/phar": "^5.26"}, "type": "library", "extra": {"branch-alias": {"dev-master": "5.x-dev"}}, "autoload": {"psr-4": {"phpDocumentor\\Reflection\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "With this component, a library can provide support for annotations via DocBlocks or otherwise retrieve information that is embedded in a DocBlock.", "support": {"issues": "https://github.com/phpDocumentor/ReflectionDocBlock/issues", "source": "https://github.com/phpDocumentor/ReflectionDocBlock/tree/5.6.3"}, "time": "2025-08-01T19:43:32+00:00"}, {"name": "phpdocumentor/type-resolver", "version": "1.10.0", "source": {"type": "git", "url": "https://github.com/phpDocumentor/TypeResolver.git", "reference": "679e3ce485b99e84c775d28e2e96fade9a7fb50a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpDocumentor/TypeResolver/zipball/679e3ce485b99e84c775d28e2e96fade9a7fb50a", "reference": "679e3ce485b99e84c775d28e2e96fade9a7fb50a", "shasum": ""}, "require": {"doctrine/deprecations": "^1.0", "php": "^7.3 || ^8.0", "phpdocumentor/reflection-common": "^2.0", "phpstan/phpdoc-parser": "^1.18|^2.0"}, "require-dev": {"ext-tokenizer": "*", "phpbench/phpbench": "^1.2", "phpstan/extension-installer": "^1.1", "phpstan/phpstan": "^1.8", "phpstan/phpstan-phpunit": "^1.1", "phpunit/phpunit": "^9.5", "rector/rector": "^0.13.9", "vimeo/psalm": "^4.25"}, "type": "library", "extra": {"branch-alias": {"dev-1.x": "1.x-dev"}}, "autoload": {"psr-4": {"phpDocumentor\\Reflection\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A PSR-5 based resolver of Class names, Types and Structural Element Names", "support": {"issues": "https://github.com/phpDocumentor/TypeResolver/issues", "source": "https://github.com/phpDocumentor/TypeResolver/tree/1.10.0"}, "time": "2024-11-09T15:12:26+00:00"}, {"name": "phpstan/phpdoc-parser", "version": "2.3.0", "source": {"type": "git", "url": "https://github.com/phpstan/phpdoc-parser.git", "reference": "1e0cd5370df5dd2e556a36b9c62f62e555870495"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpstan/phpdoc-parser/zipball/1e0cd5370df5dd2e556a36b9c62f62e555870495", "reference": "1e0cd5370df5dd2e556a36b9c62f62e555870495", "shasum": ""}, "require": {"php": "^7.4 || ^8.0"}, "require-dev": {"doctrine/annotations": "^2.0", "nikic/php-parser": "^5.3.0", "php-parallel-lint/php-parallel-lint": "^1.2", "phpstan/extension-installer": "^1.0", "phpstan/phpstan": "^2.0", "phpstan/phpstan-phpunit": "^2.0", "phpstan/phpstan-strict-rules": "^2.0", "phpunit/phpunit": "^9.6", "symfony/process": "^5.2"}, "type": "library", "autoload": {"psr-4": {"PHPStan\\PhpDocParser\\": ["src/"]}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "PHPDoc parser with support for nullable, intersection and generic types", "support": {"issues": "https://github.com/phpstan/phpdoc-parser/issues", "source": "https://github.com/phpstan/phpdoc-parser/tree/2.3.0"}, "time": "2025-08-30T15:50:23+00:00"}, {"name": "phpunit/php-code-coverage", "version": "10.1.16", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "reference": "7e308268858ed6baedc8704a304727d20bc07c77"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/7e308268858ed6baedc8704a304727d20bc07c77", "reference": "7e308268858ed6baedc8704a304727d20bc07c77", "shasum": ""}, "require": {"ext-dom": "*", "ext-libxml": "*", "ext-xmlwriter": "*", "nikic/php-parser": "^4.19.1 || ^5.1.0", "php": ">=8.1", "phpunit/php-file-iterator": "^4.1.0", "phpunit/php-text-template": "^3.0.1", "sebastian/code-unit-reverse-lookup": "^3.0.0", "sebastian/complexity": "^3.2.0", "sebastian/environment": "^6.1.0", "sebastian/lines-of-code": "^2.0.2", "sebastian/version": "^4.0.1", "theseer/tokenizer": "^1.2.3"}, "require-dev": {"phpunit/phpunit": "^10.1"}, "suggest": {"ext-pcov": "PHP extension that provides line coverage", "ext-xdebug": "PHP extension that provides line coverage as well as branch and path coverage"}, "type": "library", "extra": {"branch-alias": {"dev-main": "10.1.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Library that provides collection, processing, and rendering functionality for PHP code coverage information.", "homepage": "https://github.com/sebastian<PERSON>mann/php-code-coverage", "keywords": ["coverage", "testing", "xunit"], "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/security/policy", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/10.1.16"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2024-08-22T04:31:57+00:00"}, {"name": "phpunit/php-file-iterator", "version": "4.1.0", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator.git", "reference": "a95037b6d9e608ba092da1b23931e537cadc3c3c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-file-iterator/zipball/a95037b6d9e608ba092da1b23931e537cadc3c3c", "reference": "a95037b6d9e608ba092da1b23931e537cadc3c3c", "shasum": ""}, "require": {"php": ">=8.1"}, "require-dev": {"phpunit/phpunit": "^10.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "4.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "FilterIterator implementation that filters files based on a list of suffixes.", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator/", "keywords": ["filesystem", "iterator"], "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator/security/policy", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator/tree/4.1.0"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2023-08-31T06:24:48+00:00"}, {"name": "phpunit/php-invoker", "version": "4.0.0", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/php-invoker.git", "reference": "f5e568ba02fa5ba0ddd0f618391d5a9ea50b06d7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-invoker/zipball/f5e568ba02fa5ba0ddd0f618391d5a9ea50b06d7", "reference": "f5e568ba02fa5ba0ddd0f618391d5a9ea50b06d7", "shasum": ""}, "require": {"php": ">=8.1"}, "require-dev": {"ext-pcntl": "*", "phpunit/phpunit": "^10.0"}, "suggest": {"ext-pcntl": "*"}, "type": "library", "extra": {"branch-alias": {"dev-main": "4.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Invoke callables with a timeout", "homepage": "https://github.com/sebastian<PERSON>mann/php-invoker/", "keywords": ["process"], "support": {"issues": "https://github.com/sebastian<PERSON>mann/php-invoker/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-invoker/tree/4.0.0"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2023-02-03T06:56:09+00:00"}, {"name": "phpunit/php-text-template", "version": "3.0.1", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template.git", "reference": "0c7b06ff49e3d5072f057eb1fa59258bf287a748"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-text-template/zipball/0c7b06ff49e3d5072f057eb1fa59258bf287a748", "reference": "0c7b06ff49e3d5072f057eb1fa59258bf287a748", "shasum": ""}, "require": {"php": ">=8.1"}, "require-dev": {"phpunit/phpunit": "^10.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "3.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Simple template engine.", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template/", "keywords": ["template"], "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template/security/policy", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template/tree/3.0.1"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2023-08-31T14:07:24+00:00"}, {"name": "phpunit/php-timer", "version": "6.0.0", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/php-timer.git", "reference": "e2a2d67966e740530f4a3343fe2e030ffdc1161d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-timer/zipball/e2a2d67966e740530f4a3343fe2e030ffdc1161d", "reference": "e2a2d67966e740530f4a3343fe2e030ffdc1161d", "shasum": ""}, "require": {"php": ">=8.1"}, "require-dev": {"phpunit/phpunit": "^10.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "6.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Utility class for timing", "homepage": "https://github.com/sebastian<PERSON>mann/php-timer/", "keywords": ["timer"], "support": {"issues": "https://github.com/sebastian<PERSON>mann/php-timer/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-timer/tree/6.0.0"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2023-02-03T06:57:52+00:00"}, {"name": "phpunit/phpunit", "version": "10.5.45", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/phpunit.git", "reference": "bd68a781d8e30348bc297449f5234b3458267ae8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/phpunit/zipball/bd68a781d8e30348bc297449f5234b3458267ae8", "reference": "bd68a781d8e30348bc297449f5234b3458267ae8", "shasum": ""}, "require": {"ext-dom": "*", "ext-json": "*", "ext-libxml": "*", "ext-mbstring": "*", "ext-xml": "*", "ext-xmlwriter": "*", "myclabs/deep-copy": "^1.12.1", "phar-io/manifest": "^2.0.4", "phar-io/version": "^3.2.1", "php": ">=8.1", "phpunit/php-code-coverage": "^10.1.16", "phpunit/php-file-iterator": "^4.1.0", "phpunit/php-invoker": "^4.0.0", "phpunit/php-text-template": "^3.0.1", "phpunit/php-timer": "^6.0.0", "sebastian/cli-parser": "^2.0.1", "sebastian/code-unit": "^2.0.0", "sebastian/comparator": "^5.0.3", "sebastian/diff": "^5.1.1", "sebastian/environment": "^6.1.0", "sebastian/exporter": "^5.1.2", "sebastian/global-state": "^6.0.2", "sebastian/object-enumerator": "^5.0.0", "sebastian/recursion-context": "^5.0.0", "sebastian/type": "^4.0.0", "sebastian/version": "^4.0.1"}, "suggest": {"ext-soap": "To be able to generate mocks based on WSDL files"}, "bin": ["phpunit"], "type": "library", "extra": {"branch-alias": {"dev-main": "10.5-dev"}}, "autoload": {"files": ["src/Framework/Assert/Functions.php"], "classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "The PHP Unit Testing framework.", "homepage": "https://phpunit.de/", "keywords": ["phpunit", "testing", "xunit"], "support": {"issues": "https://github.com/sebastian<PERSON>mann/phpunit/issues", "security": "https://github.com/sebastian<PERSON>mann/phpunit/security/policy", "source": "https://github.com/sebastian<PERSON>mann/phpunit/tree/10.5.45"}, "funding": [{"url": "https://phpunit.de/sponsors.html", "type": "custom"}, {"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/phpunit/phpunit", "type": "tidelift"}], "time": "2025-02-06T16:08:12+00:00"}, {"name": "revolt/event-loop", "version": "v1.0.7", "source": {"type": "git", "url": "https://github.com/revoltphp/event-loop.git", "reference": "09bf1bf7f7f574453efe43044b06fafe12216eb3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/revoltphp/event-loop/zipball/09bf1bf7f7f574453efe43044b06fafe12216eb3", "reference": "09bf1bf7f7f574453efe43044b06fafe12216eb3", "shasum": ""}, "require": {"php": ">=8.1"}, "require-dev": {"ext-json": "*", "jetbrains/phpstorm-stubs": "^2019.3", "phpunit/phpunit": "^9", "psalm/phar": "^5.15"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.x-dev"}}, "autoload": {"psr-4": {"Revolt\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Rock-solid event loop for concurrent PHP applications.", "keywords": ["async", "asynchronous", "concurrency", "event", "event-loop", "non-blocking", "scheduler"], "support": {"issues": "https://github.com/revoltphp/event-loop/issues", "source": "https://github.com/revoltphp/event-loop/tree/v1.0.7"}, "time": "2025-01-25T19:27:39+00:00"}, {"name": "sebastian/cli-parser", "version": "2.0.1", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/cli-parser.git", "reference": "c34583b87e7b7a8055bf6c450c2c77ce32a24084"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/cli-parser/zipball/c34583b87e7b7a8055bf6c450c2c77ce32a24084", "reference": "c34583b87e7b7a8055bf6c450c2c77ce32a24084", "shasum": ""}, "require": {"php": ">=8.1"}, "require-dev": {"phpunit/phpunit": "^10.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "2.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Library for parsing CLI options", "homepage": "https://github.com/sebastian<PERSON>mann/cli-parser", "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/cli-parser/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/cli-parser/security/policy", "source": "https://github.com/sebas<PERSON><PERSON>mann/cli-parser/tree/2.0.1"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2024-03-02T07:12:49+00:00"}, {"name": "sebastian/code-unit", "version": "2.0.0", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/code-unit.git", "reference": "a81fee9eef0b7a76af11d121767abc44c104e503"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/code-unit/zipball/a81fee9eef0b7a76af11d121767abc44c104e503", "reference": "a81fee9eef0b7a76af11d121767abc44c104e503", "shasum": ""}, "require": {"php": ">=8.1"}, "require-dev": {"phpunit/phpunit": "^10.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "2.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Collection of value objects that represent the PHP code units", "homepage": "https://github.com/sebastian<PERSON>mann/code-unit", "support": {"issues": "https://github.com/sebastian<PERSON>mann/code-unit/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/code-unit/tree/2.0.0"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2023-02-03T06:58:43+00:00"}, {"name": "sebastian/code-unit-reverse-lookup", "version": "3.0.0", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON><PERSON>/code-unit-reverse-lookup.git", "reference": "5e3a687f7d8ae33fb362c5c0743794bbb2420a1d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/code-unit-reverse-lookup/zipball/5e3a687f7d8ae33fb362c5c0743794bbb2420a1d", "reference": "5e3a687f7d8ae33fb362c5c0743794bbb2420a1d", "shasum": ""}, "require": {"php": ">=8.1"}, "require-dev": {"phpunit/phpunit": "^10.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "3.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Looks up which function or method a line of code belongs to", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/code-unit-reverse-lookup/", "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/code-unit-reverse-lookup/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/code-unit-reverse-lookup/tree/3.0.0"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2023-02-03T06:59:15+00:00"}, {"name": "sebastian/comparator", "version": "5.0.4", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/comparator.git", "reference": "e8e53097718d2b53cfb2aa859b06a41abf58c62e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/comparator/zipball/e8e53097718d2b53cfb2aa859b06a41abf58c62e", "reference": "e8e53097718d2b53cfb2aa859b06a41abf58c62e", "shasum": ""}, "require": {"ext-dom": "*", "ext-mbstring": "*", "php": ">=8.1", "sebastian/diff": "^5.0", "sebastian/exporter": "^5.0"}, "require-dev": {"phpunit/phpunit": "^10.5"}, "type": "library", "extra": {"branch-alias": {"dev-main": "5.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides the functionality to compare PHP values for equality", "homepage": "https://github.com/sebas<PERSON><PERSON><PERSON>/comparator", "keywords": ["comparator", "compare", "equality"], "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/comparator/issues", "security": "https://github.com/sebas<PERSON><PERSON><PERSON>/comparator/security/policy", "source": "https://github.com/sebastian<PERSON>mann/comparator/tree/5.0.4"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}, {"url": "https://liberapay.com/sebastian<PERSON>mann", "type": "liberapay"}, {"url": "https://thanks.dev/u/gh/sebas<PERSON><PERSON><PERSON>", "type": "thanks_dev"}, {"url": "https://tidelift.com/funding/github/packagist/sebastian/comparator", "type": "tidelift"}], "time": "2025-09-07T05:25:07+00:00"}, {"name": "sebastian/complexity", "version": "3.2.0", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/complexity.git", "reference": "68ff824baeae169ec9f2137158ee529584553799"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/complexity/zipball/68ff824baeae169ec9f2137158ee529584553799", "reference": "68ff824baeae169ec9f2137158ee529584553799", "shasum": ""}, "require": {"nikic/php-parser": "^4.18 || ^5.0", "php": ">=8.1"}, "require-dev": {"phpunit/phpunit": "^10.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "3.2-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Library for calculating the complexity of PHP code units", "homepage": "https://github.com/sebastian<PERSON>mann/complexity", "support": {"issues": "https://github.com/sebastian<PERSON>mann/complexity/issues", "security": "https://github.com/sebastian<PERSON>mann/complexity/security/policy", "source": "https://github.com/sebastian<PERSON>mann/complexity/tree/3.2.0"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2023-12-21T08:37:17+00:00"}, {"name": "sebastian/diff", "version": "5.1.1", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/diff.git", "reference": "c41e007b4b62af48218231d6c2275e4c9b975b2e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/diff/zipball/c41e007b4b62af48218231d6c2275e4c9b975b2e", "reference": "c41e007b4b62af48218231d6c2275e4c9b975b2e", "shasum": ""}, "require": {"php": ">=8.1"}, "require-dev": {"phpunit/phpunit": "^10.0", "symfony/process": "^6.4"}, "type": "library", "extra": {"branch-alias": {"dev-main": "5.1-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Diff implementation", "homepage": "https://github.com/sebastian<PERSON>mann/diff", "keywords": ["diff", "udiff", "unidiff", "unified diff"], "support": {"issues": "https://github.com/sebastian<PERSON>mann/diff/issues", "security": "https://github.com/sebastian<PERSON>mann/diff/security/policy", "source": "https://github.com/sebastian<PERSON>mann/diff/tree/5.1.1"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2024-03-02T07:15:17+00:00"}, {"name": "sebastian/environment", "version": "6.1.0", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/environment.git", "reference": "8074dbcd93529b357029f5cc5058fd3e43666984"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastianbergmann/environment/zipball/8074dbcd93529b357029f5cc5058fd3e43666984", "reference": "8074dbcd93529b357029f5cc5058fd3e43666984", "shasum": ""}, "require": {"php": ">=8.1"}, "require-dev": {"phpunit/phpunit": "^10.0"}, "suggest": {"ext-posix": "*"}, "type": "library", "extra": {"branch-alias": {"dev-main": "6.1-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides functionality to handle HHVM/PHP environments", "homepage": "https://github.com/sebastian<PERSON>mann/environment", "keywords": ["Xdebug", "environment", "hhvm"], "support": {"issues": "https://github.com/sebastian<PERSON>mann/environment/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/environment/security/policy", "source": "https://github.com/sebastian<PERSON>mann/environment/tree/6.1.0"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2024-03-23T08:47:14+00:00"}, {"name": "sebastian/exporter", "version": "5.1.2", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/exporter.git", "reference": "955288482d97c19a372d3f31006ab3f37da47adf"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/exporter/zipball/955288482d97c19a372d3f31006ab3f37da47adf", "reference": "955288482d97c19a372d3f31006ab3f37da47adf", "shasum": ""}, "require": {"ext-mbstring": "*", "php": ">=8.1", "sebastian/recursion-context": "^5.0"}, "require-dev": {"phpunit/phpunit": "^10.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "5.1-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "b<PERSON><PERSON><PERSON>@gmail.com"}], "description": "Provides the functionality to export PHP variables for visualization", "homepage": "https://www.github.com/sebastianbergmann/exporter", "keywords": ["export", "exporter"], "support": {"issues": "https://github.com/sebastian<PERSON>mann/exporter/issues", "security": "https://github.com/sebastian<PERSON>mann/exporter/security/policy", "source": "https://github.com/sebastian<PERSON>mann/exporter/tree/5.1.2"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2024-03-02T07:17:12+00:00"}, {"name": "sebastian/global-state", "version": "6.0.2", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/global-state.git", "reference": "987bafff24ecc4c9ac418cab1145b96dd6e9cbd9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/global-state/zipball/987bafff24ecc4c9ac418cab1145b96dd6e9cbd9", "reference": "987bafff24ecc4c9ac418cab1145b96dd6e9cbd9", "shasum": ""}, "require": {"php": ">=8.1", "sebastian/object-reflector": "^3.0", "sebastian/recursion-context": "^5.0"}, "require-dev": {"ext-dom": "*", "phpunit/phpunit": "^10.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "6.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Snapshotting of global state", "homepage": "https://www.github.com/sebastian<PERSON>mann/global-state", "keywords": ["global state"], "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/global-state/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/global-state/security/policy", "source": "https://github.com/sebastian<PERSON>mann/global-state/tree/6.0.2"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2024-03-02T07:19:19+00:00"}, {"name": "sebastian/lines-of-code", "version": "2.0.2", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/lines-of-code.git", "reference": "856e7f6a75a84e339195d48c556f23be2ebf75d0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/lines-of-code/zipball/856e7f6a75a84e339195d48c556f23be2ebf75d0", "reference": "856e7f6a75a84e339195d48c556f23be2ebf75d0", "shasum": ""}, "require": {"nikic/php-parser": "^4.18 || ^5.0", "php": ">=8.1"}, "require-dev": {"phpunit/phpunit": "^10.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "2.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Library for counting the lines of code in PHP source code", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/lines-of-code", "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/lines-of-code/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/lines-of-code/security/policy", "source": "https://github.com/sebas<PERSON><PERSON>mann/lines-of-code/tree/2.0.2"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2023-12-21T08:38:20+00:00"}, {"name": "sebastian/object-enumerator", "version": "5.0.0", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON><PERSON>/object-enumerator.git", "reference": "202d0e344a580d7f7d04b3fafce6933e59dae906"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/object-enumerator/zipball/202d0e344a580d7f7d04b3fafce6933e59dae906", "reference": "202d0e344a580d7f7d04b3fafce6933e59dae906", "shasum": ""}, "require": {"php": ">=8.1", "sebastian/object-reflector": "^3.0", "sebastian/recursion-context": "^5.0"}, "require-dev": {"phpunit/phpunit": "^10.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "5.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Traverses array structures and object graphs to enumerate all referenced objects", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/object-enumerator/", "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/object-enumerator/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/object-enumerator/tree/5.0.0"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2023-02-03T07:08:32+00:00"}, {"name": "sebastian/object-reflector", "version": "3.0.0", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/object-reflector.git", "reference": "24ed13d98130f0e7122df55d06c5c4942a577957"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/object-reflector/zipball/24ed13d98130f0e7122df55d06c5c4942a577957", "reference": "24ed13d98130f0e7122df55d06c5c4942a577957", "shasum": ""}, "require": {"php": ">=8.1"}, "require-dev": {"phpunit/phpunit": "^10.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "3.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Allows reflection of object attributes, including inherited and non-public ones", "homepage": "https://github.com/sebastian<PERSON>mann/object-reflector/", "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/object-reflector/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/object-reflector/tree/3.0.0"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2023-02-03T07:06:18+00:00"}, {"name": "sebastian/recursion-context", "version": "5.0.1", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context.git", "reference": "47e34210757a2f37a97dcd207d032e1b01e64c7a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/recursion-context/zipball/47e34210757a2f37a97dcd207d032e1b01e64c7a", "reference": "47e34210757a2f37a97dcd207d032e1b01e64c7a", "shasum": ""}, "require": {"php": ">=8.1"}, "require-dev": {"phpunit/phpunit": "^10.5"}, "type": "library", "extra": {"branch-alias": {"dev-main": "5.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides functionality to recursively process PHP variables", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context", "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context/security/policy", "source": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context/tree/5.0.1"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}, {"url": "https://liberapay.com/sebastian<PERSON>mann", "type": "liberapay"}, {"url": "https://thanks.dev/u/gh/sebas<PERSON><PERSON><PERSON>", "type": "thanks_dev"}, {"url": "https://tidelift.com/funding/github/packagist/sebastian/recursion-context", "type": "tidelift"}], "time": "2025-08-10T07:50:56+00:00"}, {"name": "sebastian/type", "version": "4.0.0", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/type.git", "reference": "462699a16464c3944eefc02ebdd77882bd3925bf"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/type/zipball/462699a16464c3944eefc02ebdd77882bd3925bf", "reference": "462699a16464c3944eefc02ebdd77882bd3925bf", "shasum": ""}, "require": {"php": ">=8.1"}, "require-dev": {"phpunit/phpunit": "^10.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "4.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Collection of value objects that represent the types of the PHP type system", "homepage": "https://github.com/sebastian<PERSON>mann/type", "support": {"issues": "https://github.com/sebastian<PERSON>mann/type/issues", "source": "https://github.com/sebastian<PERSON>mann/type/tree/4.0.0"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2023-02-03T07:10:45+00:00"}, {"name": "sebastian/version", "version": "4.0.1", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/version.git", "reference": "c51fa83a5d8f43f1402e3f32a005e6262244ef17"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/version/zipball/c51fa83a5d8f43f1402e3f32a005e6262244ef17", "reference": "c51fa83a5d8f43f1402e3f32a005e6262244ef17", "shasum": ""}, "require": {"php": ">=8.1"}, "type": "library", "extra": {"branch-alias": {"dev-main": "4.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Library that helps with managing the version number of Git-hosted PHP projects", "homepage": "https://github.com/sebastian<PERSON>mann/version", "support": {"issues": "https://github.com/sebastian<PERSON>mann/version/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/version/tree/4.0.1"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2023-02-07T11:34:05+00:00"}, {"name": "spatie/array-to-xml", "version": "3.4.0", "source": {"type": "git", "url": "https://github.com/spatie/array-to-xml.git", "reference": "7dcfc67d60b0272926dabad1ec01f6b8a5fb5e67"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/spatie/array-to-xml/zipball/7dcfc67d60b0272926dabad1ec01f6b8a5fb5e67", "reference": "7dcfc67d60b0272926dabad1ec01f6b8a5fb5e67", "shasum": ""}, "require": {"ext-dom": "*", "php": "^8.0"}, "require-dev": {"mockery/mockery": "^1.2", "pestphp/pest": "^1.21", "spatie/pest-plugin-snapshots": "^1.1"}, "type": "library", "extra": {"branch-alias": {"dev-main": "3.x-dev"}}, "autoload": {"psr-4": {"Spatie\\ArrayToXml\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://freek.dev", "role": "Developer"}], "description": "Convert an array to xml", "homepage": "https://github.com/spatie/array-to-xml", "keywords": ["array", "convert", "xml"], "support": {"source": "https://github.com/spatie/array-to-xml/tree/3.4.0"}, "funding": [{"url": "https://spatie.be/open-source/support-us", "type": "custom"}, {"url": "https://github.com/spatie", "type": "github"}], "time": "2024-12-16T12:45:15+00:00"}, {"name": "spiral-packages/database-seeder", "version": "3.3.0", "source": {"type": "git", "url": "https://github.com/spiral-packages/database-seeder.git", "reference": "c15c6254f19ef237c4500a7867604f9fe4300e6c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/spiral-packages/database-seeder/zipball/c15c6254f19ef237c4500a7867604f9fe4300e6c", "reference": "c15c6254f19ef237c4500a7867604f9fe4300e6c", "shasum": ""}, "require": {"butschster/entity-faker": "^2.0", "fakerphp/faker": "^1.23", "laminas/laminas-hydrator": "^4.3", "php": "^8.1", "spiral/attributes": "^2.0 || ^3.1.2", "spiral/boot": "^3.8", "spiral/console": "^3.8", "spiral/scaffolder": "^3.8"}, "require-dev": {"cycle/database": "^2.6", "mockery/mockery": "^1.6", "phpunit/phpunit": "^10.3", "spiral/cycle-bridge": "^2.5", "spiral/framework": "^3.8", "spiral/testing": "^2.6", "vimeo/psalm": "^5.15"}, "suggest": {"spiral/cycle-bridge": "For easy database and ORM configuration in a test application", "spiral/testing": "To use the Spiral\\DatabaseSeeder\\TestCase class and helpers to test an app with DB"}, "type": "library", "extra": {"spiral": {"bootloaders": ["Spiral\\DatabaseSeeder\\Bootloader\\DatabaseSeederBootloader"]}}, "autoload": {"psr-4": {"Spiral\\DatabaseSeeder\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON> (wolfy-j)", "email": "<EMAIL>"}, {"name": "<PERSON> (but<PERSON><PERSON>)", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON> (roxblnfk)", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON> (msmakouz)", "email": "<EMAIL>"}, {"name": "Spiral Community", "homepage": "https://github.com/spiral/framework/graphs/contributors"}], "description": "The package provides the ability to seed your database with data using seed classes", "homepage": "https://github.com/spiral-packages/database-seeder", "keywords": ["database-seeder", "seeder", "spiral", "spiral-packages"], "support": {"issues": "https://github.com/spiral-packages/database-seeder/issues", "source": "https://github.com/spiral-packages/database-seeder/tree/3.3.0"}, "time": "2024-10-17T06:33:14+00:00"}, {"name": "spiral/code-style", "version": "v2.3.0", "source": {"type": "git", "url": "https://github.com/spiral/code-style.git", "reference": "d9742a53dbd9ae815054ba5ffc89c8cf92a774fc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/spiral/code-style/zipball/d9742a53dbd9ae815054ba5ffc89c8cf92a774fc", "reference": "d9742a53dbd9ae815054ba5ffc89c8cf92a774fc", "shasum": ""}, "require": {"php": ">=8.0", "php-cs-fixer/shim": "^3.64"}, "require-dev": {"phpunit/phpunit": "^10.5", "spiral/dumper": "^3.3", "vimeo/psalm": "^5.26"}, "type": "library", "autoload": {"psr-4": {"Spiral\\CodeStyle\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "aleksandr.no<PERSON><PERSON>@spiralscout.com"}, {"name": "Alek<PERSON>", "email": "<EMAIL>"}], "description": "Code style and static analysis tools rulesets collection", "homepage": "https://github.com/spiral/code-style", "support": {"source": "https://github.com/spiral/code-style/tree/v2.3.0"}, "funding": [{"url": "https://github.com/sponsors/spiral", "type": "github"}], "time": "2025-08-28T07:10:02+00:00"}, {"name": "spiral/dumper", "version": "3.3.1", "source": {"type": "git", "url": "https://github.com/spiral/dumper.git", "reference": "4345f34dee53c7f455f248cf05972f289a4de6cc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/spiral/dumper/zipball/4345f34dee53c7f455f248cf05972f289a4de6cc", "reference": "4345f34dee53c7f455f248cf05972f289a4de6cc", "shasum": ""}, "require": {"buggregator/trap": "^1.8", "php": ">= 8.1", "spiral/boot": "^3.7", "symfony/var-dumper": "^6.1 || ^7.0"}, "require-dev": {"psr/http-factory": "^1.0", "psr/http-server-middleware": "^1.0", "vimeo/psalm": "^5.14"}, "type": "library", "autoload": {"files": ["src/functions.php"], "psr-4": {"Spiral\\Debug\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON> (wolfy-j)", "email": "<EMAIL>"}, {"name": "<PERSON> (but<PERSON><PERSON>)", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON> (roxblnfk)", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON> (msmakouz)", "email": "<EMAIL>"}], "description": "Dumper for PHP variables based on Symfony VarDumper for Spiral Framework and RoadRunner", "homepage": "https://spiral.dev", "support": {"chat": "https://discord.gg/V6EK4he", "docs": "https://spiral.dev/docs", "forum": "https://forum.spiral.dev", "issues": "https://github.com/spiral/framework/issues", "source": "https://github.com/spiral/dumper"}, "funding": [{"url": "https://github.com/sponsors/spiral", "type": "github"}], "time": "2024-08-22T10:27:20+00:00"}, {"name": "spiral/testing", "version": "2.11.0", "source": {"type": "git", "url": "https://github.com/spiral/testing.git", "reference": "be28b8f8fc064f079b1c6d06a1edc222d8b41aed"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/spiral/testing/zipball/be28b8f8fc064f079b1c6d06a1edc222d8b41aed", "reference": "be28b8f8fc064f079b1c6d06a1edc222d8b41aed", "shasum": ""}, "require": {"ext-json": "*", "mockery/mockery": "^1.6.12", "nyholm/psr7": "^1.8.2", "php": ">=8.1", "phpunit/phpunit": "^9.6 || 10.5.45", "spiral/auth": "^3.15", "spiral/auth-http": "^3.15", "spiral/boot": "^3.15", "spiral/console": "^3.15", "spiral/core": "^3.15", "spiral/events": "^3.15", "spiral/http": "^3.15", "spiral/mailer": "^3.15", "spiral/queue": "^3.15", "spiral/scaffolder": "^3.15", "spiral/security": "^3.15", "spiral/session": "^3.15", "spiral/storage": "^3.15", "spiral/tokenizer": "^3.15", "spiral/translator": "^3.15", "spiral/views": "^3.15", "symfony/mime": "^6.4.18 || ^7.2"}, "require-dev": {"spiral-packages/league-event": "^1.0.1", "spiral/code-style": "^2.2.2", "spiral/dumper": "^3.3.1", "spiral/framework": "^3.15.0", "spiral/nyholm-bridge": "^1.3", "spiral/roadrunner-bridge": "^2.2 || ^3.7 || ^4.0", "vimeo/psalm": "^5.26.1 || ^6.1"}, "suggest": {"brianium/paratest": "Required to run tests in parallel (^6.0).", "ext-gd": "Required to use generate fake image files"}, "type": "library", "autoload": {"psr-4": {"Spiral\\Testing\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON> (wolfy-j)", "email": "<EMAIL>"}, {"name": "<PERSON> (but<PERSON><PERSON>)", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON> (roxblnfk)", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON> (msmakouz)", "email": "<EMAIL>"}, {"name": "RoadRunner Community", "homepage": "https://github.com/spiral/roadrunner/graphs/contributors"}], "description": "Spiral Framework testing SDK", "homepage": "https://spiral.dev/", "keywords": ["spiral", "spiral-packages", "testing"], "support": {"chat": "https://discord.gg/TFeEmCs", "docs": "https://spiral.dev/docs/testing-start", "issues": "https://github.com/spiral/testing/issues", "source": "https://github.com/spiral/testing/tree/2.11.0"}, "funding": [{"url": "https://github.com/sponsors/spiral", "type": "github"}], "time": "2025-08-13T15:46:27+00:00"}, {"name": "symfony/var-dumper", "version": "v7.3.3", "source": {"type": "git", "url": "https://github.com/symfony/var-dumper.git", "reference": "34d8d4c4b9597347306d1ec8eb4e1319b1e6986f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/var-dumper/zipball/34d8d4c4b9597347306d1ec8eb4e1319b1e6986f", "reference": "34d8d4c4b9597347306d1ec8eb4e1319b1e6986f", "shasum": ""}, "require": {"php": ">=8.2", "symfony/deprecation-contracts": "^2.5|^3", "symfony/polyfill-mbstring": "~1.0"}, "conflict": {"symfony/console": "<6.4"}, "require-dev": {"symfony/console": "^6.4|^7.0", "symfony/http-kernel": "^6.4|^7.0", "symfony/process": "^6.4|^7.0", "symfony/uid": "^6.4|^7.0", "twig/twig": "^3.12"}, "bin": ["Resources/bin/var-dump-server"], "type": "library", "autoload": {"files": ["Resources/functions/dump.php"], "psr-4": {"Symfony\\Component\\VarDumper\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides mechanisms for walking through any arbitrary PHP variable", "homepage": "https://symfony.com", "keywords": ["debug", "dump"], "support": {"source": "https://github.com/symfony/var-dumper/tree/v7.3.3"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://github.com/nicolas-grekas", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-08-13T11:49:31+00:00"}, {"name": "theseer/tokenizer", "version": "1.2.3", "source": {"type": "git", "url": "https://github.com/theseer/tokenizer.git", "reference": "737eda637ed5e28c3413cb1ebe8bb52cbf1ca7a2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/theseer/tokenizer/zipball/737eda637ed5e28c3413cb1ebe8bb52cbf1ca7a2", "reference": "737eda637ed5e28c3413cb1ebe8bb52cbf1ca7a2", "shasum": ""}, "require": {"ext-dom": "*", "ext-tokenizer": "*", "ext-xmlwriter": "*", "php": "^7.2 || ^8.0"}, "type": "library", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "A small library for converting tokenized PHP source code into XML and potentially other formats", "support": {"issues": "https://github.com/theseer/tokenizer/issues", "source": "https://github.com/theseer/tokenizer/tree/1.2.3"}, "funding": [{"url": "https://github.com/theseer", "type": "github"}], "time": "2024-03-03T12:36:25+00:00"}, {"name": "vimeo/psalm", "version": "6.13.1", "source": {"type": "git", "url": "https://github.com/vimeo/psalm.git", "reference": "1e3b7f0a8ab32b23197b91107adc0a7ed8a05b51"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/vimeo/psalm/zipball/1e3b7f0a8ab32b23197b91107adc0a7ed8a05b51", "reference": "1e3b7f0a8ab32b23197b91107adc0a7ed8a05b51", "shasum": ""}, "require": {"amphp/amp": "^3", "amphp/byte-stream": "^2", "amphp/parallel": "^2.3", "composer-runtime-api": "^2", "composer/semver": "^1.4 || ^2.0 || ^3.0", "composer/xdebug-handler": "^2.0 || ^3.0", "danog/advanced-json-rpc": "^3.1", "dnoegel/php-xdg-base-dir": "^0.1.1", "ext-ctype": "*", "ext-dom": "*", "ext-json": "*", "ext-libxml": "*", "ext-mbstring": "*", "ext-simplexml": "*", "ext-tokenizer": "*", "felixfbecker/language-server-protocol": "^1.5.3", "fidry/cpu-core-counter": "^0.4.1 || ^0.5.1 || ^1.0.0", "netresearch/jsonmapper": "^5.0", "nikic/php-parser": "^5.0.0", "php": "~8.1.31 || ~8.2.27 || ~8.3.16 || ~8.4.3", "sebastian/diff": "^4.0 || ^5.0 || ^6.0 || ^7.0", "spatie/array-to-xml": "^2.17.0 || ^3.0", "symfony/console": "^6.0 || ^7.0", "symfony/filesystem": "~6.3.12 || ~6.4.3 || ^7.0.3", "symfony/polyfill-php84": "^1.31.0"}, "provide": {"psalm/psalm": "self.version"}, "require-dev": {"amphp/phpunit-util": "^3", "bamarni/composer-bin-plugin": "^1.4", "brianium/paratest": "^6.9", "danog/class-finder": "^0.4.8", "dg/bypass-finals": "^1.5", "ext-curl": "*", "mockery/mockery": "^1.5", "nunomaduro/mock-final-classes": "^1.1", "php-parallel-lint/php-parallel-lint": "^1.2", "phpstan/phpdoc-parser": "^1.6", "phpunit/phpunit": "^9.6", "psalm/plugin-mockery": "^1.1", "psalm/plugin-phpunit": "^0.19", "slevomat/coding-standard": "^8.4", "squizlabs/php_codesniffer": "^3.6", "symfony/process": "^6.0 || ^7.0"}, "suggest": {"ext-curl": "In order to send data to shepherd", "ext-igbinary": "^2.0.5 is required, used to serialize caching data"}, "bin": ["psalm", "psalm-language-server", "psalm-plugin", "psalm-refactor", "psalm-review", "psalter"], "type": "project", "extra": {"branch-alias": {"dev-1.x": "1.x-dev", "dev-2.x": "2.x-dev", "dev-3.x": "3.x-dev", "dev-4.x": "4.x-dev", "dev-5.x": "5.x-dev", "dev-6.x": "6.x-dev", "dev-master": "7.x-dev"}}, "autoload": {"psr-4": {"Psalm\\": "src/Psalm/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "A static analysis tool for finding errors in PHP applications", "keywords": ["code", "inspection", "php", "static analysis"], "support": {"docs": "https://psalm.dev/docs", "issues": "https://github.com/vimeo/psalm/issues", "source": "https://github.com/vimeo/psalm"}, "time": "2025-08-06T10:10:28+00:00"}], "aliases": [], "minimum-stability": "dev", "stability-flags": {}, "prefer-stable": true, "prefer-lowest": false, "platform": {"php": ">=8.1", "ext-mbstring": "*", "ext-sockets": "*"}, "platform-dev": {}, "plugin-api-version": "2.6.0"}